package quest

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableQuest = "quest"
const TableTrainerQuest = "trainer_quest"

// QuestCache 任务内存缓存
type QuestCache struct {
	quests map[string]*MainServer.QuestInfo // questId -> QuestInfo
	mutex  sync.RWMutex                     // 读写锁，保证并发安全
	logger runtime.Logger
}

// 全局任务缓存实例
var questCache *QuestCache

// InitQuestCache 初始化任务缓存
func InitQuestCache() {
	questCache = &QuestCache{
		quests: make(map[string]*MainServer.QuestInfo),
		// logger: logger,
	}
}

// GetQuestCache 获取任务缓存实例
func GetQuestCache() *QuestCache {
	return questCache
}
func addQuestsToCache(ctx context.Context, logger runtime.Logger, quests []*MainServer.QuestInfo) error {
	if questCache == nil {
		InitQuestCache()
	}
	questCache.mutex.Lock()
	defer questCache.mutex.Unlock()

	for _, quest := range quests {
		questCache.quests[quest.QuestId] = quest
	}

	return nil
}

// LoadAllQuestsToCache 从数据库加载所有任务到内存缓存
func LoadAllQuestsToCache(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	if questCache == nil {
		InitQuestCache()
	}

	// 从数据库查询所有任务
	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_info,
			   linear_quests, single_quest, current_quests, quest_strict, quest_reward_info,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_info, quest_repeat_limit_time, quest_cancel_info, version
		FROM %s
		ORDER BY quest_id
	`, TableQuest)

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		logger.Error("Failed to load quests from database: %v", err)
		return err
	}
	defer rows.Close()

	questCache.mutex.Lock()
	defer questCache.mutex.Unlock()

	// 清空现有缓存
	questCache.quests = make(map[string]*MainServer.QuestInfo)

	loadedCount := 0
	for rows.Next() {
		quest := &MainServer.QuestInfo{}
		var questUnlockInfoJSON, linearQuestsJSON, currentQuestsJSON, questStrictJSON, questRewardInfoJSON, questBroadcastJSON, questCompleteInfoJSON, questCancelInfoJSON []byte
		var questTypeInt, questLevelInt, questStatusInt int

		err := rows.Scan(
			&quest.QuestId, &questTypeInt, &questLevelInt, &questStatusInt, &questUnlockInfoJSON,
			&linearQuestsJSON, &quest.SingleQuest, &currentQuestsJSON, &questStrictJSON, &questRewardInfoJSON,
			&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
			&questBroadcastJSON, &questCompleteInfoJSON, &quest.QuestRepeatLimitTime, &questCancelInfoJSON, &quest.Version,
		)

		if err != nil {
			logger.Error("Failed to scan quest row: %v", err)
			continue
		}

		// 设置枚举类型
		quest.QuestType = MainServer.QuestType(questTypeInt)
		quest.QuestLevel = int32(questLevelInt)
		quest.QuestStatus = MainServer.QuestStatus(questStatusInt)

		// 反序列化JSONB字段
		json.Unmarshal(questUnlockInfoJSON, &quest.QuestUnlockInfo)
		json.Unmarshal(linearQuestsJSON, &quest.LinearQuests)
		json.Unmarshal(currentQuestsJSON, &quest.CurrentQuests)
		json.Unmarshal(questStrictJSON, &quest.QuestStrict)
		json.Unmarshal(questRewardInfoJSON, &quest.QuestRewardInfo)
		json.Unmarshal(questBroadcastJSON, &quest.QuestBroadcast)
		json.Unmarshal(questCompleteInfoJSON, &quest.QuestCompleteInfo)
		json.Unmarshal(questCancelInfoJSON, &quest.QuestCancelInfo)

		// 添加到缓存
		questCache.quests[quest.QuestId] = quest
		loadedCount++
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error iterating quest rows: %v", err)
		return err
	}

	logger.Info("Loaded %d quests to memory cache", loadedCount)
	return nil
}

// InitQuest 初始化任务系统
func InitQuest(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 创建任务表
	err := createQuestTable(ctx, logger, db)
	if err != nil {
		return err
	}

	// 创建训练师任务表
	// err = createTrainerQuestTable(ctx, logger, db)
	// if err != nil {
	// 	return err
	// }

	logger.Info("Quest system initialized successfully")
	return nil
}

// createQuestTable 创建任务表
func createQuestTable(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id SERIAL PRIMARY KEY,
			quest_id VCHAR(20) NOT NULL,
			quest_type INTEGER NOT NULL DEFAULT 0,
			quest_level INTEGER NOT NULL DEFAULT 1,
			quest_status INTEGER NOT NULL DEFAULT 0,
			quest_unlock_info JSONB DEFAULT '{}',
			linear_quests JSONB DEFAULT '{}',
			single_quest BOOLEAN NOT NULL DEFAULT true,
			current_quests JSONB DEFAULT '{}',
			quest_strict JSONB DEFAULT '{}',
			quest_reward_info JSONB DEFAULT '{}',
			quest_start_time BIGINT NOT NULL DEFAULT 0,
			quest_end_time BIGINT NOT NULL DEFAULT 0,
			quest_repeat_limit INTEGER NOT NULL DEFAULT 1,
			quest_repeat_interval INTEGER NOT NULL DEFAULT 0,
			quest_broadcast JSONB DEFAULT '{}',
			quest_complete_info JSONB DEFAULT '{}',
			quest_repeat_limit_time INTEGER NOT NULL DEFAULT 0,
			quest_cancel_info JSONB DEFAULT '{}',
			version INTEGER NOT NULL DEFAULT 1,
			create_ts BIGINT NOT NULL DEFAULT 0,
			update_ts BIGINT NOT NULL DEFAULT 0
		)
	`, TableQuest)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("Failed to create quest table: %v", err)
		return err
	}

	// 创建索引
	indexQueries := []string{
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_type ON %s (quest_type)", TableQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_status ON %s (quest_status)", TableQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_time ON %s (quest_start_time, quest_end_time)", TableQuest),
	}

	for _, indexQuery := range indexQueries {
		_, err = db.ExecContext(ctx, indexQuery)
		if err != nil {
			logger.Error("Failed to create quest index: %v", err)
			return err
		}
	}

	return nil
}

func UpsertQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, quest *MainServer.QuestInfo) error {
	nowTs := time.Now().Unix()

	// 序列化 JSONB 字段
	questUnlockInfo, _ := json.Marshal(quest.QuestUnlockInfo)
	linearQuests, _ := json.Marshal(quest.LinearQuests)
	currentQuests, _ := json.Marshal(quest.CurrentQuests)
	questStrict, _ := json.Marshal(quest.QuestStrict)
	questRewardInfo, _ := json.Marshal(quest.QuestRewardInfo)
	questBroadcast, _ := json.Marshal(quest.QuestBroadcast)
	questCancelInfo, _ := json.Marshal(quest.QuestCancelInfo)
	questCompleteInfo, _ := json.Marshal(quest.QuestCompleteInfo)
	// if trainer.Id < 0 {
	// 		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", tableName)).Scan(&trainer.Id)
	// 		if err != nil {
	// 			return 0, fmt.Errorf("failed to generate new id: %v", err)
	// 		}
	// 	}
	query := fmt.Sprintf(`
		INSERT INTO %s (
			quest_id, quest_type, quest_level, quest_status, quest_unlock_info,
			linear_quests, single_quest, current_quests, quest_strict, quest_reward_info,
			quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			quest_broadcast, quest_complete_info, quest_repeat_limit_time, quest_cancel_info, version,
			create_ts, update_ts
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
		          $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21)
		ON CONFLICT (quest_id) DO UPDATE SET
			quest_type = EXCLUDED.quest_type,
			quest_level = EXCLUDED.quest_level,
			quest_status = EXCLUDED.quest_status,
			quest_unlock_info = EXCLUDED.quest_unlock_info,
			linear_quests = EXCLUDED.linear_quests,
			single_quest = EXCLUDED.single_quest,
			current_quests = EXCLUDED.current_quests,
			quest_strict = EXCLUDED.quest_strict,
			quest_reward_info = EXCLUDED.quest_reward_info,
			quest_start_time = EXCLUDED.quest_start_time,
			quest_end_time = EXCLUDED.quest_end_time,
			quest_repeat_limit = EXCLUDED.quest_repeat_limit,
			quest_repeat_interval = EXCLUDED.quest_repeat_interval,
			quest_broadcast = EXCLUDED.quest_broadcast,
			quest_complete_info = EXCLUDED.quest_complete_info,
			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
			quest_cancel_info = EXCLUDED.quest_cancel_info,
			version = EXCLUDED.version,
			update_ts = EXCLUDED.update_ts
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query,
		quest.QuestId, quest.QuestType, quest.QuestLevel, quest.QuestStatus, questUnlockInfo,
		linearQuests, quest.SingleQuest, currentQuests, questStrict, questRewardInfo,
		quest.QuestStartTime, quest.QuestEndTime, quest.QuestRepeatLimit, quest.QuestRepeatInterval,
		questBroadcast, questCompleteInfo, quest.QuestRepeatLimitTime, questCancelInfo, quest.Version,
		nowTs, nowTs,
	)
	if err != nil {
		logger.Error("Failed to upsert quest: %v", err)
		return err
	}

	// if updateCache && questCache != nil {
	// 	questCache.Set(quest)
	// 	logger.Debug("Updated quest %d in cache after upsert", quest.QuestId)
	// }

	return nil
}

// func UpdateQuestInfo(ctx context.Context, logger runtime.Logger, tx *sql.Tx, quest *MainServer.QuestInfo, updateCache bool) error {
// 	// 更新数据库
// 	// err := UpdateQuest(ctx, logger, tx, quest)
// 	// if err != nil {
// 	// 	return fmt.Errorf("failed to update quest in database: %v", err)
// 	// }

// 	// 更新缓存
// 	if updateCache {
// 		questCache.Set(quest)
// 	}
// 	return nil
// }
// // AddQuest 添加任务到数据库
// func AddQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, quest *MainServer.QuestInfo) error {
// 	nowTs := time.Now().Unix()

// 	// 序列化JSONB字段
// 	questUnlockInfo, _ := json.Marshal(quest.QuestUnlockInfo)
// 	linearQuests, _ := json.Marshal(quest.LinearQuests)
// 	currentQuests, _ := json.Marshal(quest.CurrentQuests)
// 	questStrict, _ := json.Marshal(quest.QuestStrict)
// 	questRewardInfo, _ := json.Marshal(quest.QuestRewardInfo)
// 	questBroadcast, _ := json.Marshal(quest.QuestBroadcast)
// 	questCompleteInfo, _ := json.Marshal(quest.QuestCompleteInfo)

// 	query := fmt.Sprintf(`
// 		INSERT INTO %s (
// 			quest_id, quest_type, quest_level, quest_status, quest_unlock_info,
// 			linear_quests, single_quest, current_quests, quest_strict, quest_reward_info,
// 			quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
// 			quest_broadcast, quest_complete_info, quest_repeat_limit_time, version,
// 			create_ts, update_ts
// 		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
// 		ON CONFLICT (quest_id) DO UPDATE SET
// 			quest_type = EXCLUDED.quest_type,
// 			quest_level = EXCLUDED.quest_level,
// 			quest_status = EXCLUDED.quest_status,
// 			quest_unlock_info = EXCLUDED.quest_unlock_info,
// 			linear_quests = EXCLUDED.linear_quests,
// 			single_quest = EXCLUDED.single_quest,
// 			current_quests = EXCLUDED.current_quests,
// 			quest_strict = EXCLUDED.quest_strict,
// 			quest_reward_info = EXCLUDED.quest_reward_info,
// 			quest_start_time = EXCLUDED.quest_start_time,
// 			quest_end_time = EXCLUDED.quest_end_time,
// 			quest_repeat_limit = EXCLUDED.quest_repeat_limit,
// 			quest_repeat_interval = EXCLUDED.quest_repeat_interval,
// 			quest_broadcast = EXCLUDED.quest_broadcast,
// 			quest_complete_info = EXCLUDED.quest_complete_info,
// 			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
// 			version = EXCLUDED.version,
// 			update_ts = $20
// 	`, TableQuest)

// 	_, err := tx.ExecContext(ctx, query,
// 		quest.QuestId, quest.QuestType, quest.QuestLevel, quest.QuestStatus, questUnlockInfo,
// 		linearQuests, quest.SingleQuest, currentQuests, questStrict, questRewardInfo,
// 		quest.QuestStartTime, quest.QuestEndTime, quest.QuestRepeatLimit, quest.QuestRepeatInterval,
// 		questBroadcast, questCompleteInfo, quest.QuestRepeatLimitTime, quest.Version,
// 		nowTs, nowTs,
// 	)

// 	if err != nil {
// 		logger.Error("Failed to add quest: %v", err)
// 		return err
// 	}

// 	// 更新缓存
// 	if questCache != nil {
// 		questCache.Set(quest)
// 		logger.Debug("Updated quest %d in cache after database operation", quest.QuestId)
// 	}

//		return nil
//	}
func GetAndSimpleCheckLocalQuestById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId string) (*MainServer.QuestInfo, error) {
	questInfo, err := GetQuestById(ctx, logger, tx, questId)
	if err != nil {
		return nil, fmt.Errorf("failed to get quest by id: %v", err)
	}
	if questInfo.QuestStatus != MainServer.QuestStatus_QuestStatus_open {
		return nil, fmt.Errorf("quest %d is not open", questId)
	}
	nowTs := time.Now().Unix()
	if questInfo.QuestStartTime > 0 && questInfo.QuestStartTime > nowTs {
		return nil, fmt.Errorf("quest %d has not started yet", questId)
	}
	if questInfo.QuestEndTime > 0 && questInfo.QuestEndTime < nowTs {
		return nil, fmt.Errorf("quest %d has already ended", questId)
	}
	return questInfo, nil
}
func GetLocalTeamQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, teamLevel int32, count int32) ([]*MainServer.QuestInfo, error) {
	if questCache != nil {
		allQuests := questCache.GetAll()
		var quests []*MainServer.QuestInfo
		for _, quest := range allQuests {
			// if quest.QuestType == MainServer.QuestType_QuestType_team && quest.QuestLevel == teamLevel {
			// 	quests = append(quests, quest)
			// }
			//count 如果是5的倍数则有10%概率获得奖励任务 （普通玩家10个任务，月卡玩家20个任务，sp月卡玩家25个任务）
			//测试奖励任务
			if quest.QuestType == MainServer.QuestType_QuestType_team_reward {
				quests = append(quests, quest)
			}
		}
		return quests, nil
	}
	return nil, fmt.Errorf("quest cache is not available")
}

// GetQuestById 根据ID获取任务信息（优先从缓存获取）
func GetQuestById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId string) (*MainServer.QuestInfo, error) {
	// 优先从缓存获取
	if questCache != nil {
		if quest, exists := questCache.Get(questId); exists {
			return quest, nil
		}
	}
	quest, err := getQuestByIdFromDB(ctx, logger, tx, questId)
	if quest == nil {
		return nil, fmt.Errorf("quest with id %d does not exist", questId)
	}
	// 缓存中没有，从数据库获取
	return quest, err
}

// getQuestByIdFromDB 从数据库获取任务信息（内部函数）
func getQuestByIdFromDB(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId string) (*MainServer.QuestInfo, error) {
	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_info,
			   linear_quests, single_quest, current_quests, quest_strict, quest_reward_info,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_info, quest_repeat_limit_time, quest_cancel_info, version
		FROM %s WHERE quest_id = $1
	`, TableQuest)

	quest := &MainServer.QuestInfo{}
	var questUnlockInfoJSON, linearQuestsJSON, currentQuestsJSON, questStrictJSON, questRewardInfoJSON, questBroadcastJSON, questCompleteInfoJSON, questCancelInfoJSON []byte
	var questType, questLevel, questStatus int

	err := tx.QueryRowContext(ctx, query, questId).Scan(
		&quest.QuestId, &questType, &questLevel, &questStatus, &questUnlockInfoJSON,
		&linearQuestsJSON, &quest.SingleQuest, &currentQuestsJSON, &questStrictJSON, &questRewardInfoJSON,
		&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
		&questBroadcastJSON, &questCompleteInfoJSON, &quest.QuestRepeatLimitTime, &questCancelInfoJSON, &quest.Version,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("quest not found")
		}
		logger.Error("Failed to get quest: %v", err)
		return nil, err
	}

	// 转换枚举类型
	quest.QuestType = MainServer.QuestType(questType)
	quest.QuestLevel = int32(questLevel)
	quest.QuestStatus = MainServer.QuestStatus(questStatus)

	// 反序列化JSONB字段
	json.Unmarshal(questUnlockInfoJSON, &quest.QuestUnlockInfo)
	json.Unmarshal(linearQuestsJSON, &quest.LinearQuests)
	json.Unmarshal(currentQuestsJSON, &quest.CurrentQuests)
	json.Unmarshal(questStrictJSON, &quest.QuestStrict)
	json.Unmarshal(questRewardInfoJSON, &quest.QuestRewardInfo)
	json.Unmarshal(questBroadcastJSON, &quest.QuestBroadcast)
	json.Unmarshal(questCompleteInfoJSON, &quest.QuestCompleteInfo)
	json.Unmarshal(questCancelInfoJSON, &quest.QuestCancelInfo)

	return quest, nil
}

// UpdateQuestStatus 更新任务状态
func UpdateQuestStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId string, status MainServer.QuestStatus) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_status = $1, update_ts = $2 WHERE quest_id = $3
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query, int(status), nowTs, questId)
	if err != nil {
		logger.Error("Failed to update quest status: %v", err)
		return err
	}

	return nil
}

// GetAvailableQuests 获取可用的任务列表（优先从缓存获取）
func GetAvailableQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questType MainServer.QuestType) ([]*MainServer.QuestInfo, error) {
	// 优先从缓存获取
	if questCache != nil {
		return questCache.GetAvailableQuests(questType), nil
	}

	// 缓存不可用，从数据库获取
	return getAvailableQuestsFromDB(ctx, logger, tx, questType)
}

// getAvailableQuestsFromDB 从数据库获取可用任务列表（内部函数）
func getAvailableQuestsFromDB(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questType MainServer.QuestType) ([]*MainServer.QuestInfo, error) {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_info,
			   linear_quests, single_quest, current_quests, quest_strict, quest_reward_info,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_info, quest_repeat_limit_time, quest_cancel_info, version
		FROM %s
		WHERE quest_status = $1
		AND (quest_start_time = 0 OR quest_start_time <= $2)
		AND (quest_end_time = 0 OR quest_end_time >= $2)
		AND ($3 = -1 OR quest_type = $3)
		ORDER BY quest_level, quest_id
	`, TableQuest)

	var questTypeFilter int = -1
	if questType != MainServer.QuestType_QuestType_once {
		questTypeFilter = int(questType)
	}

	rows, err := tx.QueryContext(ctx, query, int(MainServer.QuestStatus_QuestStatus_open), nowTs, questTypeFilter)
	if err != nil {
		logger.Error("Failed to get available quests: %v", err)
		return nil, err
	}
	defer rows.Close()

	var quests []*MainServer.QuestInfo
	for rows.Next() {
		quest := &MainServer.QuestInfo{}
		var questUnlockInfoJSON, linearQuestsJSON, currentQuestsJSON, questStrictJSON, questRewardInfoJSON, questBroadcastJSON, questCompleteInfoJSON, questCancelInfoJSON []byte
		var questTypeInt, questLevelInt, questStatusInt int

		err := rows.Scan(
			&quest.QuestId, &questTypeInt, &questLevelInt, &questStatusInt, &questUnlockInfoJSON,
			&linearQuestsJSON, &quest.SingleQuest, &currentQuestsJSON, &questStrictJSON, &questRewardInfoJSON,
			&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
			&questBroadcastJSON, &questCompleteInfoJSON, &quest.QuestRepeatLimitTime, &questCancelInfoJSON, &quest.Version,
		)
		if err != nil {
			logger.Error("Failed to scan quest: %v", err)
			continue
		}

		// 转换枚举类型
		quest.QuestType = MainServer.QuestType(questTypeInt)
		quest.QuestLevel = int32(questLevelInt)
		quest.QuestStatus = MainServer.QuestStatus(questStatusInt)

		// 反序列化JSONB字段
		json.Unmarshal(questUnlockInfoJSON, &quest.QuestUnlockInfo)
		json.Unmarshal(linearQuestsJSON, &quest.LinearQuests)
		json.Unmarshal(currentQuestsJSON, &quest.CurrentQuests)
		json.Unmarshal(questStrictJSON, &quest.QuestStrict)
		json.Unmarshal(questRewardInfoJSON, &quest.QuestRewardInfo)
		json.Unmarshal(questBroadcastJSON, &quest.QuestBroadcast)
		json.Unmarshal(questCompleteInfoJSON, &quest.QuestCompleteInfo)
		json.Unmarshal(questCancelInfoJSON, &quest.QuestCancelInfo)

		quests = append(quests, quest)
	}

	return quests, nil
}

// QuestCache 方法实现

// Get 从缓存获取任务信息
func (qc *QuestCache) Get(questId string) (*MainServer.QuestInfo, bool) {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()

	quest, exists := qc.quests[questId]
	if !exists {
		return nil, false
	}

	// 返回副本，避免外部修改影响缓存
	return quest, true
}

// GetAll 获取所有任务信息
func (qc *QuestCache) GetAll() map[string]*MainServer.QuestInfo {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()

	result := make(map[string]*MainServer.QuestInfo)
	for id, quest := range qc.quests {
		result[id] = quest
	}
	return result
}

// GetByType 根据任务类型获取任务列表
func (qc *QuestCache) GetByType(questType MainServer.QuestType) []*MainServer.QuestInfo {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()

	var result []*MainServer.QuestInfo
	for _, quest := range qc.quests {
		if quest.QuestType == questType {
			result = append(result, quest)
		}
	}
	return result
}

// GetAvailableQuests 获取可用的任务列表（考虑时间和状态）
func (qc *QuestCache) GetAvailableQuests(questType MainServer.QuestType) []*MainServer.QuestInfo {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()

	nowTs := time.Now().Unix()
	var result []*MainServer.QuestInfo

	for _, quest := range qc.quests {
		// 检查任务状态
		if quest.QuestStatus != MainServer.QuestStatus_QuestStatus_open {
			continue
		}

		// 检查任务类型
		if questType != -1 && quest.QuestType != questType {
			continue
		}

		// 检查时间范围
		if quest.QuestStartTime > 0 && nowTs < quest.QuestStartTime {
			continue
		}
		if quest.QuestEndTime > 0 && nowTs > quest.QuestEndTime {
			continue
		}

		result = append(result, quest)
	}

	return result
}

// Set 设置任务信息到缓存
func (qc *QuestCache) Set(quest *MainServer.QuestInfo) {
	qc.mutex.Lock()
	defer qc.mutex.Unlock()

	qc.quests[quest.QuestId] = quest
	qc.logger.Debug("Updated quest %d in cache", quest.QuestId)
}

// Delete 从缓存删除任务
func (qc *QuestCache) Delete(questId string) {
	qc.mutex.Lock()
	defer qc.mutex.Unlock()

	delete(qc.quests, questId)
	qc.logger.Debug("Deleted quest %d from cache", questId)
}

// Count 获取缓存中任务的数量
func (qc *QuestCache) Count() int {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()

	return len(qc.quests)
}

// 全局缓存管理函数

// UpdateQuestInCache 更新任务到缓存（同时更新数据库）
func UpdateQuestInCache(ctx context.Context, logger runtime.Logger, db *sql.DB, quest *MainServer.QuestInfo) error {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 更新数据库
	err = UpsertQuest(ctx, logger, tx, quest)
	if err != nil {
		return fmt.Errorf("failed to update quest in database: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	// 缓存已在AddQuest中更新
	return nil
}

// DeleteQuestFromCache 从缓存和数据库删除任务
func DeleteQuestFromCache(ctx context.Context, logger runtime.Logger, db *sql.DB, questId string) error {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 从数据库删除
	query := fmt.Sprintf("DELETE FROM %s WHERE quest_id = $1", TableQuest)
	result, err := tx.ExecContext(ctx, query, questId)
	if err != nil {
		return fmt.Errorf("failed to delete quest from database: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected rows: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("quest %d not found", questId)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	// 从缓存删除
	if questCache != nil {
		questCache.Delete(questId)
		logger.Info("Deleted quest %d from cache and database", questId)
	}

	return nil
}

// ReloadQuestCache 重新加载整个任务缓存
func ReloadQuestCache(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	return LoadAllQuestsToCache(ctx, logger, db)
}

// GetQuestCacheStats 获取缓存统计信息
func GetQuestCacheStats() map[string]interface{} {
	if questCache == nil {
		return map[string]interface{}{
			"initialized": false,
		}
	}

	stats := map[string]interface{}{
		"initialized": true,
		"total_count": questCache.Count(),
	}

	// 按类型统计
	typeStats := make(map[string]int)
	allQuests := questCache.GetAll()
	for _, quest := range allQuests {
		typeName := quest.QuestType.String()
		typeStats[typeName]++
	}
	stats["by_type"] = typeStats

	return stats
}
