package poke

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

//不允许用户查询poke //只能通过box查询 //可以查询购买的

// QueryPokesByUpdateTs - 根据 updateTs 查询 Pokes，返回 Base64 编码的 Protobuf 数据
// func RpcQueryPokesByUpdateTs(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	admin, _ := user.IsAdmin(ctx, nk, userID)
// 	// tid := int64(0)
// 	if admin {
// 		userID = ""
// 	} else {
// 		trainer := tool.GetActiveTrainer(ctx)
// 		if trainer == nil {
// 			userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 			logger.Error("未找到用户的 active tid %s", userID)
// 			return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 		}
// 		tid = trainer.Id
// 	}
// 	// userID := "30"
// 	// return "{}", nil
// 	// 将 payload 转换为 updateTs（int64 类型）
// 	updateTs, err := strconv.ParseInt(payload, 10, 64)
// 	if err != nil {
// 		updateTs = 0
// 		// logger.Error("Failed to parse updateTs: %v", err)
// 		// return "", fmt.Errorf("failed to parse updateTs: %w", err)
// 	}

// 	// 调用查询函数（无 ID 的查询）
// 	pokes, err := queryPokesByIdsAndUpdateTs(ctx, db, nil, float64(updateTs))
// 	if err != nil {
// 		logger.Error("Failed to query pokes by updateTs: %v", err)
// 		return "", err
// 	}
// 	// 使用抽取的函数，将查询到的 Pokes 转为 Base64 编码字符串
// 	return pokesToBase64(pokes, logger)
// }

// QueryPokesByIds - 根据多个 PokeId 查询并返回 Base64 编码的 Protobuf 数据
// func RpcQueryPokesByIds(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	trainer := tool.GetActiveTrainer(ctx)
// 	if trainer == nil {
// 		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 		logger.Error("未找到用户的 active tid %s", userID)
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}

// 	// 将字符串 payload 转换为 []int64
// 	strPids := strings.Split(payload, ",")
// 	intPids := make([]int64, 0, len(strPids))

// 	// 遍历并转换每个字符串到 int64
// 	for _, str := range strPids {
// 		if pid, err := strconv.ParseInt(str, 10, 64); err == nil {
// 			intPids = append(intPids, pid)
// 		} else {
// 			logger.Error("Failed to convert poke ID to int64: %v", err)
// 			return "", fmt.Errorf("failed to convert poke ID to int64: %w", err)
// 		}
// 	}

// 	// 调用查询函数
// 	pokes, err := queryPokesByIdsAndUpdateTs(ctx, db, intPids, 0)
// 	if err != nil {
// 		logger.Error("Failed to query pokes: %v", err)
// 		return "", err
// 	}

//		// 使用抽取的函数，将查询到的 Pokes 转为 Base64 编码字符串
//		return pokesToBase64(pokes, logger)
//	}
func RpcQueryPokesByFilter(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var filter = &MainServer.PokeFilter{}
	if err := tool.Base64ToProto(payload, filter); err != nil {
		logger.Error("解析查询 filter 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	if filter.PageSize == 0 {
		filter.PageSize = 20
	}
	//test
	// var filter = &MainServer.PokeFilter{
	// 	Sale: true,
	// }
	// userID := ""
	// if filter.Owner || !filter.Sale {
	// 	userID = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// }
	tid := int64(-1)
	if filter.Owner {
		trainer := tool.GetActiveTrainerByCtx(ctx)
		if trainer == nil {
			return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
		}
		tid = trainer.Id
	} else {
		//只能查找售卖的和自己的
		if !(filter.TransactionType == MainServer.TransactionType_TransactionType_Market_Rent ||
			filter.TransactionType == MainServer.TransactionType_TransactionType_Market_Sale) {
			trainer := tool.GetActiveTrainerByCtx(ctx)
			if trainer == nil {
				return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
			}
			tid = trainer.Id
		}
	}
	if filter.TransactionType == MainServer.TransactionType_TransactionType_Market_Rent ||
		filter.TransactionType == MainServer.TransactionType_TransactionType_Market_Sale {
		filter.UpdateTs = time.Now().UnixMilli() - 14*24*60*60*1000 //最近14天的数据
	}
	pokes, err := queryPokesByPokeFilter(ctx, db, tid, filter)
	if err != nil {
		return "", err
	}
	result := &MainServer.PokesResult{
		Pokes: pokes,
		Ts:    time.Now().UnixMilli(),
		Page:  filter.Page,
	}
	return tool.ProtoToBase64(result)
}

func RpcTestQueryPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	tid := int64(-1)
	var pids []int64
	parts := strings.Split(payload, ",")
	for _, part := range parts {
		if part == "" {
			continue // 跳过空字符串
		}
		num, err := strconv.ParseInt(part, 10, 64)
		if err != nil {
			fmt.Printf("Error converting '%s' to int64: %v\n", part, err)
			continue // 跳过解析错误的部分
		}
		pids = append(pids, num)
	}
	pokes, err := QueryPokesByIdsAndUpdateTs(ctx, db, tid, pids, 0)
	if err != nil {
		return "", err
	}
	// 创建一个 map，记录每个 id 在 pids 中的位置
	pidIndexMap := make(map[int64]int)
	for index, pid := range pids {
		pidIndexMap[pid] = index
	}

	// 使用 pidIndexMap 对 pokes 排序
	sort.SliceStable(pokes, func(i, j int) bool {
		idI := pokes[i].Id // 假设 Poke 结构体中有 Id 字段
		idJ := pokes[j].Id
		return pidIndexMap[idI] < pidIndexMap[idJ]
	})
	// result := &MainServer.PokesResult{
	// 	Pokes: pokes,
	// 	Ts:    time.Now().UnixMilli(),
	// 	Page:  filter.Page,
	// }
	//  ConvertPokesToPackedFormat(pokes)
	return "{\"text\":\"" + ConvertPokesToPackedFormat(pokes) + "\" }", nil
}

// RpcUpdatePokeItem 更新宝可梦的携带物品
// 参数：
// - ctx: 上下文
// - logger: 日志记录器
// - db: 数据库连接
// - nk: Nakama模块
// - payload: 请求参数，包含以下字段：
//   - pid: 宝可梦ID
//   - item: 物品名称（可选，如果不提供则只移除当前物品）
//   - destroy: 是否销毁物品（可选，默认为false）
//
// 返回：
// - string: 空字符串
// - error: 错误信息
func RpcUpdatePokeItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前激活的训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	request := &MainServer.RpcUpdatePokeItemRequest{}
	err := tool.Base64ToProto(payload, request)
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务

	// 获取宝可梦信息
	pokemon := QueryPokeById(ctx, tx, trainer.Id, request.Pid)
	if pokemon == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}

	// 标志是否需要更新
	needsUpdate := false

	// 从 payload 获取新的物品
	if request.Item != "" {
		err := AddItemToPoke(ctx, tx, pokemon, request.Item, trainer.Id)
		if err != nil {
			logger.Error("添加物品到宝可梦失败: %v", err)
			return "", fmt.Errorf("failed to add item to Pokemon: %w", err)
		}
		needsUpdate = true
	}

	// 如果需要更新宝可梦数据，提交事务
	if needsUpdate {
		// 提交事务
		if err := tx.Commit(); err != nil {
			logger.Error("事务提交失败: %v", err)
			return "", fmt.Errorf("failed to commit transaction: %w", err)
		}
	}

	return "道具更新成功", nil
}
func RpcPokeInfos(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前激活的训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	request := &MainServer.RpcPokeInfoRequest{}
	err := tool.Base64ToProto(payload, request)
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务

	// 获取宝可梦信息
	pokemons, err := QueryPokesByIdsAndUpdateTs(ctx, tx, trainer.Id, request.PokeIds, 0)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}

	// 构建响应结果
	result := &MainServer.RpcPokeInfoResponse{
		Pokes: pokemons,
	}

	return tool.ProtoToBase64(result)
}
