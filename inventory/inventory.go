package inventory

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableInventoryName = "inventory"
)

// 初始化库存表
func InitInventory(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createInventoryTableIfNotExists(ctx, logger, db)
}

// 创建库存表（如果不存在）
func createInventoryTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            item_name VARCHAR(50) NOT NULL,
            quantity INT NOT NULL DEFAULT 0,
            price INT NOT NULL DEFAULT 0,
            team_coin INT NOT NULL DEFAULT 0,
			team_type INT NOT NULL DEFAULT 0,
            status INT NOT NULL DEFAULT 0,
            item_sale_type INT NOT NULL DEFAULT 0,
            inventory_type INT NOT NULL DEFAULT 0,
            summon_poke_name_id VARCHAR(50) NOT NULL DEFAULT '',
			seal_poke_id BIGINT NOT NULL DEFAULT 0,
            seal_info JSONB NOT NULL DEFAULT '{}',
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL,
            UNIQUE(tid, item_name, team_type, summon_poke_name_id, status, item_sale_type, seal_poke_id, inventory_type)
        );
        CREATE INDEX IF NOT EXISTS idx_inventory_id ON %s (id);
        CREATE INDEX IF NOT EXISTS idx_inventory_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON %s (item_name);
        CREATE INDEX IF NOT EXISTS idx_inventory_status ON %s (status);
        CREATE INDEX IF NOT EXISTS idx_inventory_price ON %s (price);
        CREATE INDEX IF NOT EXISTS idx_inventory_team_coin ON %s (team_coin);
        CREATE INDEX IF NOT EXISTS idx_inventory_team_type ON %s (team_type);
        CREATE INDEX IF NOT EXISTS idx_inventory_item_sale_type ON %s (item_sale_type);
        CREATE INDEX IF NOT EXISTS idx_inventory_inventory_type ON %s (inventory_type);
        CREATE INDEX IF NOT EXISTS idx_inventory_summon_poke_name_id ON %s (summon_poke_name_id);
        CREATE INDEX IF NOT EXISTS idx_inventory_seal_poke_id ON %s (seal_poke_id);
    `, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建库存表失败: %v", err)
		return
	}

	logger.Info("库存表初始化完成")
}

// InsertItem 插入新的道具记录
func InsertItem(ctx context.Context, tx *sql.Tx, inventory *MainServer.Inventory) error {
	// 序列化 seal_info
	sealInfoBytes, err := json.Marshal(inventory.SealInfo)
	if err != nil {
		return fmt.Errorf("序列化封印信息失败: %w", err)
	}

	now := time.Now().Unix()
	if inventory.CreateTs == 0 {
		inventory.CreateTs = now
	}
	inventory.UpdateTs = now

	query := fmt.Sprintf(`
		INSERT INTO %s (tid, item_name, quantity, price, team_coin, team_type, status, item_sale_type, inventory_type, summon_poke_name_id, seal_poke_id, seal_info, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id
	`, TableInventoryName)

	err = tx.QueryRowContext(ctx, query,
		inventory.Tid,
		inventory.ItemName,
		inventory.Quantity,
		inventory.Price,
		inventory.TeamCoin,
		inventory.TeamType,
		int(inventory.Status),
		int(inventory.ItemSaleType),
		int(inventory.InventoryType),
		inventory.SummonPokeNameId,
		inventory.SealPokeId,
		string(sealInfoBytes),
		inventory.CreateTs,
		inventory.UpdateTs,
	).Scan(&inventory.Id)

	if err != nil {
		return fmt.Errorf("插入道具失败: %w", err)
	}

	return nil
}

// UpdateItemFields 更新道具的指定字段，使用update_ts作为乐观锁
type UpdateFields struct {
	Quantity *int32
	Price    *int32
	TeamCoin *int32
	Status   *MainServer.InventoryStatus
	SealInfo *MainServer.InventorySealInfo
}

func UpdateItemFields(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64, fields UpdateFields) error {
	setParts := []string{}
	args := []interface{}{}
	paramIndex := 1

	currentTime := time.Now().Unix()

	// 动态构建SET子句
	if fields.Quantity != nil {
		setParts = append(setParts, fmt.Sprintf("quantity = $%d", paramIndex))
		args = append(args, *fields.Quantity)
		paramIndex++
	}

	if fields.Price != nil {
		setParts = append(setParts, fmt.Sprintf("price = $%d", paramIndex))
		args = append(args, *fields.Price)
		paramIndex++
	}

	if fields.TeamCoin != nil {
		setParts = append(setParts, fmt.Sprintf("team_coin = $%d", paramIndex))
		args = append(args, *fields.TeamCoin)
		paramIndex++
	}

	if fields.Status != nil {
		setParts = append(setParts, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(*fields.Status))
		paramIndex++
	}

	if fields.SealInfo != nil {
		sealInfoBytes, err := json.Marshal(fields.SealInfo)
		if err != nil {
			return fmt.Errorf("序列化封印信息失败: %w", err)
		}
		setParts = append(setParts, fmt.Sprintf("seal_info = $%d", paramIndex))
		args = append(args, string(sealInfoBytes))
		paramIndex++
	}

	if len(setParts) == 0 {
		return fmt.Errorf("没有指定要更新的字段")
	}

	// 总是更新update_ts
	setParts = append(setParts, fmt.Sprintf("update_ts = $%d", paramIndex))
	args = append(args, currentTime)
	paramIndex++

	// 添加WHERE条件参数
	args = append(args, itemId, expectedUpdateTs)

	query := fmt.Sprintf(`
		UPDATE %s
		SET %s
		WHERE id = $%d AND update_ts = $%d
	`, TableInventoryName, strings.Join(setParts, ", "), paramIndex, paramIndex+1)

	result, err := tx.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("更新道具字段失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("更新失败，道具可能不存在或已被其他操作修改")
	}

	return nil
}

// 添加或更新道具（合并函数）- 保持向后兼容
// 通过完整的道具信息进行添加或更新
// func UpsertItem(ctx context.Context, tx *sql.Tx, inventory *MainServer.Inventory) error {
// 	// 序列化 seal_info
// 	sealInfoBytes, err := json.Marshal(inventory.SealInfo)
// 	if err != nil {
// 		return fmt.Errorf("序列化封印信息失败: %w", err)
// 	}

// 	now := time.Now().Unix()
// 	if inventory.CreateTs == 0 {
// 		inventory.CreateTs = now
// 	}
// 	inventory.UpdateTs = now
// 	query := fmt.Sprintf(`
// 		INSERT INTO %s (tid, item_name, quantity, price, team_coin, team_type, status, item_sale_type, inventory_type, summon_poke_name_id, seal_poke_id, seal_info, create_ts, update_ts)
// 		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
// 		ON CONFLICT (tid, item_name, team_type, summon_poke_name_id, status, item_sale_type, seal_poke_id, inventory_type)
// 		DO UPDATE SET
// 			quantity = $3,
// 			price = $4,
// 			team_coin = $5,
// 			update_ts = $14
// 		RETURNING id
// 	`, TableInventoryName)

// 	err = tx.QueryRowContext(ctx, query,
// 		inventory.Tid,
// 		inventory.ItemName,
// 		inventory.Quantity,
// 		inventory.Price,
// 		inventory.TeamCoin,
// 		inventory.TeamType,
// 		int(inventory.Status),
// 		int(inventory.ItemSaleType),
// 		int(inventory.InventoryType),
// 		inventory.SummonPokeNameId,
// 		inventory.SealPokeId,
// 		string(sealInfoBytes),
// 		inventory.CreateTs,
// 		inventory.UpdateTs,
// 	).Scan(&inventory.Id)

// 	if err != nil {
// 		return fmt.Errorf("添加或更新道具失败: %w", err)
// 	}

// 	return nil
// }

// 便捷函数：只更新数量
// func UpdateItemQuantityOnly(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64, newQuantity int32) error {
// 	return UpdateItemFields(ctx, tx, itemId, expectedUpdateTs, UpdateFields{
// 		Quantity: &newQuantity,
// 	})
// }

// 便捷函数：只更新价格
func UpdateItemPrice(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64, newPrice int32) error {
	return UpdateItemFields(ctx, tx, itemId, expectedUpdateTs, UpdateFields{
		Price: &newPrice,
	})
}

// 便捷函数：只更新状态
func UpdateItemStatus(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64, newStatus MainServer.InventoryStatus) error {
	return UpdateItemFields(ctx, tx, itemId, expectedUpdateTs, UpdateFields{
		Status: &newStatus,
	})
}

// 便捷函数：更新价格和团队币
func UpdateItemPriceAndTeamCoin(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64, newPrice int32, newTeamCoin int32) error {
	return UpdateItemFields(ctx, tx, itemId, expectedUpdateTs, UpdateFields{
		Price:    &newPrice,
		TeamCoin: &newTeamCoin,
	})
}

func RemoveNormalItemByItemName(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) (*MainServer.Inventory, error) {
	// 检查道具数量是否足够
	localItem, exists := item.GetItemByName(itemName)
	if !exists {
		return nil, fmt.Errorf("道具不存在")
	}
	if localItem.InventoryType == MainServer.InventoryType_inventory_summon || localItem.InventoryType == MainServer.InventoryType_inventory_seal {
		return nil, fmt.Errorf("不能这样删除召唤/封印道具")
	}
	items, err := QueryItemsSimple(ctx, tx, tid, itemName, MainServer.InventoryStatus_InventoryStatus_Normal, 0)
	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
	if err != nil || len(items) == 0 {
		return nil, err
	}
	quantity := int32(0)
	for _, v := range items {
		quantity += v.Quantity
	}
	// if !hasEnough {
	// 	return fmt.Errorf("道具数量不足")
	// }
	if quantity < count {
		return nil, fmt.Errorf("道具数量不足")
	}
	for _, v := range items {
		//循环减，直到减够count数量，减一个保存一个
		if v.Quantity >= count {
			v.Quantity -= count
			err := UpdateItemQuantity(ctx, tx, v.Id, v.Quantity, v.UpdateTs)
			if err != nil {
				return nil, err
			}
			break
		} else {
			count -= v.Quantity
			v.Quantity = 0
			err := UpdateItemQuantity(ctx, tx, v.Id, v.Quantity, v.UpdateTs)
			if err != nil {
				return nil, err
			}
		}
	}

	return items[0], nil
}

// 不管什么类型的道具都可以
// func RemoveItemByItemName(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) error {
// 	// 检查道具数量是否足够

// 	items, err := GetItemsByItemName(ctx, tx, tid, itemName)
// 	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
// 	if err != nil {
// 		return err
// 	}
// 	quantity := int32(0)
// 	for _, v := range items {
// 		quantity += v.Quantity
// 	}
// 	// if !hasEnough {
// 	// 	return fmt.Errorf("道具数量不足")
// 	// }
// 	if quantity < count {
// 		return fmt.Errorf("道具数量不足")
// 	}
// 	for _, v := range items {
// 		//循环减，直到减够count数量，减一个保存一个
// 		if v.Quantity >= count {
// 			v.Quantity -= count
// 			err := UpsertItem(ctx, tx, v)
// 			if err != nil {
// 				return err
// 			}
// 			break
// 		} else {
// 			count -= v.Quantity
// 			v.Quantity = 0
// 			err := UpsertItem(ctx, tx, v)
// 			if err != nil {
// 				return err
// 			}
// 		}
// 	}

// 	return nil
// }

// func RemoveItemById(ctx context.Context, tx *sql.Tx, tid int64, itemId int64, itemName string, count int32) error {
// 	item, err := GetItemByTidAndId(ctx, tx, tid, itemId, itemName)
// 	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
// 	if err != nil {
// 		return err
// 	}
// 	// if !hasEnough {
// 	// 	return fmt.Errorf("道具数量不足")
// 	// }
// 	if item == nil || item.Quantity < count {
// 		return fmt.Errorf("道具数量不足")
// 	}
// 	item.Quantity -= count

//		return UpsertItem(ctx, tx, item)
//	}
func RemoveSummonItemById(ctx context.Context, tx *sql.Tx, tid int64, itemId int64, itemName string, count int32) (*MainServer.Inventory, error) {
	// level := SummonItemLevel(itemName)
	// if level == MainServer.PokeCateLevel_PokeCateLevel_Unknown {
	// 	return nil, fmt.Errorf("未知的召唤道具等级")
	// }
	// 检查道具数量是否足够
	item, err := GetItemByTidAndId(ctx, tx, tid, itemId, itemName)
	if err != nil {
		return nil, err
	}
	if item == nil || item.Quantity < count {
		return nil, fmt.Errorf("道具数量不足")
	}
	// if item.TeamType != teamType {
	// 	return runtime.NewError("道具团队类型不匹配", 400)
	// }
	// if IsSummonItemSpecial(itemName) && item.SummonPokeNameId == "" {
	// 	return nil, fmt.Errorf("特殊道具必须指定召唤精灵")
	// }
	item.Quantity -= count

	return item, UpdateItemQuantity(ctx, tx, item.Id, item.Quantity, item.UpdateTs)
}
func IsSummonItemSpecial(itemNameid string) bool {
	if itemNameid == "summon_legendary_normal" || itemNameid == "summon_legendary_special" || itemNameid == "summon_mythical" {
		return true
	}
	return false
}
func SummonItemLevel(itemNameid string) MainServer.PokeCateLevel {
	if itemNameid == "summon_level_1" {
		return MainServer.PokeCateLevel_PokeCateLevel_1
	} else if itemNameid == "summon_level_2" {
		return MainServer.PokeCateLevel_PokeCateLevel_2
	} else if itemNameid == "summon_level_3" {
		return MainServer.PokeCateLevel_PokeCateLevel_3
	} else if itemNameid == "summon_level_4" {
		return MainServer.PokeCateLevel_PokeCateLevel_4
	} else if itemNameid == "summon_level_5" {
		return MainServer.PokeCateLevel_PokeCateLevel_5
	} else if itemNameid == "summon_level_first" {
		return MainServer.PokeCateLevel_PokeCateLevel_First
	} else if itemNameid == "summon_level_late" {
		return MainServer.PokeCateLevel_PokeCateLevel_Late
	} else if itemNameid == "summon_level_paradox" {
		return MainServer.PokeCateLevel_PokeCateLevel_Paradox
	} else if itemNameid == "summon_level_ultra" {
		return MainServer.PokeCateLevel_PokeCateLevel_Ultra
	} else if itemNameid == "summon_legendary_normal" {
		return MainServer.PokeCateLevel_PokeCateLevel_LegendaryNormal
	} else if itemNameid == "summon_legendary_special" {
		return MainServer.PokeCateLevel_PokeCateLevel_LegendarySpecial
	} else if itemNameid == "summon_mythical" {
		return MainServer.PokeCateLevel_PokeCateLevel_Mythical
	}
	return MainServer.PokeCateLevel_PokeCateLevel_Unknown
}
func RemoveItemById(ctx context.Context, tx *sql.Tx, tid int64, itemId int64, itemName string, count int32) (*MainServer.Inventory, error) {
	// 检查道具数量是否足够

	item, err := GetItemByTidAndId(ctx, tx, tid, itemId, itemName)
	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
	if err != nil {
		return nil, err
	}
	// if !hasEnough {
	// 	return fmt.Errorf("道具数量不足")
	// }
	if item == nil || item.Quantity < count {
		return nil, fmt.Errorf("道具数量不足")
	}
	item.Quantity -= count

	// 更新道具数量
	// err = updateItemQuantityWithDetails(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal, summonPokeNameId, sealInfo)
	// if err != nil {
	// 	return err
	// }

	return item, UpdateItemQuantity(ctx, tx, item.Id, item.Quantity, item.UpdateTs)
}

func RemoveNormalItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) error {
	// 检查道具数量是否足够
	item, err := GetItemWithDetails(ctx, tx, MainServer.ItemSaleType_ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)
	if err != nil {
		return err
	}
	// if !hasEnough {
	// 	return fmt.Errorf("道具数量不足")
	// }
	if item == nil || item.Quantity < count {
		return fmt.Errorf("道具数量不足")
	}
	item.Quantity -= count

	// 更新道具数量
	// err = updateItemQuantityWithDetails(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal, summonPokeNameId, sealInfo)
	// if err != nil {
	// 	return err
	// }

	return UpdateItemQuantity(ctx, tx, item.Id, item.Quantity, item.UpdateTs)
}

// 移除道具
// 通过ItemName、tid和count进行移除，注意数量判断
// func RemoveItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) error {
// 	// 检查道具数量是否足够
// 	hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, tid, itemName, count, summonPokeNameId, sealInfo)
// 	if err != nil {
// 		return err
// 	}
// 	if !hasEnough {
// 		return fmt.Errorf("道具数量不足")
// 	}

// 	// 更新道具数量
// 	err = updateItemQuantityWithDetails(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal, summonPokeNameId, sealInfo)
// 	if err != nil {
// 		return err
// 	}

// 	return nil
// }

// 查找合适的道具（统一查找函数）
func findSuitableItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, requiredCount int32, allowedSaleTypes []MainServer.ItemSaleType) (*MainServer.Inventory, error) {
	// 使用现有的 QueryItemsExtended 函数
	var items []*MainServer.Inventory
	var err error

	// 使用统一的查询函数
	filter := &MainServer.InventoryFilter{
		Tid:              tid,
		ItemName:         itemName,
		Status:           status,
		MinQuantity:      requiredCount,
		AllowedSaleTypes: allowedSaleTypes,
	}
	items, err = QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	// 筛选数量足够的道具，并按数量降序排序
	var suitableItems []*MainServer.Inventory
	for _, item := range items {
		if item.Quantity >= requiredCount {
			suitableItems = append(suitableItems, item)
		}
	}

	if len(suitableItems) == 0 {
		return nil, fmt.Errorf("没有找到足够数量的道具")
	}

	// 选择数量最多的道具
	bestItem := suitableItems[0]
	for _, item := range suitableItems[1:] {
		if item.Quantity > bestItem.Quantity {
			bestItem = item
		}
	}

	return bestItem, nil
}

// 上架道具
// 通过 tid, itemName, count, price 进行上架
func SaleItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, price int32) error {
	// 1. 查找可上架的普通状态道具（只允许 Normal 和 Team_Normal 类型）
	allowedTypes := []MainServer.ItemSaleType{
		MainServer.ItemSaleType_ItemSaleType_Normal,
		MainServer.ItemSaleType_ItemSaleType_Team_Normal,
	}
	sourceItem, err := findSuitableItem(ctx, tx, tid, itemName, MainServer.InventoryStatus_InventoryStatus_Normal, count, allowedTypes)
	if err != nil {
		return err
	}
	if sourceItem.ItemSaleType == MainServer.ItemSaleType_ItemSaleType_Trainer_Only {
		return runtime.NewError("专属道具不能上架", 400)
	}

	// 2. 减少原道具数量
	sourceItem.Quantity -= count
	// 如果还有剩余，更新原记录
	err = UpdateItemQuantity(ctx, tx, sourceItem.Id, sourceItem.Quantity, sourceItem.UpdateTs)
	if err != nil {
		return fmt.Errorf("更新原道具数量失败: %w", err)
	}
	saleItem := &MainServer.Inventory{
		Tid:              tid,
		ItemName:         itemName,
		Quantity:         count,
		Price:            price,
		TeamCoin:         0, // 上架时默认为0
		Status:           MainServer.InventoryStatus_InventoryStatus_Sale,
		ItemSaleType:     sourceItem.ItemSaleType,  // 保持原有的销售类型
		InventoryType:    sourceItem.InventoryType, // 保持原有的库存类型
		SummonPokeNameId: sourceItem.SummonPokeNameId,
		SealInfo:         sourceItem.SealInfo,
	}
	oldSaleItem, _ := findSuitableItem(ctx, tx, tid, itemName, MainServer.InventoryStatus_InventoryStatus_Sale, 0, []MainServer.ItemSaleType{})
	if oldSaleItem != nil {
		saleItem.Quantity += oldSaleItem.Quantity
		UpdateItemQuantity(ctx, tx, oldSaleItem.Id, saleItem.Quantity, oldSaleItem.UpdateTs)
	} else {
		InsertItem(ctx, tx, saleItem)
	}
	// if sourceItem.Quantity > 0 {
	// 	// 如果还有剩余，更新原记录
	// 	err = UpsertItem(ctx, tx, sourceItem)
	// 	if err != nil {
	// 		return fmt.Errorf("更新原道具数量失败: %w", err)
	// 	}
	// } else {
	// 	// 如果数量为0，删除原记录
	// 	err = deleteItemById(ctx, tx, sourceItem.Id)
	// 	if err != nil {
	// 		return fmt.Errorf("删除空道具记录失败: %w", err)
	// 	}
	// }

	// 3. 创建或更新上架状态的道具

	// err = UpsertItem(ctx, tx, saleItem)
	// if err != nil {
	// 	return fmt.Errorf("上架道具失败: %w", err)
	// }

	return nil
}

// 根据ID查询道具（内部函数，使用统一查询）
// func getItemById(ctx context.Context, tx *sql.Tx, itemId int64) (*MainServer.Inventory, error) {
// 	return GetItemById(ctx, tx, itemId)
// }

// 根据ID删除道具
// func deleteItemById(ctx context.Context, tx *sql.Tx, itemId int64) error {
// 	query := fmt.Sprintf(`DELETE FROM %s WHERE id = $1`, TableInventoryName)

// 	result, err := tx.ExecContext(ctx, query, itemId)
// 	if err != nil {
// 		return fmt.Errorf("删除道具失败: %w", err)
// 	}

// 	rowsAffected, err := result.RowsAffected()
// 	if err != nil {
// 		return fmt.Errorf("获取影响行数失败: %w", err)
// 	}

// 	if rowsAffected == 0 {
// 		return fmt.Errorf("道具不存在")
// 	}

// 	return nil
// }

// 下架道具
// 通过 tid, itemName, count 进行下架
func UnsaleItem(ctx context.Context, tx *sql.Tx, tid int64, inventoryId int64, itemName string, count int32) error {
	// 1. 查找可下架的上架状态道具
	// sourceItem, err := findSuitableItem(ctx, tx, tid, itemName, MainServer.InventoryStatus_InventoryStatus_Sale, count, nil)
	// if err != nil {
	// 	return err
	// }
	sourceItem, err := GetItemByTidAndId(ctx, tx, tid, inventoryId, itemName)
	if err != nil {
		return err
	}
	if sourceItem.Quantity < count {
		return runtime.NewError("道具数量不足", 400)
	}
	if sourceItem.Status != MainServer.InventoryStatus_InventoryStatus_Sale {
		return runtime.NewError("道具不在上架状态", 400)
	}
	// 2. 减少上架道具数量
	sourceItem.Quantity -= count
	err = UpdateItemQuantity(ctx, tx, sourceItem.Id, sourceItem.Quantity, sourceItem.UpdateTs)
	if err != nil {
		return fmt.Errorf("更新上架道具数量失败: %w", err)
	}
	normalItem, _ := findSuitableItem(ctx, tx, tid, itemName, MainServer.InventoryStatus_InventoryStatus_Sale, 0, []MainServer.ItemSaleType{})
	if normalItem != nil {
		normalItem.Quantity += count
		UpdateItemQuantity(ctx, tx, normalItem.Id, normalItem.Quantity, normalItem.UpdateTs)
	} else {
		normalItem = &MainServer.Inventory{
			Tid:              tid,
			ItemName:         itemName,
			Quantity:         count,
			Price:            0, // 下架后价格重置为0
			TeamCoin:         0, // 下架后团队币重置为0
			Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
			ItemSaleType:     sourceItem.ItemSaleType,  // 保持原有的销售类型
			InventoryType:    sourceItem.InventoryType, // 保持原有的库存类型
			SummonPokeNameId: sourceItem.SummonPokeNameId,
			SealInfo:         sourceItem.SealInfo,
		}
		InsertItem(ctx, tx, normalItem)
	}

	return nil
}

// 不管什么类型的item都需要检查
func CheckItemQuantityByItemName(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) (bool, error) {

	items, err := GetItemsByItemName(ctx, tx, tid, itemName)
	if err != nil {
		return false, err
	}
	var totalQuantity int32
	for _, item := range items {
		totalQuantity += item.Quantity
	}
	return totalQuantity < count, nil
}

// 检查道具数量（简化版本，向后兼容）
// 通过ItemName、tid和count进行判断，看该tid是否有相应数量的Item
func CheckItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemId int64, itemName string, count int32, itemSaleType []MainServer.ItemSaleType) (bool, error) {

	item, err := GetItemByTidAndId(ctx, tx, tid, itemId, itemName)
	if err != nil {
		return false, err
	}
	typeFound := false
	for _, v := range itemSaleType {
		if item.ItemSaleType == v {
			typeFound = true
			break
		}
	}
	if !typeFound {
		return false, fmt.Errorf("道具类型不匹配")
	}
	// if item.ItemSaleType != itemSaleType {
	// 	return false, fmt.Errorf("道具类型不匹配")
	// }
	// hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, MainServer.ItemSaleType_Normal, tid, itemName, count, summonPokeNameId, sealInfo)

	// if !hasEnough {
	// 	return fmt.Errorf("道具数量不足")
	// }
	// if item == nil || item.Quantity < count {
	// 	return fmt.Errorf("道具数量不足")
	// }
	return item.Quantity < count, nil

	// return CheckItemQuantityWithDetails(ctx, tx, itemSaleType, tid, itemName, int32(count), "", &MainServer.InventorySealInfo{})
}

// // 检查道具数量（详细版本）
// // 通过完整的道具信息进行判断
// func CheckItemQuantityWithDetails(ctx context.Context, tx *sql.Tx, itemSaleType MainServer.ItemSaleType, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) (bool, error) {
// 	// 使用统一的查询函数来检查数量
// 	// filter := &MainServer.InventoryFilter{
// 	// 	Tid:              tid,
// 	// 	ItemName:         itemName,
// 	// 	Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
// 	// 	ItemSaleType:     itemSaleType,
// 	// 	SummonPokeNameId: summonPokeNameId,
// 	// 	SealPokeId:       sealInfo.PokeId,
// 	// }
// 	item, err := GetItemWithDetails(ctx, tx, itemSaleType, tid, itemName, count, summonPokeNameId, sealInfo)
// 	if err != nil {
// 		return false, err
// 	}
// 	return item.Quantity >= count, nil
// }

func GetItemWithDetails(ctx context.Context, tx *sql.Tx, itemSaleType MainServer.ItemSaleType, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) (*MainServer.Inventory, error) {
	// 使用统一的查询函数来检查数量
	filter := &MainServer.InventoryFilter{
		Tid:              tid,
		ItemName:         itemName,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     itemSaleType,
		SummonPokeNameId: summonPokeNameId,
		SealPokeId:       sealInfo.PokeId,
	}
	items, err := QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}
	if len(items) > 1 {
		return nil, fmt.Errorf("找到多个道具记录")
	}
	return items[0], nil
}

// 添加道具（简化版本，向后兼容）
// 通过ItemName、tid和count进行添加
func AddNormalItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, itemType MainServer.InventoryType, count int32) error {
	// inventory := &MainServer.Inventory{
	// 	Tid:              tid,
	// 	ItemName:         itemName,
	// 	Quantity:         count,
	// 	Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
	// 	ItemSaleType:     MainServer.ItemSaleType_Normal,
	// 	SummonPokeNameId: "",
	// 	SealInfo:         &MainServer.InventorySealInfo{},
	// }
	// return UpsertItem(ctx, tx, inventory)
	return AddItemToInventory(ctx, tx, tid, itemName, itemType, count, MainServer.ItemSaleType_ItemSaleType_Normal, MainServer.TrainerTeam_TRAINER_TEAM_NONE)
}

// 购买item时候用
func AddItemOrUpdate(ctx context.Context, tx *sql.Tx, tid int64, itemName string, itemType MainServer.InventoryType, count int32, itemSaleType MainServer.ItemSaleType, teamType MainServer.TrainerTeam, summonPokeNameId string) error {
	if itemType == MainServer.InventoryType_inventory_seal {
		return AddItemToInventory(ctx, tx, tid, itemName, itemType, count, itemSaleType, teamType)
	}
	inventoryItem, _ := QueryAddItem(ctx, tx, tid, itemName, itemType, teamType, summonPokeNameId)
	if inventoryItem != nil {
		inventoryItem.Quantity += count
		return UpdateItemQuantity(ctx, tx, inventoryItem.Id, inventoryItem.Quantity, inventoryItem.UpdateTs)
	} else {
		return AddItemToInventory(ctx, tx, tid, itemName, itemType, count, itemSaleType, teamType)
	}
}
func AddItemToInventory(ctx context.Context, tx *sql.Tx, tid int64, itemName string, itemType MainServer.InventoryType, count int32, itemSaleType MainServer.ItemSaleType, teamType MainServer.TrainerTeam) error {
	inventory := &MainServer.Inventory{
		Tid:              tid,
		ItemName:         itemName,
		Quantity:         count,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     itemSaleType,
		TeamType:         teamType,
		InventoryType:    itemType,
		SummonPokeNameId: "",
		SealInfo:         &MainServer.InventorySealInfo{},
	}
	_, exists := item.GetItemByKeyAndName(inventory.InventoryType, inventory.ItemName)
	if !exists {
		return runtime.NewError("Failed to query item info", 404)
	}
	return InsertItem(ctx, tx, inventory)
}

// 更新道具数量（简化版本，向后兼容）
// 内部函数，用于更新道具数量
// func updateItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, countDelta int, status MainServer.InventoryStatus) error {
// 	return updateItemQuantityWithDetails(ctx, tx, tid, itemName, int32(countDelta), status, "", &MainServer.InventorySealInfo{})
// }

// 更新道具数量（详细版本）
// 内部函数，用于更新道具数量
// func updateItemQuantityWithDetails(ctx context.Context, tx *sql.Tx, tid int64, itemName string, countDelta int32, status MainServer.InventoryStatus, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) error {
// 	// 序列化 seal_info
// 	sealInfoBytes, err := json.Marshal(sealInfo)
// 	if err != nil {
// 		return fmt.Errorf("序列化封印信息失败: %w", err)
// 	}

// 	now := time.Now().Unix()
// 	query := fmt.Sprintf(`
// 		UPDATE %s
// 		SET quantity = quantity + $1, update_ts = $2
// 		WHERE tid = $3 AND item_name = $4 AND status = $5 AND summon_poke_name_id = $6 AND seal_info = $7
// 	`, TableInventoryName)

// 	result, err := tx.ExecContext(ctx, query, countDelta, now, tid, itemName, int(status), summonPokeNameId, string(sealInfoBytes))
// 	if err != nil {
// 		return fmt.Errorf("更新道具数量失败: %w", err)
// 	}

// 	rowsAffected, err := result.RowsAffected()
// 	if err != nil {
// 		return fmt.Errorf("获取影响行数失败: %w", err)
// 	}

// 	if rowsAffected == 0 {
// 		return fmt.Errorf("道具不存在")
// 	}

// 	return nil
// }

// 临时的 InventoryFilter 结构（等待 proto 生成）
// 请在其他工程中生成对应的 proto 文件
/*
message InventoryFilter {
  int64 tid = 1;                                    // 训练师ID（必填）
  string item_name = 2;                             // 道具名称（可选，空字符串表示查询所有）
  InventoryStatus status = 3;                       // 道具状态（可选，0表示查询所有状态）
  InventoryItemSaleType item_sale_type = 4;         // 销售类型（可选，0表示查询所有类型）
  string summon_poke_name_id = 5;                   // 召唤宝可梦名称ID（可选）
  int64 seal_poke_id = 6;                          // 封印宝可梦ID（可选，0表示不限制）
  int64 update_ts = 7;                             // 更新时间戳（可选，0表示不限制）
  int32 min_quantity = 8;                          // 最小数量（可选，0表示不限制）
  repeated InventoryItemSaleType allowed_sale_types = 9; // 允许的销售类型列表（可选）
}
*/

// 临时结构，等待 proto 生成后替换
// type InventoryFilter struct {
// 	Tid              int64
// 	ItemName         string
// 	Status           MainServer.InventoryStatus
// 	ItemSaleType     MainServer.ItemSaleType
// 	SummonPokeNameId string
// 	SealPokeId       int64
// 	UpdateTs         int64
// 	MinQuantity      int32
// 	AllowedSaleTypes []MainServer.ItemSaleType
// }

// 统一的道具查询函数
// 使用 InventoryFilter 进行条件过滤
func QueryItems(ctx context.Context, tx *sql.Tx, filter *MainServer.InventoryFilter) ([]*MainServer.Inventory, error) {
	// 构建查询条件
	conditions := []string{}
	args := []interface{}{}
	paramIndex := 1

	// 如果指定了 id，则只查询该 id，忽略其他条件
	if filter.Id > 0 {
		conditions = append(conditions, fmt.Sprintf("id = $%d", paramIndex))
		args = append(args, filter.Id)
		paramIndex++
	}

	if filter.Tid > 0 {
		conditions = append(conditions, fmt.Sprintf("tid = $%d", paramIndex))
		args = append(args, filter.Tid)
		paramIndex++
	}

	if len(filter.ItemNames) > 0 {
		// conditions = append(conditions, fmt.Sprintf("item_name = $%d", paramIndex))
		// args = append(args, filter.ItemName)
		// paramIndex++
	}

	if filter.Status != 0 { // 0 表示查询所有状态
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(filter.Status))
		paramIndex++
	}

	// 处理销售类型过滤
	if len(filter.AllowedSaleTypes) > 0 {
		// 如果指定了允许的销售类型列表，使用 IN 查询
		placeholders := make([]string, len(filter.AllowedSaleTypes))
		for i, saleType := range filter.AllowedSaleTypes {
			placeholders[i] = fmt.Sprintf("$%d", paramIndex)
			args = append(args, int(saleType))
			paramIndex++
		}
		conditions = append(conditions, fmt.Sprintf("item_sale_type IN (%s)", strings.Join(placeholders, ",")))
	} else if filter.ItemSaleType != 0 {
		// 如果指定了单个销售类型
		conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
		args = append(args, int(filter.ItemSaleType))
		paramIndex++
	}

	// 处理库存类型过滤
	if filter.InventoryType != MainServer.InventoryType_inventory_unknown {
		conditions = append(conditions, fmt.Sprintf("inventory_type = $%d", paramIndex))
		args = append(args, int(filter.InventoryType))
		paramIndex++
	}

	if filter.SummonPokeNameId != "" {
		conditions = append(conditions, fmt.Sprintf("summon_poke_name_id = $%d", paramIndex))
		args = append(args, filter.SummonPokeNameId)
		paramIndex++
	}

	if filter.SealPokeId > 0 {
		conditions = append(conditions, fmt.Sprintf("seal_poke_id = $%d", paramIndex))
		args = append(args, fmt.Sprintf("%d", filter.SealPokeId))
		paramIndex++
	}

	if filter.UpdateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, filter.UpdateTs)
		paramIndex++
	}

	if filter.MinQuantity > 0 {
		conditions = append(conditions, fmt.Sprintf("quantity >= $%d", paramIndex))
		args = append(args, filter.MinQuantity)
		paramIndex++
	}

	// 添加价格过滤条件
	if filter.MinPrice > 0 {
		conditions = append(conditions, fmt.Sprintf("price >= $%d", paramIndex))
		args = append(args, filter.MinPrice)
		paramIndex++
	}

	if filter.MaxPrice > 0 {
		conditions = append(conditions, fmt.Sprintf("price <= $%d", paramIndex))
		args = append(args, filter.MaxPrice)
		paramIndex++
	}

	// 构建查询语句
	baseQuery := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, team_type, status, item_sale_type, inventory_type, summon_poke_name_id, seal_poke_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE %s
		ORDER BY update_ts DESC
	`, TableInventoryName, strings.Join(conditions, " AND "))

	// 添加分页支持
	query := baseQuery
	if filter.PageSize > 0 {
		// 计算偏移量
		offset := int32(0)
		if filter.Page > 0 {
			offset = (filter.Page - 1) * filter.PageSize
		}

		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", baseQuery, paramIndex, paramIndex+1)
		args = append(args, filter.PageSize, offset)
	}

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var statusInt int
		var itemSaleType int
		var inventoryType int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&item.TeamType,
			&statusInt,
			&itemSaleType,
			&inventoryType,
			&item.SummonPokeNameId,
			&item.SealPokeId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(statusInt)
		item.ItemSaleType = MainServer.ItemSaleType(itemSaleType)
		item.InventoryType = MainServer.InventoryType(inventoryType)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 批量添加或更新道具
// func BatchUpsertItems(ctx context.Context, tx *sql.Tx, inventories []*MainServer.Inventory) error {
// 	for _, inventory := range inventories {
// 		err := UpsertItem(ctx, tx, inventory)
// 		if err != nil {
// 			return fmt.Errorf("批量添加道具失败，道具: %s, 错误: %w", inventory.ItemName, err)
// 		}
// 	}
// 	return nil
// }

// 获取道具总数量（按条件）
func GetItemTotalQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus) (int32, error) {
	var total int32
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(quantity), 0) FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3
	`, TableInventoryName)

	err := tx.QueryRowContext(ctx, query, tid, itemName, int(status)).Scan(&total)
	if err != nil {
		return 0, fmt.Errorf("查询道具总数量失败: %w", err)
	}

	return total, nil
}

// 获取指定训练师的可上架道具列表
func GetSaleableItems(ctx context.Context, tx *sql.Tx, tid int64) ([]*MainServer.Inventory, error) {
	// 使用统一的查询函数
	filter := &MainServer.InventoryFilter{
		Tid:    tid,
		Status: MainServer.InventoryStatus_InventoryStatus_Normal,
		AllowedSaleTypes: []MainServer.ItemSaleType{
			MainServer.ItemSaleType_ItemSaleType_Normal,
			MainServer.ItemSaleType_ItemSaleType_Team_Normal,
		},
	}
	return QueryItems(ctx, tx, filter)
}

// 获取指定训练师的已上架道具列表
func GetSaledItems(ctx context.Context, tx *sql.Tx, tid int64, page int32, pageSize int32) ([]*MainServer.Inventory, error) {
	// 使用统一的查询函数
	filter := &MainServer.InventoryFilter{
		Tid:      tid,
		Status:   MainServer.InventoryStatus_InventoryStatus_Sale,
		Page:     page,
		PageSize: pageSize,
	}
	return QueryItems(ctx, tx, filter)
}

// 便捷函数：获取玩家的所有道具（向后兼容）
func GetAllItems(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Tid:      tid,
		UpdateTs: updateTs,
	}
	return QueryItems(ctx, tx, filter)
}

// 便捷函数：简化的查询接口（向后兼容）
func QueryItemsSimple(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, updateTs int64) ([]*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Tid:      tid,
		ItemName: itemName,
		Status:   status,
		UpdateTs: updateTs,
	}
	return QueryItems(ctx, tx, filter)
}
func QueryAddItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, inventoryType MainServer.InventoryType, teamType MainServer.TrainerTeam, summonPokeNameId string) (*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Tid:              tid,
		ItemName:         itemName,
		InventoryType:    inventoryType,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		TeamType:         teamType,
		SummonPokeNameId: summonPokeNameId,
		AllowedSaleTypes: []MainServer.ItemSaleType{
			MainServer.ItemSaleType_ItemSaleType_Normal,
			MainServer.ItemSaleType_ItemSaleType_Team_Normal,
		},
	}
	items, err := QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}
	if len(items) > 0 {
		return items[0], nil
	}
	return nil, nil
}
func GetItemsByItemName(ctx context.Context, tx *sql.Tx, tid int64, itemName string) ([]*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Tid:      tid,
		ItemName: itemName,
	}
	items, err := QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("道具不存在")
	}

	return items, nil
}

// 便捷函数：通过 ID 查询单个道具
func GetItemById(ctx context.Context, tx *sql.Tx, itemId int64, itemName string) (*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Id:       itemId,
		ItemName: itemName,
	}
	items, err := QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("道具不存在")
	}

	return items[0], nil
}
func GetItemByTidAndId(ctx context.Context, tx *sql.Tx, tid int64, itemId int64, itemName string) (*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Id:       itemId,
		ItemName: itemName,
		Tid:      tid,
	}
	items, err := QueryItems(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(items) == 0 {
		return nil, fmt.Errorf("道具不存在")
	}

	return items[0], nil
}

// 查询道具（支持价格过滤和分页）
func QueryItemsWithPriceAndPaging(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, minPrice int32, maxPrice int32, page int32, pageSize int32) ([]*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		Tid:      tid,
		ItemName: itemName,
		Status:   status,
		MinPrice: minPrice,
		MaxPrice: maxPrice,
		Page:     page,
		PageSize: pageSize,
	}
	return QueryItems(ctx, tx, filter)
}

// 查询市场道具（支持价格过滤和分页）
func QueryMarketItems(ctx context.Context, tx *sql.Tx, itemName string, minPrice int32, maxPrice int32, page int32, pageSize int32) ([]*MainServer.Inventory, error) {
	filter := &MainServer.InventoryFilter{
		ItemName: itemName,
		Status:   MainServer.InventoryStatus_InventoryStatus_Sale,
		MinPrice: minPrice,
		MaxPrice: maxPrice,
		Page:     page,
		PageSize: pageSize,
	}
	return QueryItems(ctx, tx, filter)
}

// 便捷函数：通过 ID 修改并保存道具
// func UpdateItemById(ctx context.Context, tx *sql.Tx, itemId int64, updateFunc func(*MainServer.Inventory) error) error {
// 	// 1. 查询道具
// 	item, err := GetItemById(ctx, tx, itemId)
// 	if err != nil {
// 		return fmt.Errorf("查询道具失败: %w", err)
// 	}

// 	// 2. 执行修改函数
// 	err = updateFunc(item)
// 	if err != nil {
// 		return fmt.Errorf("修改道具失败: %w", err)
// 	}

// 	// 3. 保存修改
// 	err = UpsertItem(ctx, tx, item)
// 	if err != nil {
// 		return fmt.Errorf("保存道具失败: %w", err)
// 	}

// 	return nil
// }

// UpdateItemQuantity 更新道具数量，使用update_ts作为乐观锁
// 如果数量更新后为0，则删除记录
func UpdateItemQuantity(ctx context.Context, tx *sql.Tx, itemId int64, newQuantity int32, expectedUpdateTs int64) error {
	currentTime := time.Now().Unix()

	// 如果新数量为0，直接删除记录
	if newQuantity <= 0 {
		return DeleteItemById(ctx, tx, itemId, expectedUpdateTs)
	}

	// 使用乐观锁更新数量
	query := fmt.Sprintf(`
		UPDATE %s
		SET quantity = $1, update_ts = $2
		WHERE id = $3 AND update_ts = $4
	`, TableInventoryName)

	result, err := tx.ExecContext(ctx, query, newQuantity, currentTime, itemId, expectedUpdateTs)
	if err != nil {
		return fmt.Errorf("更新道具数量失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("更新失败，道具可能不存在或已被其他操作修改")
	}

	return nil
}

// DeleteItemById 根据ID删除道具记录，使用update_ts作为乐观锁
func DeleteItemById(ctx context.Context, tx *sql.Tx, itemId int64, expectedUpdateTs int64) error {
	query := fmt.Sprintf(`
		DELETE FROM %s
		WHERE id = $1 AND update_ts = $2
	`, TableInventoryName)

	result, err := tx.ExecContext(ctx, query, itemId, expectedUpdateTs)
	if err != nil {
		return fmt.Errorf("删除道具记录失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("删除失败，道具可能不存在或已被其他操作修改")
	}

	return nil
}
