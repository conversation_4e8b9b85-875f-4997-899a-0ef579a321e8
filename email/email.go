package email

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/lib/pq"
)

const (
	TableEmailName = "emails"
)

func InitEmailsTable(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTableSQL := fmt.Sprintf(`
	CREATE TABLE IF NOT EXISTS %s (
		id BIGSERIAL PRIMARY KEY,
		sender_id BIGINT NOT NULL,
		receiver_id BIGINT NOT NULL,
		contents JSONB NOT NULL DEFAULT '{}'::jsonb,
		send_time BIGINT NOT NULL,
		read_time BIGINT DEFAULT 0,
		status INT NOT NULL,
		attachments JSONB NOT NULL DEFAULT '{}'::jsonb,
		email_type INT NOT NULL DEFAULT 0,
		extra JSONB NOT NULL DEFAULT '{}'::jsonb,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);
	CREATE INDEX IF NOT EXISTS idx_%s_receiver_id ON %s (receiver_id);
	CREATE INDEX IF NOT EXISTS idx_%s_sender_id ON %s (sender_id);
	CREATE INDEX IF NOT EXISTS idx_%s_status ON %s (status);
	CREATE INDEX IF NOT EXISTS idx_%s_send_time ON %s (send_time);
	CREATE INDEX IF NOT EXISTS idx_%s_email_type ON %s (email_type);
	`, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName, TableEmailName)
	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Error creating emails table: %v", err)
	} else {
		logger.Info("Emails table created or already exists")
	}
}

// sendEmail 发送邮件给指定训练师
func sendEmail(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, senderId, receiverId int64, contents map[string]*MainServer.EmailLanguage, attachments *MainServer.EmailAttachment, emailType MainServer.EmainType, extra *MainServer.EmailExtra) error {
	// 序列化附件为jsonb
	var attachmentsBytes []byte
	var err error
	if attachments != nil {
		attachmentsBytes, err = json.Marshal(attachments)
		if err != nil {
			return fmt.Errorf("序列化附件失败: %v", err)
		}
	}

	// 序列化contents为jsonb
	contentsBytes, err := json.Marshal(contents)
	if err != nil {
		return fmt.Errorf("序列化邮件内容失败: %v", err)
	}

	// 序列化extra为jsonb
	var extraBytes []byte
	if extra == nil {
		extra = &MainServer.EmailExtra{}
	}
	extraBytes, err = json.Marshal(extra)
	if err != nil {
		return fmt.Errorf("序列化邮件额外信息失败: %v", err)
	}

	// 插入邮件记录
	query := fmt.Sprintf(`
		INSERT INTO %s (sender_id, receiver_id, contents, send_time, status, attachments, email_type, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`, TableEmailName)
	now := time.Now().UnixMilli()
	var emailId int64
	err = tx.QueryRowContext(ctx, query,
		senderId,
		receiverId,
		contentsBytes,
		now,
		MainServer.EmailStatus_EMAIL_STATUS_UNREAD,
		attachmentsBytes,
		emailType,
		extraBytes,
	).Scan(&emailId)
	if err != nil {
		return fmt.Errorf("插入邮件记录失败: %v", err)
	}

	// 获取接收者信息
	receiver := tool.GetActiveTrainerByTid(receiverId)
	if receiver == nil {
		logger.Error("接收者不在线 (receiver_id: %d)", receiverId)
		return nil
	}

	// 构建邮件信息
	email := &MainServer.EmailInfo{
		Id:          emailId,
		SenderId:    senderId,
		ReceiverId:  receiverId,
		Contents:    contents,
		SendTime:    now,
		Status:      MainServer.EmailStatus_EMAIL_STATUS_UNREAD,
		Attachments: attachments,
		EmailType:   emailType,
		Extra:       extra,
	}

	// 发送通知
	notification := &MainServer.EmailNotification{
		Email: email,
	}
	notificationStr, _ := tool.ProtoToBase64(notification)
	subject := "new email"
	contentMap := map[string]interface{}{
		"email": notificationStr,
	}
	code := 200 // ServerNotificationType_NewEmail = 200
	persistent := true

	if err := nk.NotificationSend(ctx, receiver.Uid, subject, contentMap, code, "", persistent); err != nil {
		logger.Error("发送邮件通知失败: %v", err)
		// 继续执行，不中断流程
	}

	return nil
}

// GetEmailList 获取用户的邮件列表
func GetEmailList(ctx context.Context, logger runtime.Logger, db *sql.DB, userId int64, page int32, pageSize int32, isOnlyUnRead bool) ([]*MainServer.EmailInfo, error) {
	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询条件
	var query string
	var args []interface{}

	if isOnlyUnRead {
		query = fmt.Sprintf(`
			SELECT id, sender_id, receiver_id, contents, send_time, read_time, status, attachments, email_type, extra
			FROM %s
			WHERE receiver_id = $1 AND status = $2
			ORDER BY send_time DESC
			LIMIT $3 OFFSET $4
		`, TableEmailName)
		args = append(args, userId, MainServer.EmailStatus_EMAIL_STATUS_UNREAD, pageSize, offset)
	} else {
		query = fmt.Sprintf(`
			SELECT id, sender_id, receiver_id, contents, send_time, read_time, status, attachments, email_type, extra
			FROM %s
			WHERE receiver_id = $1 AND status != $2
			ORDER BY send_time DESC
			LIMIT $3 OFFSET $4
		`, TableEmailName)
		args = append(args, userId, MainServer.EmailStatus_EMAIL_STATUS_DELETED, pageSize, offset)
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询邮件列表失败: %v", err)
	}
	defer rows.Close()

	var emails []*MainServer.EmailInfo
	for rows.Next() {
		var email MainServer.EmailInfo
		var contentsBytes []byte
		var attachmentsBytes []byte
		var extraBytes []byte
		var emailType int32
		if err := rows.Scan(
			&email.Id,
			&email.SenderId,
			&email.ReceiverId,
			&contentsBytes,
			&email.SendTime,
			&email.ReadTime,
			&email.Status,
			&attachmentsBytes,
			&emailType,
			&extraBytes,
		); err != nil {
			return nil, fmt.Errorf("读取邮件数据失败: %v", err)
		}

		email.EmailType = MainServer.EmainType(emailType)

		if len(contentsBytes) > 0 {
			var contents map[string]*MainServer.EmailLanguage
			if err := json.Unmarshal(contentsBytes, &contents); err == nil {
				email.Contents = contents
			}
		}

		if len(attachmentsBytes) > 0 {
			var emailAttachments MainServer.EmailAttachment
			if err := json.Unmarshal(attachmentsBytes, &emailAttachments); err == nil {
				email.Attachments = &emailAttachments
			}
		}

		if len(extraBytes) > 0 {
			var extra MainServer.EmailExtra
			if err := json.Unmarshal(extraBytes, &extra); err == nil {
				email.Extra = &extra
			}
		}

		emails = append(emails, &email)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历邮件列表失败: %v", err)
	}

	// 构建响应
	// response := &MainServer.EmailListResponse{
	// 	Emails: emails,
	// }

	return emails, nil
}

// ReadEmail 标记邮件为已读
func ReadEmail(ctx context.Context, logger runtime.Logger, db *sql.DB, emailId int64, userId int64) error {
	query := fmt.Sprintf(`UPDATE %s 
			  SET status = $1, read_time = $2 
			  WHERE id = $3 AND receiver_id = $4 AND status = $5`, TableEmailName)

	result, err := db.ExecContext(ctx, query,
		int32(MainServer.EmailStatus_EMAIL_STATUS_READ),
		time.Now().UnixMilli(),
		emailId,
		userId,
		int32(MainServer.EmailStatus_EMAIL_STATUS_UNREAD),
	)
	if err != nil {
		logger.Error("更新邮件状态失败: %v", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取影响行数失败: %v", err)
		return err
	}

	if rowsAffected == 0 {
		return runtime.NewError("邮件不存在或已被读取", 404)
	}

	return nil
}

// DeleteEmails 批量删除邮件
func DeleteEmails(ctx context.Context, logger runtime.Logger, db *sql.DB, emailIds []int64, userId int64) error {
	if len(emailIds) == 0 {
		return nil
	}

	// 构建IN查询的参数
	query := fmt.Sprintf(`UPDATE %s 
			  SET status = $1 
			  WHERE id = ANY($2) AND receiver_id = $3`, TableEmailName)

	_, err := db.ExecContext(ctx, query,
		int32(MainServer.EmailStatus_EMAIL_STATUS_DELETED),
		emailIds,
		userId,
	)
	if err != nil {
		logger.Error("删除邮件失败: %v", err)
		return err
	}

	return nil
}

// GetEmailById 根据ID获取邮件详情
func GetEmailById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, tid int64) (*MainServer.EmailInfo, error) {
	query := fmt.Sprintf(`SELECT id, sender_id, receiver_id, contents, send_time, read_time, status, attachments, email_type, extra
			  FROM %s
			  WHERE id = $1 AND receiver_id = $2`, TableEmailName)

	var email MainServer.EmailInfo
	var contentsBytes []byte
	var attachmentsBytes []byte
	var extraBytes []byte
	var readTime sql.NullInt64
	var status int32
	var emailType int32

	err := tx.QueryRowContext(ctx, query, emailId, tid).Scan(
		&email.Id,
		&email.SenderId,
		&email.ReceiverId,
		&contentsBytes,
		&email.SendTime,
		&readTime,
		&status,
		&attachmentsBytes,
		&emailType,
		&extraBytes,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, runtime.NewError("邮件不存在", 404)
		}
		logger.Error("查询邮件详情失败: %v", err)
		return nil, err
	}

	email.Status = MainServer.EmailStatus(status)
	email.EmailType = MainServer.EmainType(emailType)
	if readTime.Valid {
		email.ReadTime = readTime.Int64
	}

	if len(contentsBytes) > 0 {
		var contents map[string]*MainServer.EmailLanguage
		if err := json.Unmarshal(contentsBytes, &contents); err != nil {
			logger.Error("解析邮件内容失败: %v", err)
		} else {
			email.Contents = contents
		}
	}

	if len(attachmentsBytes) > 0 {
		var attachments MainServer.EmailAttachment
		if err := json.Unmarshal(attachmentsBytes, &attachments); err != nil {
			logger.Error("解析邮件附件失败: %v", err)
		} else {
			email.Attachments = &attachments
		}
	}

	if len(extraBytes) > 0 {
		var extra MainServer.EmailExtra
		if err := json.Unmarshal(extraBytes, &extra); err != nil {
			logger.Error("解析邮件额外信息失败: %v", err)
		} else {
			email.Extra = &extra
		}
	}

	return &email, nil
}

// RpcGetEmailById 获取邮件详情RPC处理函数
// func RpcGetEmailById(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取当前用户ID
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}

// 	// 解析请求
// 	var request struct {
// 		EmailId int64 `json:"email_id"`
// 	}
// 	if err := json.Unmarshal([]byte(payload), &request); err != nil {
// 		logger.Error("解析请求失败: %v", err)
// 		return "", runtime.NewError("无效的请求格式", 400)
// 	}

// 	// 获取邮件详情
// 	email, err := GetEmailById(ctx, logger, db, request.EmailId, trainer.Id)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 序列化响应
// 	responseBytes, err := proto.Marshal(email)
// 	if err != nil {
// 		logger.Error("序列化响应失败: %v", err)
// 		return "", err
// 	}

// 	return string(responseBytes), nil
// }

// ReadEmailWithTx 在事务中标记邮件为已读
func ReadEmailWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, userId int64) error {
	return OperateEmailForStatusWithTx(ctx, logger, tx, emailId, userId, MainServer.EmailStatus_EMAIL_STATUS_UNREAD, MainServer.EmailStatus_EMAIL_STATUS_READ)
}

//	func DeleteEmailWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, userId int64) error {
//		return OperateEmailWithTx(ctx, logger, tx, emailId, userId, MainServer.EmailStatus_EMAIL_STATUS_UNREAD, MainServer.EmailStatus_EMAIL_STATUS_DELETED)
//	}
func ReceiveEmailAttachmentsWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, tId int64) error {
	return OperateEmailNotStatusWithTx(ctx, logger, tx, emailId, tId, MainServer.EmailStatus_EMAIL_STATUS_DELETED, MainServer.EmailStatus_EMAIL_STATUS_RECEIVED_ATTACHMENT)
}
func OperateEmailNotStatusWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, userId int64, fromNotStatus MainServer.EmailStatus, toStatus MainServer.EmailStatus) error {
	query := fmt.Sprintf(`
		UPDATE %s 
		SET status = $1, read_time = $2 
		WHERE id = $3 AND receiver_id = $4 AND status != $5
	`, TableEmailName)
	now := time.Now().UnixMilli()
	result, err := tx.ExecContext(ctx, query,
		toStatus,
		now,
		emailId,
		userId,
		fromNotStatus)
	if err != nil {
		return fmt.Errorf("更新邮件状态失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("邮件不存在或状态不正确")
	}

	return nil
}
func OperateEmailForStatusWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailId int64, userId int64, fromStatus MainServer.EmailStatus, toStatus MainServer.EmailStatus) error {
	query := fmt.Sprintf(`
		UPDATE %s 
		SET status = $1, read_time = $2 
		WHERE id = $3 AND receiver_id = $4 AND status = $5
	`, TableEmailName)
	now := time.Now().UnixMilli()
	result, err := tx.ExecContext(ctx, query,
		toStatus,
		now,
		emailId,
		userId,
		fromStatus)
	if err != nil {
		return fmt.Errorf("更新邮件状态失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("邮件不存在或状态不正确")
	}

	return nil
}

// DeleteEmailsWithTx 在事务中删除邮件
func DeleteEmailsWithTx(ctx context.Context, logger runtime.Logger, tx *sql.Tx, emailIds []int64, userId int64) error {
	if len(emailIds) == 0 {
		return nil
	}

	query := fmt.Sprintf(`
		UPDATE %s 
		SET status = $1 
		WHERE id = ANY($2) AND receiver_id = $3 AND status != $4
	`, TableEmailName)
	result, err := tx.ExecContext(ctx, query,
		MainServer.EmailStatus_EMAIL_STATUS_DELETED,
		pq.Array(emailIds),
		userId,
		MainServer.EmailStatus_EMAIL_STATUS_DELETED)
	if err != nil {
		return fmt.Errorf("删除邮件失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("没有邮件被删除")
	}

	return nil
}

func SendPokesEmail(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, senderId int64, receiverId int64, pokes []*MainServer.Poke, emailType MainServer.EmainType) error {
	contents := make(map[string]*MainServer.EmailLanguage)
	contents[strconv.Itoa(int(MainServer.EmailLanguageKey_EmailLanguageKey_EN))] = &MainServer.EmailLanguage{
		Title:   "New Pokes",
		Content: "You have received new pokes",
	}
	contents[strconv.Itoa(int(MainServer.EmailLanguageKey_EmailLanguageKey_ZH))] = &MainServer.EmailLanguage{
		Title:   "新精灵",
		Content: "您收到了新的精灵",
	}
	contents[strconv.Itoa(int(MainServer.EmailLanguageKey_EmailLanguageKey_JP))] = &MainServer.EmailLanguage{
		Title:   "新しいポケモン",
		Content: "新しいポケモンを受け取りました",
	}
	attachments := &MainServer.EmailAttachment{
		Pokemons: pokes,
	}
	return sendEmail(ctx, logger, tx, nk, senderId, receiverId, contents, attachments, emailType, nil)
}

// SendEmailsToUsers 给多个指定用户发送邮件
func SendEmailsToUsers(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, senderId int64, receiverIds []int64, contents map[string]*MainServer.EmailLanguage, attachments *MainServer.EmailAttachment, emailType MainServer.EmainType, extra *MainServer.EmailExtra) error {
	if len(receiverIds) == 0 {
		return fmt.Errorf("接收者列表为空")
	}
	if len(contents) == 0 {
		if attachments != nil {
			if len(attachments.Pokemons) > 0 || len(attachments.Items) > 0 {
				return fmt.Errorf("邮件内容为空")
			}
		} else {
			return fmt.Errorf("邮件内容为空")
		}
	}

	// 序列化附件为jsonb
	var attachmentsBytes []byte
	var err error
	if attachments != nil {
		attachmentsBytes, err = json.Marshal(attachments)
		if err != nil {
			return fmt.Errorf("序列化附件失败: %v", err)
		}
	}

	// 序列化contents为jsonb
	contentsBytes, err := json.Marshal(contents)
	if err != nil {
		return fmt.Errorf("序列化邮件内容失败: %v", err)
	}

	// 序列化extra为jsonb
	var extraBytes []byte
	if extra != nil {
		extraBytes, err = json.Marshal(extra)
		if err != nil {
			return fmt.Errorf("序列化邮件额外信息失败: %v", err)
		}
	}

	// 使用事务批量插入邮件记录
	query := fmt.Sprintf(`
		INSERT INTO %s (sender_id, receiver_id, contents, send_time, status, attachments, email_type, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`, TableEmailName)
	now := time.Now().Unix()

	// 准备批量插入的语句
	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("准备批量插入语句失败: %v", err)
	}
	defer stmt.Close()

	// 用于存储所有接收者的通知信息
	notifications := make([]struct {
		uid   string
		email *MainServer.EmailInfo
	}, 0, len(receiverIds))

	// 批量插入邮件记录
	for _, receiverId := range receiverIds {
		var emailId int64
		err = stmt.QueryRowContext(ctx,
			senderId,
			receiverId,
			contentsBytes,
			now,
			MainServer.EmailStatus_EMAIL_STATUS_UNREAD,
			attachmentsBytes,
			emailType,
			extraBytes,
		).Scan(&emailId)
		if err != nil {
			logger.Error("插入邮件记录失败 (receiver_id: %d): %v", receiverId, err)
			continue
		}

		// 获取接收者信息
		receiver := tool.GetActiveTrainerByTid(receiverId)
		if receiver == nil {
			logger.Error("接收者不在线 (receiver_id: %d)", receiverId)
			continue
		}

		// 构建邮件信息
		email := &MainServer.EmailInfo{
			Id:          emailId,
			SenderId:    senderId,
			ReceiverId:  receiverId,
			Contents:    contents,
			SendTime:    now,
			Status:      MainServer.EmailStatus_EMAIL_STATUS_UNREAD,
			Attachments: attachments,
			EmailType:   emailType,
			Extra:       extra,
		}

		notifications = append(notifications, struct {
			uid   string
			email *MainServer.EmailInfo
		}{receiver.Uid, email})
	}

	// 批量发送通知
	for _, notification := range notifications {
		notificationMsg := &MainServer.EmailNotification{
			Email: notification.email,
		}
		notificationStr, _ := tool.ProtoToBase64(notificationMsg)
		subject := "新邮件"
		contentMap := map[string]interface{}{
			"email": notificationStr,
		}
		code := 200 // ServerNotificationType_NewEmail = 200
		persistent := true

		if err := nk.NotificationSend(ctx, notification.uid, subject, contentMap, code, "", persistent); err != nil {
			logger.Error("发送邮件通知失败 (uid: %s): %v", notification.uid, err)
			// 继续发送其他通知
			continue
		}
	}

	return nil
}
