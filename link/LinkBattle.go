package link

import (
	"context"
	"database/sql"
	"go-nakama-poke/battle"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

type LinkBattle struct{}

func NewLinkBattle() tool.ToolBattleLink {
	return new(LinkBattle)
}
func (linkedBattle *LinkBattle) TryBattlePrepare(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, trainerInfo *MainServer.Trainer, prepareInfo *MainServer.BattlePrepare, moreConfigInfo *MainServer.BattleMoreConfigInfo) (string, error) {
	return battle.TryBattlePrepare(ctx, logger, tx, nk, trainerInfo, prepareInfo, moreConfigInfo)
}
