package trainer

import (
	"context"
	"database/sql"
	"strconv"
	"time"

	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// TODO: 所有RPC函数需要在另一个项目中定义相应的proto消息
// 包括：RpcAcceptQuestRequest, RpcAcceptQuestResponse, RpcCompleteQuestRequest,
// RpcCompleteQuestResponse, RpcClaimQuestRewardRequest, RpcClaimQuestRewardResponse,
// RpcGetAvailableQuestsRequest, RpcGetAvailableQuestsResponse, RpcGetTrainerQuestsResponse,
// RpcUpdateQuestProgressRequest, RpcUpdateQuestProgressResponse
func RpcAcceptTeamQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByCtx(ctx)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}
	if trainerInfo.Team == MainServer.TrainerTeam_TRAINER_TEAM_NONE {
		return "", runtime.NewError("你还没有加入任何队伍", 400)
	}
	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	trainerQuest, err := AcceptTeamQuest(ctx, logger, tx, trainerInfo)
	if err != nil {
		return "", err
	}
	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}
	response := &MainServer.RpcAcceptQuestResponse{
		Success:      true,
		Message:      "任务接受成功",
		TrainerQuest: trainerQuest,
	}
	return tool.ProtoToBase64(response)
}

// RpcAcceptQuest RPC函数：接受任务
func RpcAcceptQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}
	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}
	questId := payload
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }
	request := &MainServer.RpcAcceptQuestRequest{
		QuestId: questId,
	}
	// 解析请求参数
	// var request MainServer.RpcAcceptQuestRequest
	// err := tool.Base64ToProto(payload, &request)
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 接受任务
	_, err = AcceptQuest(ctx, logger, tx, trainerInfo, request.QuestId, false)
	if err != nil {
		return "", runtime.NewError("接受任务失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcAcceptQuestResponse{
		Success: true,
		Message: "任务接受成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcCompleteQuest RPC函数：完成任务
func RpcCompleteQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}
	questId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	request := &MainServer.RpcCompleteQuestRequest{
		DbId:       questId,
		ParentDbId: 1,
	}
	// 解析请求参数
	// var request MainServer.RpcCompleteQuestRequest
	// err := tool.Base64ToProto(payload, &request)
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }

	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 完成任务
	err = CompleteQuestByParentAndId(ctx, logger, tx, trainerInfo, request.ParentDbId, request.DbId)
	if err != nil {
		return "", runtime.NewError("完成任务失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcCompleteQuestResponse{
		Success: true,
		Message: "任务完成成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcClaimQuestReward RPC函数：领取任务奖励
// func RpcClaimQuestReward(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcClaimQuestRewardRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainerInfo := tool.GetActiveTrainerByUid(userID)
// 	if trainerInfo == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 领取任务奖励
// 	err = ClaimQuestReward(ctx, logger, tx, trainerInfo.Id, request.QuestId)
// 	if err != nil {
// 		return "", runtime.NewError("领取任务奖励失败: "+err.Error(), 400)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcClaimQuestRewardResponse{
// 		Success: true,
// 		Message: "任务奖励领取成功",
// 	}

// 	resultStr, err := tool.ProtoToBase64(result)
// 	if err != nil {
// 		return "", runtime.NewError("Failed to encode response", 500)
// 	}
// 	return resultStr, nil
// }

// RpcGetTrainerQuests RPC函数：获取训练师任务列表
func RpcGetTrainerQuests(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师任务列表
	trainerQuests, err := GetTrainerQuestList(ctx, logger, tx, trainerInfo.Id)
	if err != nil {
		return "", runtime.NewError("获取训练师任务列表失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetTrainerQuestsResponse{
		Success:       true,
		Message:       "获取训练师任务列表成功",
		TrainerQuests: trainerQuests,
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcUpdateYarnTitleQuestProgress RPC函数：更新任务的Yarn进度
func RpcUpdateYarnTitleQuestProgress(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.UpdateQuestThroughPointInfoRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 更新任务进度
	// 这里需要根据具体的进度类型来确定progressKey
	// progressKey := "general_progress" // 默认进度键
	err = UpdateYarnTitleQuestProgress(ctx, logger, tx, trainerInfo.Id, request.QuestId, request.ThroughPointInfo)
	if err != nil {
		return "", runtime.NewError("更新任务进度失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.UpdateYarnThroughPointInfoResponse{
		Success: true,
		Message: "任务进度更新成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

func RpcGetActiveTrainerQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}
	questId := payload
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }
	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, trainerInfo.Id, questId)
	if err != nil {
		return "", runtime.NewError("获取训练师任务失败: "+err.Error(), 400)
	}
	trainerQuest = ConvertQuestInfoToSimpleQuestInfo(trainerQuest)
	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetTrainerQuestsResponse{
		Success:       true,
		Message:       "获取训练师任务成功",
		TrainerQuests: []*MainServer.TrainerQuest{trainerQuest},
		CurrentTs:     time.Now().Unix(),
	}

	return tool.ProtoToBase64(result)
}

func RpcCancelTrainerQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}
	request := &MainServer.QuestOperationRequest{}
	err := tool.Base64ToProto(payload, request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	if request.OperationType != MainServer.QuestOperationType_QuestOperationType_cancel {
		return "", runtime.NewError("无效的请求数据: 操作类型错误", 400)
	}
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }
	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 取消训练师任务
	err = tryCancelTrainerQuest(ctx, logger, tx, trainerInfo.Id, request.QuestId)
	if err != nil {
		return "", runtime.NewError("取消训练师任务失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.QuestOperationResponse{
		Success: true,
		Message: "取消训练师任务成功",
	}

	return tool.ProtoToBase64(result)
}
func RpcStartBattleQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}
	request := &MainServer.QuestOperationRequest{}
	err := tool.Base64ToProto(payload, request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	if request.OperationType != MainServer.QuestOperationType_QuestOperationType_start {
		return "", runtime.NewError("无效的请求数据: 操作类型错误", 400)
	}
	// if err != nil {
	// 	return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	// }
	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userID)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	matchId, err := tryStartBattleByQuest(ctx, logger, tx, nk, trainerInfo, request.QuestId)
	if err != nil {
		return "", runtime.NewError("开始训练师任务失败: "+err.Error(), 400)
	}
	// trainerQuest = ConvertQuestInfoToSimpleQuestInfo(trainerQuest)
	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.QuestOperationResponse{
		Success: true,
		Message: "开始训练师任务成功",
		Value:   matchId,
	}

	return tool.ProtoToBase64(result)
}

// func RpcUpdateYarnTitleQuestProgress(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcUpdateQuestProgressRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainerInfo := tool.GetActiveTrainerByUid(userID)
// 	if trainerInfo == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 更新任务进度
// 	// 这里需要根据具体的进度类型来确定progressKey
// 	progressKey := "general_progress" // 默认进度键
// 	err = UpdateQuestProgress(ctx, logger, tx, trainerInfo.Id, request.QuestId, progressKey, request.Progress)
// 	if err != nil {
// 		return "", runtime.NewError("更新任务进度失败: "+err.Error(), 400)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcUpdateQuestProgressResponse{
// 		Success: true,
// 		Message: "任务进度更新成功",
// 	}

// 	resultStr, err := tool.ProtoToBase64(result)
// 	if err != nil {
// 		return "", runtime.NewError("Failed to encode response", 500)
// 	}
// 	return resultStr, nil
// }
