package trainer

import (
	"context"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/tidwall/buntdb"
	// "google.golang.org/protobuf/encoding/protojson"
)

// var db *buntdb.DB

// // 初始化 BuntDB 并创建空间索引
//
//	func initBuntdb() {
//		var err error
//		db, err = buntdb.Open(":memory:") // 使用内存数据库
//		if err != nil {
//			panic(fmt.Sprintf("无法打开 BuntDB 数据库: %v", err))
//		}
//		db.Indexes()
//	}
func TickBroadcast() {
	// for each player id p {
	//    // 获取 AOI 列表
	//    list := aoiManager.BroadcastNearbyForPlayer(mapId, p, true)
	//    // 将 list 序列化为 AOIBroadcast protobuf，发送给客户端 p
	// }
}

// UpdateUserLoc 函数，用于更新用户位置并返回附近的用户
func updateUserLoc(ctx context.Context, logger runtime.Logger, trainer *MainServer.Trainer, region string, point string) (*MainServer.TrainersResult, error) {
	tid := trainer.Id
	// regionID := userLoc.ReginId
	regionIndexName := "loc_idx_" + region
	err := tool.CreateRectIndexIfNeed(regionIndexName)
	if err != nil {
		return nil, err
	}
	// if !db.IndexExist(regionIndexName) {
	// 	db.CreateSpatialIndex(regionIndexName, regionIndexName+":*", buntdb.IndexRect)
	// }
	// key := fmt.Sprintf("region:%s:loc:%s", regionID, tid)
	tidStr := strconv.FormatInt(int64(trainer.Id), 10)
	dbkey := regionIndexName + ":" + tidStr
	// Step 1: 更新自己的 loc 到 BuntDB
	// locData, err := json.Marshal(userLoc)
	// if err != nil {
	// 	logger.Error("无法序列化用户 %s 的位置数据: %v", tid, err)
	// 	return nil, err
	// }

	// 更新用户位置信息到指定的 RegionId 区域
	// err := db.Update(func(tx *buntdb.Tx) error {
	// 	// 使用 BuntDB 的地理空间索引存储位置信息
	// 	_, _, err := tx.Set(dbkey, point, &buntdb.SetOptions{
	// 		Expires: false,
	// 		// Rect:    buntdb.Rect(float64(userLoc.X), float64(userLoc.Y), float64(userLoc.X), float64(userLoc.Y)), // 空间索引
	// 	})
	// 	return err
	// })
	err = tool.Set(dbkey, point)
	if err != nil {
		logger.Error("无法更新用户 %s 的位置信息到 BuntDB: %v", tid, err)
		return nil, err
	}

	logger.Info("用户 %s 的位置信息已更新到 Region %s, x y z: %s", tid, region, point)

	// Step 2: 获取附近用户 loc（同一个 Region 块）
	var nearbyTids []string
	maxUsers := 100 // 限制返回的最多用户数量
	// searchPoint := fmt.Sprintf("[%d %d]", userLoc.X, userLoc.Y) // 用户当前的点

	// 从当前用户的 RegionId 查询附近用户的位置信息
	err = tool.DB().View(func(tx *buntdb.Tx) error {
		count := 0 // 用于记录已找到的用户数量

		// 使用 Nearby 函数查找附近用户（k-Nearest Neighbors）
		err := tx.Nearby(regionIndexName, point, func(key, val string, dist float64) bool {
			if dist > 4900 { //距离超过70米的不返回
				return false
			}
			// 忽略当前用户自己
			// if key == tidStr {
			// 	return true
			// }
			nearbyTids = append(nearbyTids, strings.Split(key, ":")[1])
			// 反序列化附近用户的位置数据
			// var nearbyUser MainServer.Trainer = *tool.GetUserActiveTrainer()

			// err := json.Unmarshal([]byte(val), &nearbyUser)
			// if err != nil {
			// 	logger.Error("反序列化附近用户数据失败: %v", err)
			// 	return true
			// }

			// nearbyUsers = append(nearbyUsers, &nearbyUser)
			count++

			// 如果找到的用户数量达到限制，则停止搜索
			if count >= maxUsers {
				return false // 返回 false 来停止查询
			}

			return true // 继续查询下一个
		})

		return err
	})
	if err != nil {
		logger.Error("Nearby err: %v", err)
		return nil, err
	}
	//脱敏后的数据
	nearbyUsers := tool.GetDesensitizeActiveTrainersByIds(nearbyTids)
	result := &MainServer.TrainersResult{
		Trainers:  nearbyUsers,
		CurrentTs: time.Now().Unix(),
	}

	logger.Info("附近用户数量: %d", len(nearbyUsers))

	// Step 3: 返回附近用户的位置信息
	return result, nil
}
