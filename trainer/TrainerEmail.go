package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/email"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"math"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RpcGetEmailList 获取邮件列表RPC处理函数
func RpcGetEmailList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}

	// 解析请求
	var request MainServer.EmailListRequest
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	emails, err := email.GetEmailList(ctx, logger, db, trainer.Id, request.Page, request.PageSize, !request.IsFilterAll)
	if err != nil {
		return "", err
	}
	response := &MainServer.EmailListResponse{
		Emails: emails,
	}
	return tool.ProtoToBase64(response)
}

// RpcEmailOp 邮件操作RPC处理函数
func RpcEmailOperation(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}

	// 解析请求
	var request MainServer.EmailOpRequest
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	// 根据操作类型处理
	switch request.Op {
	case MainServer.EmailOp_EMAIL_OP_READ:
		// 批量标记为已读
		for _, emailId := range request.EmailIds {
			if err := email.ReadEmail(ctx, logger, db, emailId, trainer.Id); err != nil {
				logger.Error("标记邮件已读失败: %v", err)
				// 继续处理其他邮件
				continue
			}
		}
	case MainServer.EmailOp_EMAIL_OP_DELETE:
		// 批量删除
		if err := email.DeleteEmails(ctx, logger, db, request.EmailIds, trainer.Id); err != nil {
			return "", err
		}
	case MainServer.EmailOp_EMAIL_OP_RECEIVE_ATTACHMENT:
		for _, emailId := range request.EmailIds {
			if err := ReceiveEmailAttachments(ctx, logger, db, emailId, trainer); err != nil {
				return "", err
			}
		}
	default:
		return "", runtime.NewError("不支持的操作类型", 400)
	}

	return "{}", nil
}

func ReceiveEmailAttachments(ctx context.Context, logger runtime.Logger, db *sql.DB, emailId int64, trainer *MainServer.Trainer) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()

	gemail, err := email.GetEmailById(ctx, logger, tx, emailId, trainer.Id)
	if err != nil {
		return err
	}
	if time.Now().UnixMilli()-gemail.SendTime > 1000*60*60*24*15 {
		return fmt.Errorf("邮件已过期")
	}
	if gemail.Status == MainServer.EmailStatus_EMAIL_STATUS_RECEIVED_ATTACHMENT {
		return fmt.Errorf("邮件已领取")
	}
	if gemail.Status == MainServer.EmailStatus_EMAIL_STATUS_DELETED {
		return fmt.Errorf("邮件已删除")
	}
	if gemail.Attachments == nil {
		return fmt.Errorf("邮件没有附件")
	}
	err = email.ReceiveEmailAttachmentsWithTx(ctx, logger, tx, emailId, trainer.Id)
	if err != nil {
		return err
	}
	if gemail.EmailType == MainServer.EmainType_EmainType_Deposit {

		//计算gemail.SendTime距离现在过去了多久每一个小时收费100coin
		ts := time.Now().UnixMilli() - gemail.SendTime
		//hour向上取整
		hour := int64(math.Ceil(float64(ts) / (1000 * 60 * 60)))
		coin := hour * 100
		if coin > 10000 {
			coin = 10000
		}
		if hour > 0 {
			_, err = UpdateTrainerCoinOnly(ctx, logger, tx, trainer.Id, -coin)
			if err != nil {
				return err
			}
		}
	}
	for _, attachItem := range gemail.Attachments.Items {
		//TODO
		localItem, exists := item.GetItemByName(attachItem.ItemName)
		if !exists {
			return runtime.NewError("Failed to query item info", 404)
		}
		if err := inventory.AddItemToInventory(ctx, tx, trainer.Id, attachItem.ItemName, localItem.InventoryType, attachItem.Count, attachItem.ItemSaleType, attachItem.TeamType); err != nil {
			return err
		}
	}
	if len(gemail.Attachments.Pokemons) > 0 {
		err = SavePokesToNormalBox(ctx, logger, trainer, tx, gemail.Attachments.Pokemons)
		if err != nil {
			return err
		}
	}
	for _, cloth := range gemail.Attachments.Clothes {
		err = AddTrainerCloth(ctx, logger, tx, trainer.Id, cloth.Type, cloth.Name)
		if err != nil {
			return err
		}
	}
	for _, equipment := range gemail.Attachments.Equipments {
		//TODO 装备的属性调整，不然保存的时候数据会有一点问题，比如时间不对
		equipment.Tid = trainer.Id
		err = AddTrainerEquipment(ctx, tx, equipment)
		if err != nil {
			return err
		}
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}
	return nil
}
