package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// ExampleUpdateTrainerPartialFields 展示如何安全地更新trainer的部分字段
func ExampleUpdateTrainerPartialFields(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	trainerId := int64(12345)

	// 示例1: 使用便利函数更新单个字段
	logger.Info("=== 示例1: 便利函数更新单个字段 ===")

	// 更新poke_ids
	newPokeIds := []string{"1001", "1002", "1003"}
	trainer, err := UpdateTrainerPokeIdsOnly(ctx, logger, tx, trainerId, newPokeIds)
	if err != nil {
		logger.Error("更新poke_ids失败: %v", err)
		return err
	}
	logger.Info("成功更新poke_ids: %v", trainer.PokeIds)

	// 更新team
	newTeam := MainServer.TrainerTeam_TRAINER_TEAM_Rocket
	trainer, err = UpdateTrainerTeamOnly(ctx, logger, tx, trainerId, newTeam)
	if err != nil {
		logger.Error("更新team失败: %v", err)
		return err
	}
	logger.Info("成功更新team: %v", trainer.Team)

	// 更新online_time
	newOnlineTime := int64(3600) // 1小时
	trainer, err = UpdateTrainerOnlineTimeOnly(ctx, logger, tx, trainerId, newOnlineTime)
	if err != nil {
		logger.Error("更新online_time失败: %v", err)
		return err
	}
	logger.Info("成功更新online_time: %d", trainer.OnlineTime)

	// 示例2: 使用通用函数同时更新多个字段
	logger.Info("=== 示例2: 同时更新多个字段 ===")

	// 创建action_info
	actionInfo := &MainServer.TrainerActionInfo{
		Action: MainServer.TrainerActionType_battle,
		Loc: &MainServer.TrainerLoc{
			MainLandType: MainServer.MainLandType_MainLand_HeartGold,
			X:            100,
			Y:            200,
			Z:            0,
		},
	}

	// 创建team_info
	teamInfo := &MainServer.TrainerOnTeamInfo{
		Contribution: 1000,
		Level:        5,
		Exp:          250,
	}

	// 创建follow_poke
	followPoke := &MainServer.TrainerFollowPoke{
		Pokes: []*MainServer.TrainerFollowPokeInfo{
			{
				Id:     1001,
				Name:   "pikachu",
				Shiny:  0,
				Gender: MainServer.Gender_M,
			},
		},
		Ride: MainServer.TrainerRideType_Ride_None,
	}

	// 同时更新多个字段
	updates := &TrainerPartialUpdate{
		ActionInfo: actionInfo,
		TeamInfo:   teamInfo,
		FollowPoke: followPoke,
		OnlineTime: &newOnlineTime,
	}

	trainer, err = UpdateTrainerPartialFieldsSafe(ctx, logger, tx, trainerId, updates)
	if err != nil {
		logger.Error("多字段更新失败: %v", err)
		return err
	}
	logger.Info("成功更新多个字段")
	logger.Info("- Action: %v", trainer.ActionInfo.Action)
	logger.Info("- Team Contribution: %d", trainer.TeamInfo.Contribution)
	logger.Info("- Follow Poke Count: %d", len(trainer.FollowPoke.Pokes))
	logger.Info("- Online Time: %d", trainer.OnlineTime)

	// 示例3: 错误处理
	logger.Info("=== 示例3: 错误处理 ===")

	// 尝试传入空的更新对象
	emptyUpdates := &TrainerPartialUpdate{}
	_, err = UpdateTrainerPartialFieldsSafe(ctx, logger, tx, trainerId, emptyUpdates)
	if err != nil {
		if runtimeErr, ok := err.(*runtime.Error); ok && runtimeErr.Code == 400 {
			logger.Info("预期的错误：没有字段需要更新")
		} else {
			logger.Error("意外的错误: %v", err)
		}
	}

	// 尝试传入nil更新对象
	_, err = UpdateTrainerPartialFieldsSafe(ctx, logger, tx, trainerId, nil)
	if err != nil {
		if runtimeErr, ok := err.(*runtime.Error); ok && runtimeErr.Code == 400 {
			logger.Info("预期的错误：更新对象为nil")
		} else {
			logger.Error("意外的错误: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Info("所有部分字段更新操作完成")
	return nil
}

// ExampleBatchUpdate 展示如何批量更新多个trainer的字段
func ExampleBatchUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	logger.Info("=== 批量更新示例 ===")

	// 要更新的trainer列表
	trainerIds := []int64{12345, 12346, 12347}

	// 批量更新online_time
	newOnlineTime := int64(7200) // 2小时
	for _, tid := range trainerIds {
		trainer, err := UpdateTrainerOnlineTimeOnly(ctx, logger, tx, tid, newOnlineTime)
		if err != nil {
			logger.Error("更新trainer %d的online_time失败: %v", tid, err)
			continue
		}
		logger.Info("成功更新trainer %d的online_time: %d", tid, trainer.OnlineTime)
	}

	// 批量更新team
	newTeam := MainServer.TrainerTeam_TRAINER_TEAM_Magma
	for _, tid := range trainerIds {
		trainer, err := UpdateTrainerTeamOnly(ctx, logger, tx, tid, newTeam)
		if err != nil {
			logger.Error("更新trainer %d的team失败: %v", tid, err)
			continue
		}
		logger.Info("成功更新trainer %d的team: %v", tid, trainer.Team)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Info("批量更新完成")
	return nil
}

// ExampleOptimisticLockConflict 展示乐观锁冲突的处理
func ExampleOptimisticLockConflict(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	trainerId := int64(12345)

	logger.Info("=== 乐观锁冲突处理示例 ===")

	// 模拟并发更新的情况
	for i := 0; i < 3; i++ {
		// 开始事务
		tx, err := db.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %v", err)
		}

		// 尝试更新online_time
		newOnlineTime := int64(1000 + i*100)
		trainer, err := UpdateTrainerOnlineTimeOnly(ctx, logger, tx, trainerId, newOnlineTime)
		if err != nil {
			tx.Rollback()
			// 检查是否是乐观锁冲突
			if runtimeErr, ok := err.(*runtime.Error); ok && runtimeErr.Code == 409 {
				logger.Warn("第%d次尝试发生乐观锁冲突，重试中...", i+1)
				continue
			}
			return fmt.Errorf("update failed: %v", err)
		}

		// 提交事务
		if err := tx.Commit(); err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to commit transaction: %v", err)
		}

		logger.Info("第%d次尝试成功，当前online_time: %d", i+1, trainer.OnlineTime)
		break
	}

	return nil
}

// ExampleComplexUpdate 展示复杂的字段更新场景
func ExampleComplexUpdate(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	trainerId := int64(12345)

	logger.Info("=== 复杂更新场景示例 ===")

	// 场景：玩家完成了一个任务，需要更新多个字段
	// 1. 获得新的宝可梦，更新poke_ids
	// 2. 获得徽章奖励，更新badges
	// 3. 加入新的队伍，更新team和team_info
	// 4. 更新在线时间

	// 创建新的badges
	newBadges := &MainServer.TrainerBadges{
		// 假设这里有一些徽章数据
	}

	// 创建新的team_info
	newTeamInfo := &MainServer.TrainerOnTeamInfo{
		Contribution: 500,
		Level:        1,
		Exp:          0,
	}

	// 新的poke_ids（添加了新的宝可梦）
	newPokeIds := []string{"1001", "1002", "1003", "1004"}

	// 新的team
	newTeam := MainServer.TrainerTeam_TRAINER_TEAM_Aqua

	// 新的online_time
	newOnlineTime := int64(5400) // 1.5小时

	// 一次性更新所有字段
	updates := &TrainerPartialUpdate{
		PokeIds:    newPokeIds,
		Team:       &newTeam,
		Badges:     newBadges,
		TeamInfo:   newTeamInfo,
		OnlineTime: &newOnlineTime,
	}

	trainer, err := UpdateTrainerPartialFieldsSafe(ctx, logger, tx, trainerId, updates)
	if err != nil {
		logger.Error("复杂更新失败: %v", err)
		return err
	}

	logger.Info("复杂更新成功:")
	logger.Info("- Poke IDs: %v", trainer.PokeIds)
	logger.Info("- Team: %v", trainer.Team)
	logger.Info("- Team Contribution: %d", trainer.TeamInfo.Contribution)
	logger.Info("- Online Time: %d", trainer.OnlineTime)

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Info("复杂更新场景完成")
	return nil
}
