package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/equipment"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RpcAddEquipment 添加装备
func RpcAddEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.Equipment
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 设置装备的训练师ID
	request.Tid = trainer.Id

	// 添加装备
	err = AddTrainerEquipment(ctx, tx, &request)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to add equipment: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.AddEquipmentResponse{
		Success: true,
		Message: "装备添加成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcUpdateEquipment 更新装备
func RpcUpdateEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.Equipment
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 检查装备是否属于该训练师
	if request.Tid != trainer.Id {
		return "", runtime.NewError("装备不属于该训练师", 403)
	}

	// 更新装备
	err = UpdateTrainerEquipment(ctx, tx, &request)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to update equipment: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.AddEquipmentResponse{
		Success: true,
		Message: "装备更新成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcDeleteEquipment 删除装备
func RpcDeleteEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.DeleteEquipmentParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}
	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainerInfo := tool.GetActiveTrainerByUid(userId)
	if trainerInfo == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 检查装备是否属于该训练师
	filter := &MainServer.EquipmentFilter{
		Id:  request.EquipmentId,
		Tid: trainerInfo.Id,
	}
	equipments, err := QueryTrainerEquipments(ctx, tx, filter)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to query equipment: %v", err), 500)
	}
	if len(equipments) == 0 {
		return "", runtime.NewError("装备不存在或不属于该训练师", 404)
	}

	// 删除装备
	err = DeleteTrainerEquipment(ctx, tx, request.EquipmentId)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to delete equipment: %v", err), 500)
	}
	if trainerInfo.Decoration != nil {

		unequipEquipmentType := MainServer.EquipmentType_equipment_nor
		switch request.EquipmentType {
		case MainServer.EquipmentType_equipment_title:
			if trainerInfo.Decoration.EquipmentTitle != nil && trainerInfo.Decoration.EquipmentTitle.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_title
			}
		case MainServer.EquipmentType_equipment_card:
			if trainerInfo.Decoration.EquipmentCard != nil && trainerInfo.Decoration.EquipmentCard.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_card
			}
		case MainServer.EquipmentType_equipment_ride:
			if trainerInfo.Decoration.EquipmentRide != nil && trainerInfo.Decoration.EquipmentRide.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_ride
			}
		case MainServer.EquipmentType_equipment_pokeball:
			if trainerInfo.Decoration.EquipmentPokeball != nil && trainerInfo.Decoration.EquipmentPokeball.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_pokeball
			}
		case MainServer.EquipmentType_equipment_badge:
			if trainerInfo.Decoration.EquipmentBadge != nil && trainerInfo.Decoration.EquipmentBadge.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_badge
			}
		case MainServer.EquipmentType_equipment_amulet:
			if trainerInfo.Decoration.EquipmentAmulet != nil && trainerInfo.Decoration.EquipmentAmulet.Id == request.EquipmentId {
				unequipEquipmentType = MainServer.EquipmentType_equipment_amulet
			}
		}
		if unequipEquipmentType != MainServer.EquipmentType_equipment_nor {
			err = UnequipFromTrainerDecoration(ctx, logger, tx, trainerInfo, unequipEquipmentType)
			if err != nil {
				return "", runtime.NewError(fmt.Sprintf("Failed to unequip from decoration: %v", err), 500)
			}
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.OprationEquipmentResponse{
		Success: true,
		Message: "装备删除成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcQueryEquipments 查询装备
func RpcQueryEquipments(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.QueryEquipmentsParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 构建查询过滤器
	filter := &MainServer.EquipmentFilter{
		Tid:           trainer.Id,
		EquipmentName: request.EquipmentName,
		EquipmentType: request.EquipmentType,
		Status:        request.Status,
		UpdateTs:      request.UpdateTs,
	}

	// 查询装备
	equipments, err := QueryTrainerEquipments(ctx, tx, filter)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to query equipments: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.QueryEquipmentsResponse{
		Success:    true,
		Equipments: equipments,
	}

	return tool.ProtoToBase64(response)
}

// RpcSaleEquipment 上架装备
func RpcSaleEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.SaleEquipmentParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 上架装备
	err = SaleTrainerEquipment(ctx, tx, trainer.Id, &request)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to sale equipment: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.OprationEquipmentResponse{
		Success: true,
		Message: "装备上架成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcUnsaleEquipment 下架装备
func RpcUnsaleEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.UnsaleEquipmentParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 下架装备
	err = UnsaleTrainerEquipment(ctx, tx, trainer.Id, &request)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to unsale equipment: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.OprationEquipmentResponse{
		Success: true,
		Message: "装备下架成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcEquipToDecoration 装备到装饰
func RpcEquipToDecoration(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	// var request struct {
	// 	EquipmentId   int64 `json:"equipment_id"`
	// 	EquipmentType int32 `json:"equipment_type"`
	// }
	var request MainServer.EquipToDecorationParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}
	// if err := json.Unmarshal([]byte(payload), &request); err != nil {
	// 	return "", runtime.NewError("Invalid request payload", 400)
	// }

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 装备到装饰
	err = EquipToTrainerDecoration(ctx, logger, tx, trainer, request.EquipmentId, MainServer.EquipmentType(request.EquipmentType))
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to equip to decoration: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.AddEquipmentResponse{
		Success: true,
		Message: "装备成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcUnequipFromDecoration 从装饰卸下装备
func RpcUnequipFromDecoration(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	// var request struct {
	// 	EquipmentType int32 `json:"equipment_type"`
	// }
	var request MainServer.UnequipToDecorationParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}
	// if err := json.Unmarshal([]byte(payload), &request); err != nil {
	// 	return "", runtime.NewError("Invalid request payload", 400)
	// }

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 从装饰卸下装备
	err = UnequipFromTrainerDecoration(ctx, logger, tx, trainer, MainServer.EquipmentType(request.EquipmentType))
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to unequip from decoration: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.AddEquipmentResponse{
		Success: true,
		Message: "卸下装备成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcFortifyEquipment 强化装备
func RpcFortifyEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	// var request struct {
	// 	EquipmentId  int64 `json:"equipment_id"`
	// 	FortifyCount int32 `json:"fortify_count"`
	// }
	var request MainServer.EquipmentFortifyParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}
	// if err := json.Unmarshal([]byte(payload), &request); err != nil {
	// 	return "", runtime.NewError("Invalid request payload", 400)
	// }

	// 获取用户信息
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("User ID not found", 401)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 检查装备是否属于该训练师
	filter := &MainServer.EquipmentFilter{
		Id:  request.EquipmentId,
		Tid: trainer.Id,
	}
	equipments, err := QueryTrainerEquipments(ctx, tx, filter)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to query equipment: %v", err), 500)
	}
	if len(equipments) == 0 {
		return "", runtime.NewError("装备不存在或不属于该训练师", 404)
	}

	subEquipment, err := equipment.GetEquipmentById(ctx, tx, request.SubEquipmentId)
	if err != nil {
		return "", err
	}
	isEquiped := false
	switch subEquipment.EquipmentType {
	case MainServer.EquipmentType_equipment_title:
		if trainer.Decoration.EquipmentTitle != nil && trainer.Decoration.EquipmentTitle.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	case MainServer.EquipmentType_equipment_card:
		if trainer.Decoration.EquipmentCard != nil && trainer.Decoration.EquipmentCard.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	case MainServer.EquipmentType_equipment_ride:
		if trainer.Decoration.EquipmentRide != nil && trainer.Decoration.EquipmentRide.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	case MainServer.EquipmentType_equipment_pokeball:
		if trainer.Decoration.EquipmentPokeball != nil && trainer.Decoration.EquipmentPokeball.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	case MainServer.EquipmentType_equipment_badge:
		if trainer.Decoration.EquipmentBadge != nil && trainer.Decoration.EquipmentBadge.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	case MainServer.EquipmentType_equipment_amulet:
		if trainer.Decoration.EquipmentAmulet != nil && trainer.Decoration.EquipmentAmulet.Id == request.EquipmentId {
			isEquiped = true
		}
		break
	}
	if isEquiped {
		return "", runtime.NewError("装备已装备，无法作为素材", 400)
	}

	// 强化装备
	err = FortifyTrainerEquipment(ctx, tx, trainer.Id, request.EquipmentId, &request)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to fortify equipment: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.AddEquipmentResponse{
		Success: true,
		Message: "装备强化成功",
	}

	return tool.ProtoToBase64(response)
}

// RpcGetAllEquipments 获取所有装备（管理员用）
func RpcGetAllEquipments(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.GetAllEquipmentsParam
	if err := tool.Base64ToProto(payload, &request); err != nil {
		return "", runtime.NewError("Invalid request payload", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取所有装备
	equipments, err := equipment.GetAllEquipments(ctx, tx, request.UpdateTs)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("Failed to get all equipments: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回响应
	response := &MainServer.GetAllEquipmentsResponse{
		Success:    true,
		Equipments: equipments,
	}

	return tool.ProtoToBase64(response)
}
