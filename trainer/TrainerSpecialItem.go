package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	ItemNameMewtwoQuestSlate  = "mewtwoquestslate"
	ItemNameMewtwoMailSpecial = "mewtwomail_special"
)

func IsTrainerSpecialItem(itemName string) bool {
	if itemName == ItemNameMewtwoQuestSlate { //超梦任务石板
		return true
	}
	return false
}

// RpcTryUseTrainerSpecialItem 使用训练师特殊道具
func UseTrainerSpecialItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, localItem *MainServer.LocalItem) (bool, error) {
	if localItem.Name == ItemNameMewtwoQuestSlate { //超梦任务石板
		_, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, localItem.Name, 1)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, localItem.Name, err)
			return false, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		err = addItemStatus(ctx, logger, tx, trainer, &MainServer.LocalItem{Name: ItemNameMewtwoMailSpecial, Duration: 0})
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, localItem.Name, err)
			return false, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		_, err = AcceptQuest(ctx, logger, tx, trainer, "20010", false)
		if err != nil {
			logger.Error("用户 %s 接受任务失败: %v", trainer.Id, err)
			return false, runtime.NewError(fmt.Sprintf("任务接受失败: %v", err), 500)
		}

		// err = inventory.AddItem(ctx, tx, trainer.Id, ItemNameMewtwoMailSpecial, 1)
		// if err != nil {
		// 	logger.Error("用户 %s 添加道具 %s 失败: %v", trainer.Id, ItemNameMewtwoMailSpecial, err)
		// 	return false, runtime.NewError(fmt.Sprintf("道具添加失败: %v", err), 500)
		// }
		return true, nil
	}
	return false, nil
}
