package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/config"
	"go-nakama-poke/inventory"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

func Initiation(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	initTrainers(ctx, logger, db)
	initPokeBoxs(ctx, logger, db)
	initTrainerQuestTable(ctx, logger, db)
}

// 随身
func RpcGetTrainerAroundPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, MainServer.PokeBoxType_around, 0, 0)
	tool.AddOrUpdateTrainerAroundPokes(trainer.Id, singlePokeBox.Pokes)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	trainer.SessionInfo.BattlePokes = singlePokeBox.Pokes
	result := &MainServer.PokesResult{
		Pokes: singlePokeBox.Pokes,
	}
	return tool.ProtoToBase64(result)
}

func RpcGetTrainerBoxAndPokesByBoxInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	var boxInfo = &MainServer.SinglePokeBoxParam{}
	err = tool.Base64ToProto(payload, boxInfo)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, boxInfo.BoxType, boxInfo.Index, boxInfo.UpdateTs)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(singlePokeBox)
}

//	func RpcGetTrainerPokesByBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
//		trainer := tool.GetActiveTrainerByCtx(ctx)
//		if trainer == nil {
//			return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
//		}
//		// Step 1: 开始事务
//		tx, err := db.BeginTx(ctx, nil)
//		if err != nil {
//			logger.Error("failed to begin transaction: %v", err)
//			return "", fmt.Errorf("failed to begin transaction: %w", err)
//		}
//		defer tx.Rollback()
//		singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, MainServer.PokeBoxType_around, 0, 0)
//		if err != nil {
//			return "", err
//		}
//		// 提交事务
//		if err := tx.Commit(); err != nil {
//			logger.Error("事务提交失败: %v", err)
//			return "", fmt.Errorf("failed to commit transaction: %w", err)
//		}
//		result := &MainServer.PokesResult{
//			Pokes: singlePokeBox.Pokes,
//		}
//		return tool.ProtoToBase64(result)
//	}

// 需要确定是否需要加入最后的战斗或者最后的party
func RpcSelectTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	tid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}

	userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		// If user ID is not found, RPC was called without a session token.
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_AuthenticationFail)
	}
	sessionID, ok := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)
	if !ok {
		// If session ID is not found, RPC was not called over a connected socket.
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_AuthenticationFail)
	}
	trainer, err := SelectTrainerProto(ctx, db, tid)
	if err != nil || trainer.Uid != userID {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 检查并扩容盒子
	if err := checkAndExpandBoxes(ctx, logger, tx, trainer); err != nil {
		return "", err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

	nowTs := time.Now().UnixMilli()
	if nowTs-trainer.SessionInfo.SessionEndTs < config.ReconnectTime {
		//return 旧的 party
	}

	trainer.SessionInfo.SessionEndTs = 0
	trainer.SessionInfo.LastOnlineTs = nowTs
	if tool.GlobalAOIManager.JoinAOIStream(nk, userID, sessionID, logger) != nil {
		return "", runtime.NewError("join aoi stream err", 400)
	}
	tool.SetUserActiveTrainer(userID, trainer)
	tool.GetGlobalPartyMap().Remove(trainer.Id)
	trainerSelectResult := &MainServer.TrainerSelectResult{
		Trainer: trainer,
		Config: &MainServer.GameConfig{
			OneUserMaxBoxCount:        oneUserMaxBoxCount,
			OneUserSpecialMaxBoxCount: oneUserSpecialMaxBoxCount,
		},
	}
	return tool.ProtoToBase64(trainerSelectResult)
}
func RpcGetTrainerInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 查询训练师数据
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	// 返回训练师信息
	return tool.ProtoToBase64(trainer)
}
func RpcGetSafeTrainerInfos(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	var infoParam = &MainServer.GetTrainerInfosParam{}
	err := tool.Base64ToProto(payload, infoParam)
	safeTrainers := make([]*MainServer.Trainer, 0, len(infoParam.Uids))
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	if len(infoParam.Tids) > 0 {
		trainer := tool.GetSafeActiveTrainersByIds(infoParam.Tids)
		safeTrainers = append(safeTrainers, trainer...)
		// for _, v := range infoParam.Uids {

		// 	if trainer != nil {
		// 		safeTrainers = append(safeTrainers, tool.CopySafeTrainer(trainer))
		// 		// selectUids = append(selectUids, trainer.Uid)
		// 	}
		// }
	}
	if len(infoParam.Uids) > 0 {
		trainers := tool.GetSafeActiveTrainersByUids(infoParam.Uids)
		safeTrainers = append(safeTrainers, trainers...)
	}
	// selectUids := infoParam.Uids //[]string{"1", "2", "3"}
	// for _, trainer := range trainers {
	// 	safeTrainers = append(safeTrainers, tool.CopySafeTrainer(trainer))
	// }
	// trainers, err := selectTrainersByIDs(ctx, db, []int64{1, 2, 3})
	// if err != nil {
	// 	return "", fmt.Errorf("failed to retrieve trainers: %v", err)
	// }
	// safeTrainers := make([]*MainServer.Trainer, 0, len(trainers))
	// for _, trainer := range trainers {
	// 	safeTrainers = append(safeTrainers, tool.CopySafeTrainer(trainer))
	// }
	infos := &MainServer.TrainerInfosResult{
		Trainers: safeTrainers,
	}
	// 查询训练师数据
	// trainer := tool.GetActiveTrainerByCtx(ctx)
	// if trainer == nil {
	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	// }
	// 返回训练师信息
	return tool.ProtoToBase64(infos)
}

func RpcAllTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 查询训练师数据
	trainers, err := SelectTrainersByUID(ctx, db, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve trainers: %v", err)
	}
	// 将 trainers 转换为 JSON
	// trainersJSON, err := json.Marshal(trainers)
	// if err != nil {
	// 	return "", fmt.Errorf("failed to convert trainers to JSON: %v", err)
	// }
	result := &MainServer.TrainersResult{
		Trainers:  trainers,
		CurrentTs: time.Now().Unix(),
	}
	// trainersJSON, err = tool.ProtoToBase64(result)

	// 返回 JSON 字符串
	return tool.ProtoToBase64(result)
}

func RpcTrainerNameValid(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	unique, err := isNameValid(ctx, db, payload)
	if err != nil {
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	if !unique {
		return "", fmt.Errorf("trainer Name err")
	}
	return "", nil
}
func RpcUploadAOIUserLoc(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainerLocInfo := &MainServer.TrainerLoc{}
	err := tool.Base64ToProto(payload, trainerLocInfo)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	trainer.SessionInfo.SessionEndTs = 0
	if trainer.SessionInfo.LocInfo == nil {
		trainer.SessionInfo.LocInfo = &MainServer.TrainerLocInfo{}
	}
	trainer.SessionInfo.LocInfo.Loc = trainerLocInfo
	trainer.ActionInfo.Loc = trainerLocInfo
	if trainerLocInfo.MainLandType != MainServer.MainLandType_MainLand_None {
		trainer.ActionInfo.LastMainLand.Loc = trainerLocInfo
		if time.Now().UnixMilli()-trainer.ActionInfo.UpdateTs > config.TrainerLocalUpdateInterval {
			trainer.ActionInfo.UpdateTs = time.Now().UnixMilli()
			// 开始事务
			tx, err := db.BeginTx(ctx, nil)
			if err != nil {
				return "", fmt.Errorf("failed to begin transaction: %v", err)
			}
			// defer tx.Rollback()
			trainerNew, err := UpdateTrainerActionInfoOnly(ctx, logger, tx, trainer.Id, trainer.ActionInfo)
			if err == nil {
				trainer.UpdateTs = trainerNew.UpdateTs
			}
			// 提交事务
			if err := tx.Commit(); err != nil {
				tx.Rollback()
				// return "", fmt.Errorf("failed to commit transaction: %v", err)
			}
		}
		// if trainerLocInfo.PcName != "" {
		// 	trainer.ActionInfo.LastMainLand.LastPcName = trainerLocInfo.PcName
		// }
	}
	nowTime := time.Now().UnixMilli()
	if trainer.SessionInfo.LastOnlineTs == 0 {
		trainer.SessionInfo.LastOnlineTs = nowTime
	} else if nowTime-trainer.SessionInfo.LastOnlineTs < 3000 { //距离最后一次更新如果大于3000就不更新了
		trainer.OnlineTime += int64(nowTime-trainer.SessionInfo.LastOnlineTs) / 1000
		trainer.SessionInfo.LastOnlineTs = nowTime
	}
	// // trainer.OnlineTime = trainer.OnlineTime + 30 //看看这个多久被调用一次
	trainerLocInfo.TrainerId = trainer.Id
	tool.GlobalAOIManager.AddPlayer(trainerLocInfo.MainLandType, trainer.Id, trainerLocInfo)
	// nearby := tool.GlobalAOIManager.GetNearbyPlayersForPlayer(trainerLocInfo.MainLandType, trainer.Id, true, 0)
	// aoiManager.AddPlayer(req.MapId, req.PlayerId, Position{X:float64(req.Pos.X),Y:...})
	return "", nil
}

// 更新自己的位置，同时获取周围的训练家信息和自己同一个party的训练家信息
func RpcUpdateUserLoc(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return "", nil
	// trainerLocInfo := &MainServer.TrainerLocInfo{}
	// err := tool.Base64ToProto(payload, trainerLocInfo)
	// if err != nil {
	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	// }
	// if trainerLocInfo.Loc == nil {
	// 	return "", nil
	// }
	// point := fmt.Sprintf("[%.2f %.2f %.2f]",
	// 	trainerLocInfo.Loc.X,
	// 	trainerLocInfo.Loc.Y,
	// 	trainerLocInfo.Loc.Z)
	// // loc := fmt.Sprintf("%s|[%.2f %.2f %.2f]",
	// // 	trainerLocInfo.Loc.ReginId,
	// // 	trainerLocInfo.Loc.X,
	// // 	trainerLocInfo.Loc.Y,
	// // 	trainerLocInfo.Loc.Z)
	// // data := strings.Split(loc, "|") // region|[0,1]
	// // if len(data) < 2 {
	// // 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	// // }
	// trainer := tool.GetActiveTrainerByCtx(ctx)
	// if trainer == nil {
	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	// }
	// if trainerLocInfo.Loc.MainLandType != MainServer.MainLandType_MainLand_None {
	// 	trainer.ActionInfo.LastMainLand.Loc = trainerLocInfo.Loc
	// 	if trainerLocInfo.PcName != "" {
	// 		trainer.ActionInfo.LastMainLand.LastPcName = trainerLocInfo.PcName
	// 	}
	// }
	// trainer.SessionInfo.LocInfo = trainerLocInfo
	// // loc, err := tool.ProtoToBase64(&MainServer.TrainerLocInfo{
	// // 	Loc: &MainServer.TrainerLoc{
	// // 		ReginId: trainerLocInfo.Loc.ReginId,
	// // 		X:       trainerLocInfo.Loc.X,
	// // 		Y:       trainerLocInfo.Loc.Y,
	// // 		Z:       trainerLocInfo.Loc.Z,
	// // 	},
	// // })
	// if err != nil {
	// 	return "", err
	// }
	// trainer.ActionInfo.Loc = trainerLocInfo.Loc
	// result, err := updateUserLoc(ctx, logger, trainer, trainerLocInfo.Loc.ReginId, point)
	// if err != nil {
	// 	return "", err
	// }
	// partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
	// if exists {
	// 	partyTrainers := make([]*MainServer.Trainer, len(partyInfo.Trainers))
	// 	for _, v := range partyInfo.Trainers {
	// 		partyTrainers = append(partyTrainers, tool.CopySafeTrainer((v)))
	// 	}
	// 	result.PartyTrainers = partyTrainers
	// }

	// nowTime := time.Now().UnixMilli()
	// if nowTime-trainer.UpdateTs < 3000 { //距离最后一次更新如果大于3000就不更新了
	// 	trainer.OnlineTime = nowTime - trainer.UpdateTs
	// }
	// // trainer.OnlineTime = trainer.OnlineTime + 30 //看看这个多久被调用一次
	// defer func() {
	// 	// 	partyInfo, exists := tool.GetGlobalPartyMap().Get(userId)
	// 	// if !exists {
	// 	// 	partyInfo = &tool.PartyInfo{
	// 	// 		Trainers: make(map[int64]*MainServer.Trainer),
	// 	// 	}
	// 	// }
	// 	//300000 毫秒 约等于 300秒 = 5分钟 test 0.5分钟
	// 	if trainer.UpdateTs+config.TrainerUpdateInterval < nowTime {
	// 		//更新一下
	// 		uptrainer, err := UpdateTrainer(ctx, logger, db, trainer)
	// 		if err != nil {
	// 			trainer.UpdateTs = uptrainer.UpdateTs
	// 		}
	// 	}
	// }()
	// return tool.ProtoToBase64(result)
}

func RpcCreateTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	count := userTrainerCount(db, userID, 0, true)
	if count >= 6 {
		return "", runtime.NewError("Trainer count max: "+strconv.Itoa(count), 400)
		// return "", fmt.Errorf("Trainer count max: %d", count)
	}
	trainer := &MainServer.Trainer{}
	err := tool.Base64ToProto(payload, trainer)
	if err != nil {
		// 创建 Trainer 对象
		trainer = &MainServer.Trainer{
			Uid:    userID,
			Name:   "Ash Ketchum",
			Gender: MainServer.Gender_M,
			ActionInfo: &MainServer.TrainerActionInfo{
				Action: MainServer.TrainerActionType_battle,
			},
			PokeIds:    []string{"poke_001", "poke_002", "poke_003"},
			Items:      map[string]*MainServer.TrainerItemInfo{},
			Badges:     &MainServer.TrainerBadges{},
			Team:       0,
			GroupId:    "group_101",
			Cloth:      &MainServer.TrainerCloth{},
			StrictInfo: &MainServer.TrainerStrictInfo{},
			CreateTs:   time.Now().UnixMilli(),
			UpdateTs:   time.Now().UnixMilli(),
		}
	}
	trainer.Uid = userID
	return createTrainer(ctx, logger, db, nk, trainer)
}
func RpcTraienrAllPokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	boxs, err := GetPokeBoxsByTs(ctx, logger, db, MainServer.PokeBoxType_normal, 0)
	if err != nil {
		return "", err
	}
	result := &MainServer.PokeBoxResult{
		Boxs: boxs,
		Ts:   time.Now().UnixMilli(),
	}
	return tool.ProtoToBase64(result)
}

func RpcBatchExchangePokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	exchange := &MainServer.PokeBatchBoxExchange{}
	err := tool.Base64ToProto(payload, exchange) //MainServer.PokeBoxExchange
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	updateTs, err := BatchExchangePokeBox(ctx, logger, tx, exchange.Exchanges)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return strconv.FormatInt(updateTs, 10), err
}

func RpcRecoverAroundPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	pokes, exists := tool.GetTrainerAroundPokes(trainer.Id)
	if !exists {
		return "", runtime.NewError("未找到周围宝可梦", 404)
	}
	// 遍历所有宝可梦，恢复它们的状态
	for _, poke := range pokes {
		poke.HpSub = 0
	}
	return "", nil
}
func RpcUpdateFollowPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	param := &MainServer.FollowPokeParam{}
	err := tool.Base64ToProto(payload, param)
	// pokeId, err := strconv.ParseInt(payload, 10, 64)
	// pokeId, ex := tool.GetIntFromPayload("pid", payload)
	if err != nil {
		return "", fmt.Errorf("not found pokeid")
	}
	// remove, _ := tool.GetBoolFromPayload("remove", payload)
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	_, err = UpdateFollowPoke(ctx, logger, tx, param)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return "", err
}

func RpcBuyPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	param := &MainServer.RpcPokeSimpleInfoRequest{}
	err := tool.Base64ToProto(payload, param)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeinfo, err := BuyPoke(ctx, logger, tx, nk, param.Pid)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return tool.ProtoToBase64(pokeinfo)
}

// 下架
func RpcUnsalePokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// pid, err := strconv.ParseInt(payload, 10, 64)
	// if err != nil {
	// 	return "", err
	// }
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	param := &MainServer.RpcPokeSimpleInfoRequest{}
	err := tool.Base64ToProto(payload, param)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	info, err := UnsalePokemon(ctx, logger, tx, trainer, param.Pid, MainServer.TransactionType_TransactionType_Market_Sale)
	if err != nil {
		return "", fmt.Errorf("failed to unsale pokemon: %v", err)
	}
	// _, err = SavePokeToNormalBox(ctx, logger, trainer, tx, pokemon.Info)
	// if err != nil {
	// 	return "", fmt.Errorf("failed to save pokemon: %v", err)
	// }
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(info)
}

func RpcSalePokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	param := &MainServer.RpcSalePokemonRequest{}
	err := tool.Base64ToProto(payload, param)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// 获取当前训练师
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	info, err := SalePokemon(ctx, tx, trainer, param.BoxType, param.BoxIndex, param.BoxLoc, param.Pid, param.Price)
	if err != nil {
		return "", fmt.Errorf("failed to sale pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(info)
}
func RpcReleasePoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	param := &MainServer.SimpleBoxPokeInfo{}
	err = tool.Base64ToProto(payload, param)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	_, err = ReleasePoke(ctx, logger, tx, trainer, param.PokeBoxType, param.PokeBox, param.PokeBoxLoc, param.PokeId)
	if err != nil {
		return "", fmt.Errorf("failed to release pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return "", nil
}

func RpcBorrowPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	pokeId, ex := tool.GetIntFromPayload("pid", payload)
	if !ex {
		return "", fmt.Errorf("not found pokeid")
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeinfo, err := BuyPoke(ctx, logger, tx, nk, pokeId)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return tool.ProtoToBase64(pokeinfo)
}

// 出租
// func RpcRentPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	param := &MainServer.RpcSalePokemonRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}
// 	// 获取当前训练师
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}
// 	// Step 1: 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("failed to begin transaction: %v", err)
// 		return "", fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback()
// 	pokeId, err := RentPokemon(ctx, tx, trainer, param.BoxType, param.BoxIndex, param.Pid, param.Price, param.RentDay, param.CanRenewed)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to sale pokemon: %v", err)
// 	}
// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		logger.Error("事务提交失败: %v", err)
// 		return "", fmt.Errorf("failed to commit transaction: %w", err)
// 	}
// 	return strconv.FormatInt(pokeId, 10), err
// }

// func RpcUnrentPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	pid, err := strconv.ParseInt(payload, 10, 64)
// 	if err != nil {
// 		return "", err
// 	}
// 	// 获取当前训练师
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}
// 	// Step 1: 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("failed to begin transaction: %v", err)
// 		return "", fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback()
// 	info, err := UnsalePokemon(ctx, logger, tx, trainer, pid, MainServer.TransactionType_TransactionType_Market_Rent)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to unsale pokemon: %v", err)
// 	}
// 	// _, err = SavePokeToNormalBox(ctx, logger, trainer, tx, pokemon)
// 	// if err != nil {
// 	// 	return "", fmt.Errorf("failed to save pokemon: %v", err)
// 	// }
// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		logger.Error("事务提交失败: %v", err)
// 		return "", fmt.Errorf("failed to commit transaction: %w", err)
// 	}
// 	return tool.ProtoToBase64(info)
// }

//	func RpcUpdateParty(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
//		//
//	}
//
// RpcBreedPokemon 宝可梦繁殖RPC函数
// 输入参数: 父母宝可梦的ID，繁殖道具信息，繁殖次数
// 返回: 新生宝可梦信息，父母是否死亡的信息
func RpcBreedPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 定义请求结构
	var request MainServer.BreedPokemonRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	// 获取当前训练师
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TransactionFailed)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	if !BoxContainsPoke(ctx, tx, trainer.Id, request.PokeInfo_1.PokeBox, request.PokeInfo_1.PokeBoxLoc, request.PokeInfo_1.PokeId, request.PokeInfo_1.PokeBoxType) {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotInBox)
	}
	if !BoxContainsPoke(ctx, tx, trainer.Id, request.PokeInfo_2.PokeBox, request.PokeInfo_1.PokeBoxLoc, request.PokeInfo_2.PokeId, request.PokeInfo_2.PokeBoxType) {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotInBox)
	}

	// 获取父母宝可梦
	poke1 := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeInfo_1.PokeId)
	if poke1 == nil {
		return "", runtime.NewError("获取父方宝可梦失败: "+err.Error(), 404)
	}

	poke2 := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeInfo_2.PokeId)
	if poke2 == nil {
		return "", runtime.NewError("获取母方宝可梦失败: "+err.Error(), 404)
	}

	// 检查是否可以繁殖，并获取正确的父方和母方
	fatherPoke, motherPoke, canBreed := poke.CanBreed(poke1, poke2)
	if !canBreed {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_CannotBreed)
	}
	fatherItems := &MainServer.BreedingItems{}
	motherItems := &MainServer.BreedingItems{}
	fatherBreedPokeInfo := &MainServer.SimpleBoxPokeInfo{}
	motherBreedPokeInfo := &MainServer.SimpleBoxPokeInfo{}
	if fatherPoke.Id == poke1.Id {
		fatherItems = request.Items_1
		motherItems = request.Items_2
		fatherBreedPokeInfo = request.PokeInfo_1
		motherBreedPokeInfo = request.PokeInfo_2
	} else {
		fatherItems = request.Items_2
		motherItems = request.Items_1
		fatherBreedPokeInfo = request.PokeInfo_2
		motherBreedPokeInfo = request.PokeInfo_1
	}
	// 检查和应用繁殖道具
	fatherItemName := ""
	if fatherPoke.ItemInfo != nil {
		fatherItemName = fatherPoke.ItemInfo.ItemName
	}
	motherItemName := ""
	if motherPoke.ItemInfo != nil {
		motherItemName = motherPoke.ItemInfo.ItemName
	}
	cleanFatherItem, cleanMotherItem, err := CheckBreedingItems(ctx, tx, trainer.Id, fatherItemName, motherItemName, fatherItems, motherItems)
	if err != nil {
		return "", err
	}
	baby := &MainServer.Poke{}
	fatherDied := false
	motherDied := false
	// err := nil
	if request.IsInline {
		// 执行繁殖
		baby, fatherDied, motherDied, err = poke.BreedPokemon(ctx, tx, fatherPoke, motherPoke, fatherItems, motherItems)
		if err != nil {
			return "", runtime.NewError("繁殖失败: "+err.Error(), 400)
		}
	} else {
		baby, fatherDied, motherDied, err = poke.MMoBreedPokemon(ctx, tx, fatherPoke, motherPoke, fatherItems, motherItems)
		if err != nil {
			return "", runtime.NewError("繁殖失败: "+err.Error(), 400)
		}
	}

	// 处理死亡状态
	if fatherDied {
		fatherPoke.Status.LiveStatus = MainServer.PokeLiveStatus_PokeLiveStatus_Dead
		_, err = ReleasePoke(ctx, logger, tx, trainer, fatherBreedPokeInfo.PokeBoxType, int32(fatherBreedPokeInfo.PokeBox), int32(fatherBreedPokeInfo.PokeBoxLoc), fatherPoke.Id)
		// fatherExchange := &MainServer.PokeBoxExchange{
		// 	SourceBox:     int32(fatherBreedPokeInfo.PokeBox),
		// 	SourceBoxType: fatherBreedPokeInfo.PokeBoxType,
		// 	SourceLoc:     int32(fatherBreedPokeInfo.PokeBoxLoc),
		// 	IsDelete:      true,
		// }
		// _, err = ExchangePokeBox(ctx, logger, tx, fatherExchange)
		if err != nil {
			return "", runtime.NewError("删除父方宝可梦失败: "+err.Error(), 500)
		}
		fatherPoke.Release = true
	}
	fatherPoke.BreedCount++
	if cleanFatherItem {
		if fatherPoke.ItemInfo == nil {
			fatherPoke.ItemInfo = &MainServer.PokeItemInfo{}
		}
		fatherPoke.ItemInfo.ItemName = ""
	}
	err = poke.UpdatePokeData(ctx, tx, fatherPoke)
	if err != nil {
		return "", runtime.NewError("更新父方宝可梦状态失败: "+err.Error(), 500)
	}

	if motherDied {
		motherPoke.Status.LiveStatus = MainServer.PokeLiveStatus_PokeLiveStatus_Dead
		_, err = ReleasePoke(ctx, logger, tx, trainer, motherBreedPokeInfo.PokeBoxType, int32(motherBreedPokeInfo.PokeBox), int32(motherBreedPokeInfo.PokeBoxLoc), motherPoke.Id)
		if err != nil {
			return "", runtime.NewError("删除母方宝可梦失败: "+err.Error(), 500)
		}
		motherPoke.Release = true
		// motherExchange := &MainServer.PokeBoxExchange{
		// 	SourceBox:     int32(motherBreedPokeInfo.PokeBox),
		// 	SourceBoxType: motherBreedPokeInfo.PokeBoxType,
		// 	SourceLoc:     int32(motherBreedPokeInfo.PokeBoxLoc),
		// 	IsDelete:      true,
		// }
		// _, err = ExchangePokeBox(ctx, logger, tx, motherExchange)
		// if err != nil {
		// 	return "", runtime.NewError("删除母方宝可梦失败: "+err.Error(), 500)
		// }
	}
	motherPoke.BreedCount++
	if cleanMotherItem {
		if motherPoke.ItemInfo == nil {
			motherPoke.ItemInfo = &MainServer.PokeItemInfo{}
		}
		motherPoke.ItemInfo.ItemName = ""
	}
	err = poke.UpdatePokeData(ctx, tx, motherPoke)
	if err != nil {
		return "", runtime.NewError("更新母方宝可梦状态失败: "+err.Error(), 500)
	}

	// 设置新宝可梦所属训练师ID
	baby.Tid = trainer.Id
	err = poke.InsertPokeData(ctx, tx, baby)
	if err != nil {
		return "", runtime.NewError("更新新生宝可梦状态失败: "+err.Error(), 500)
	}
	// 将新宝可梦添加到训练师的盒子中
	_, err = SaveNewPokeToNormalBoxPriorityBoxType(ctx, logger, trainer, MainServer.PokeBoxType_hatch, tx, baby)
	// _, err = SavePokeToNormalBox(ctx, logger, trainer, tx, baby)
	if err != nil {
		return "", runtime.NewError("保存新宝可梦到盒子失败: "+err.Error(), 500)
	}

	// 构建响应结果
	result := &MainServer.BreedPokemonResponse{
		Baby:       baby,
		FatherDied: fatherDied,
		MotherDied: motherDied,
		FatherId:   fatherPoke.Id,
		MotherId:   motherPoke.Id,
	}

	return tool.ProtoToBase64(result)
}

// Check and apply breeding items for both parents, and verify inventory sufficiency
func CheckBreedingItems(ctx context.Context, tx *sql.Tx, trainerId int64, fatherItemName, motherItemName string, fatherItems, motherItems *MainServer.BreedingItems) (cleanFatherItem bool, cleanMotherItem bool, err error) {
	type itemInfo struct {
		name  string
		count int
	}
	cleanFatherItem = false
	cleanMotherItem = false
	addStatItemsCount := func(count1 int32, count2 int32) int {
		count := 0
		if count1 > 0 {
			count = int(count) + int(count1)
		}
		if count2 > 0 {
			count = int(count) + int(count2)
		}
		return count
	}
	// Helper to apply stat item effect
	checkItems := []itemInfo{
		{"powerweight", addStatItemsCount(fatherItems.StatItems.Hp, motherItems.StatItems.Hp)},
		{"powerbracer", addStatItemsCount(fatherItems.StatItems.Atk, motherItems.StatItems.Atk)},
		{"powerbelt", addStatItemsCount(fatherItems.StatItems.Def, motherItems.StatItems.Def)},
		{"powerlens", addStatItemsCount(fatherItems.StatItems.Spa, motherItems.StatItems.Spa)},
		{"powerband", addStatItemsCount(fatherItems.StatItems.Spd, motherItems.StatItems.Spd)},
		{"poweranklet", addStatItemsCount(fatherItems.StatItems.Spe, motherItems.StatItems.Spe)},
		{"destinyknot", func() int {
			count := 0
			if fatherItems.RedString {
				count++
			}
			if motherItems.RedString {
				count++
			}
			return count
		}()},
	}
	for _, item := range checkItems {
		if item.count > 0 {
			sufficient, err := inventory.CheckItemQuantityByItemName(ctx, tx, trainerId, item.name, int32(item.count))
			if err != nil {
				return false, false, err
			}
			if !sufficient {
				return false, false, runtime.NewError("insufficient items", 403)
			}
		}
	}
	//携带的道具不需要进行判断
	applyStatItem := func(itemName string, items *MainServer.BreedingItems) bool {
		switch itemName {
		case "powerweight":
			items.StatItems.Hp = 1
		case "powerbracer":
			items.StatItems.Atk = 1
		case "powerbelt":
			items.StatItems.Def = 1
		case "powerlens":
			items.StatItems.Spa = 1
		case "powerband":
			items.StatItems.Spd = 1
		case "poweranklet":
			items.StatItems.Spe = 1
		case "destinyknot":
			items.RedString = true
		default:
			return false
		}
		return true
	}
	cleanFatherItem = applyStatItem(fatherItemName, fatherItems)
	cleanMotherItem = applyStatItem(motherItemName, motherItems)

	return cleanFatherItem, cleanMotherItem, nil
}

// func RpcHatchEgg(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 定义请求结构
// 	var request MainServer.HatchEggRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}
// 	// 获取当前训练师
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}
// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", runtime.NewError("开始事务失败: "+err.Error(), 500)
// 	}
// 	defer func() {
// 		if err != nil {
// 			tx.Rollback()
// 		} else {
// 			tx.Commit()
// 		}
// 	}()
// 	hatchPoke(ctx, logger, nk, tx, trainer, &request)
// 	// 获取宝可梦
// 	// pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeId)
// 	// if pokemon == nil {
// 	// 	return "", runtime.NewError("获取宝可梦失败: "+err.Error(), 404)
// 	// }
// 	// if !pokemon.Egg {
// 	// 	return "", runtime.NewError("不是蛋", 400)
// 	// }
// 	// pokemon.Egg = false
// 	return "", nil
// }

// RpcEvolveByLevel 宝可梦等级进化RPC函数
// 输入参数: 宝可梦ID和目标进化形态
// 返回: 进化后的宝可梦信息
func RpcEvolveByLevel(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 定义请求结构
	var request MainServer.EvolveByLevelRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("开始事务失败: "+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// 获取宝可梦
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeId)
	if pokemon == nil {
		return "", runtime.NewError("获取宝可梦失败: "+err.Error(), 404)
	}

	// 检查等级要求
	// evoData, exists := poke.GetPokemonEvolutionInfo(poke.Name, request.TargetEvolution)
	// if !exists {
	// 	return "", runtime.NewError("无效的进化路径", 400)
	// }

	// if evoData.Method != "level-up" || poke.Level < evoData.Level {
	// 	return "", runtime.NewError("不满足进化条件", 400)
	// }

	// 执行进化
	evolvedPoke, err := poke.EvolveByLevel(ctx, tx, pokemon, request.TargetEvolution)
	if err != nil {
		return "", runtime.NewError("进化失败: "+err.Error(), 400)
	}

	// 构建响应结果
	// result := &MainServer.EvolveByLevelResponse{
	// 	Poke: evolvedPoke,
	// }

	return tool.ProtoToBase64(evolvedPoke)
}

// RPC处理函数

func RpcAddTraienrTeam(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	if trainer.Team != MainServer.TrainerTeam_TRAINER_TEAM_NONE {
		return "", runtime.NewError("已经加入组织", 400)
	}
	// 解析请求
	var request MainServer.RpcAddTrainerTeamRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	checked := nconst.CheckAddTeam(request.Type)
	if !checked {
		return "", runtime.NewError("无法加入该组织", 400)
	}
	// clothId := nconst.GetInitTeamClothIdBy(request.Type)
	// 添加校服服装(手动领取)
	// err = AddTrainerCloth(ctx, logger, tx, trainer.Id, MainServer.TrainerClothType_CLOTH_TYPE_OLD_NIN, clothId)
	// if err != nil {
	// 	return "", err
	// }
	titleType := nconst.GetInitTeamTitleBy(request.Type)
	// 添加称号
	err = AddTrainerTitle(ctx, logger, tx, trainer.Id, titleType, 0, -1)
	if err != nil {
		return "", err
	}
	trainer.Team = request.Type
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.Team = MainServer.TrainerTeam_TRAINER_TEAM_NONE
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		trainer.Team = MainServer.TrainerTeam_TRAINER_TEAM_NONE
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return "", nil
}

func RpcLastExpInfoRequest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	// 解析请求
	var request MainServer.LastExpInfoRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	pokemon, err := lastExpInfoRequest(ctx, logger, tx, trainer, &request)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(pokemon)
}
func RpcTryUpTeamLevel(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryUpTeamLevel(ctx, logger, tx, trainer)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}
func RpcDonateTeamByCoin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	amount, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryDonateTeamByCoin(ctx, logger, tx, trainer, amount)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}
func RpcConsumeTeamContributionToExp(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	amount, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryConsumeTeamContributionToExp(ctx, logger, tx, trainer, amount)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}
func RpcBuyStoreItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	var request MainServer.RpcBuyItemRequest
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	err = tryBuyStoreItem(ctx, logger, tx, trainer, request.ItemName, request.InventoryType, request.Count, request.BuySiteType, request.TeamType)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return "", nil
	// return tool.ProtoToBase64(trainer)
}

func RpcUpgradeTeamSummonFlute(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	var request MainServer.RpcUpgradeTeamSummonFluteRequest
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	err = tryUpdateTeamSummonFluteInfo(ctx, logger, tx, trainer, request.IsCount)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return "", nil
}
func RpcOtherTypeBoxPokeToHatchBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	var request MainServer.SimpleBoxPokeInfo
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	cellInfo, err := OtherTypeBoxPokeToHatchBox(ctx, logger, trainer, tx, request.PokeBoxType, request.PokeBox, request.PokeBoxLoc, request.PokeId)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(cellInfo)
}
func RpcOtherTypeBoxPokeToNarmalBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	var request MainServer.SimpleBoxPokeInfo
	err = tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	cellInfo, err := OtherTypeBoxPokeToNarmalBox(ctx, logger, trainer, tx, request.PokeBoxType, request.PokeBox, request.PokeBoxLoc, request.PokeId)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(cellInfo)
}
