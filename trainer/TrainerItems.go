package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/config"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"math"
	"math/rand"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// var itemCollectionName = "items"

// TODO 服务器记录道具使用时长
// // 道具结构体，用来解析 payload
//
//	type ItemPayload struct {
//		ItemID   string `json:"id"`
//		Quantity int    `json:"qty"`
//		Target   string `json:"tg"`
//	}
func initTrainerItemBox(ctx context.Context, tx *sql.Tx) {

}

// func TestRpcAddItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// trainer := tool.GetActiveTrainer(ctx)
// 	// if trainer == nil {
// 	// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	// 	logger.Error("未找到用户的 active tid %s", userID)
// 	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	// }
// 	// tid := trainer.Id
// 	tid := int64(1)
// 	// itype, ex := tool.GetIntFromPayload("type", payload)
// 	// if !ex {
// 	// 	return "", runtime.NewError("Not found type", 404)
// 	// }
// 	name, ex := tool.GetStringFromPayload("name", payload)
// 	if !ex {
// 		return "", runtime.NewError("Not found name", 404)
// 	}
// 	quantity, ex := tool.GetIntFromPayload("q", payload)
// 	if !ex {
// 		quantity = 1
// 	}
// 	// 开启事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("事务启动失败: %v", err)
// 		return "", fmt.Errorf("failed to start transaction: %w", err)
// 	}
// 	defer tx.Rollback()
// 	err = inventory.AddItemToInventory(ctx, logger, tx, tid, name, int32(quantity))
// 	if err != nil {
// 		return "", err
// 	}
// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		logger.Error("事务提交失败: %v", err)
// 		return "", fmt.Errorf("failed to commit transaction: %w", err)
// 	}

// 	return "{}", nil
// }

// func RpcTraienrAllInventory(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取 Trainer 信息
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 		logger.Error("未找到用户的 active tid %s", userID)
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}
// 	tid := trainer.Id
// 	// 开启事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("事务启动失败: %v", err)
// 		return "", fmt.Errorf("failed to start transaction: %w", err)
// 	}
// 	defer tx.Rollback() // 确保失败时回滚事务

// 	// 获取所有库存
// 	inventories, err := inventory.SelectAllInventoriesByTid(ctx, tx, tid)
// 	if err != nil {
// 		logger.Error("获取库存失败: %v", err)
// 		return "", fmt.Errorf("failed to select inventories: %w", err)
// 	}

// 	// 构造结果
// 	result := &MainServer.InventorysResult{
// 		Page:       1, // 默认第一页
// 		Ts:         time.Now().UnixMilli(),
// 		Inventorys: inventories,
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		logger.Error("事务提交失败: %v", err)
// 		return "", fmt.Errorf("failed to commit transaction: %w", err)
// 	}

// 	// 返回结果
// 	base64Result, err := tool.ProtoToBase64(result)
// 	if err != nil {
// 		logger.Error("Proto 转 Base64 失败: %v", err)
// 		return "", fmt.Errorf("failed to encode result to base64: %w", err)
// 	}

//		return base64Result, nil
//	}
func RpcTryUseSummonItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var itemPayload = &MainServer.UseSummonItemInfo{}
	if err := tool.Base64ToProto(payload, itemPayload); err != nil {
		logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	response, err := TryUseSummonItem(ctx, logger, tx, nk, trainer, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}
func RpcTryUseTrainerItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var itemPayload = &MainServer.UseItemInfo{}
	if err := tool.Base64ToProto(payload, itemPayload); err != nil {
		logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return "", err
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TransactionFailed)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	response, err := TryUseTrainerItem(ctx, logger, tx, nk, trainer, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}

func RpcTryUsePokeItemOne(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var itemPayload = &MainServer.UseItemInfo{}
	if err := tool.Base64ToProto(payload, itemPayload); err != nil {
		logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	itemPayload.Quantity = 1
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	response, err := TryUsePokeItem(ctx, logger, tx, nk, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}

func RpcTryUsePokeItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var itemPayload = &MainServer.UseItemInfo{}
	if err := tool.Base64ToProto(payload, itemPayload); err != nil {
		logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务

	response, err := TryUsePokeItem(ctx, logger, tx, nk, itemPayload)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}
func TryUseSummonItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, trainer *MainServer.Trainer, useItemInfo *MainServer.UseSummonItemInfo) (*MainServer.UseSummonItemInfoResponse, error) {
	_, ex := item.GetItemByName(useItemInfo.ItemName)
	if !ex {
		return nil, fmt.Errorf("failed to read item: %s", useItemInfo.ItemName)
	}
	item, err := inventory.RemoveSummonItemById(ctx, tx, trainer.Id, useItemInfo.ItemId, useItemInfo.ItemName, useItemInfo.Quantity)
	if err != nil {
		return nil, err
	}
	summonItemLevel := inventory.SummonItemLevel(useItemInfo.ItemName)
	level := int32(100)
	teamVerify := false
	switch summonItemLevel {
	case MainServer.PokeCateLevel_PokeCateLevel_1:
		level = 10
		teamVerify = true
	case MainServer.PokeCateLevel_PokeCateLevel_2:
		level = 20
		teamVerify = true
	case MainServer.PokeCateLevel_PokeCateLevel_3:
		level = 30
		teamVerify = true
	case MainServer.PokeCateLevel_PokeCateLevel_4:
		level = 40
		teamVerify = true
	case MainServer.PokeCateLevel_PokeCateLevel_5:
		level = 50
		teamVerify = true
	case MainServer.PokeCateLevel_PokeCateLevel_First:
		level = 70
	case MainServer.PokeCateLevel_PokeCateLevel_Late:
		level = 80
	case MainServer.PokeCateLevel_PokeCateLevel_Paradox:
		level = 80
	case MainServer.PokeCateLevel_PokeCateLevel_Ultra:
		level = 80
	case MainServer.PokeCateLevel_PokeCateLevel_LegendaryNormal:
		level = 100
	case MainServer.PokeCateLevel_PokeCateLevel_LegendarySpecial:
		level = 100
	case MainServer.PokeCateLevel_PokeCateLevel_Mythical:
		level = 100
	}
	// if item.TeamType <= MainServer.TrainerTeam_TRAINER_TEAM_ALL {
	// 	return nil, fmt.Errorf("召唤道具Team属性不对")
	// }
	// if teamVerify && item.TeamType == MainServer.TrainerTeam_TRAINER_TEAM_NONE {
	// 	return nil, fmt.Errorf("召唤道具Team属性不对")
	// }
	if item.TeamType <= MainServer.TrainerTeam_TRAINER_TEAM_ALL {
	}
	var nameList *MainServer.PokeNameIdList
	exists := false
	if !teamVerify {
		nameList, exists = poke.GetOtherPokeNameList(summonItemLevel)
	} else {
		nameList, exists = poke.GetWithinLevel5PokeNameList(item.TeamType, summonItemLevel)
	}
	//TODO判断训练师的召唤笛等级，按照等级分配百分比
	// useItemInfo.LockedPokeIds
	// nameList, exists := poke.GetWithinLevel5PokeNameList(item.TeamType, summonItemLevel)
	if !exists {
		return nil, fmt.Errorf("failed to get poke name list")
	}
	lockedPokeNames := make([]string, 0)
	for _, lockedPokeId := range useItemInfo.LockedPokeIds {
		if pokeName, exists := nameList.PokeMaps[lockedPokeId]; exists {
			lockedPokeNames = append(lockedPokeNames, pokeName)
		}
	}
	fluteInfo := trainer.TeamInfo.SummonFlute
	if fluteInfo == nil {
		fluteInfo = &MainServer.SummonFluteInfo{}
	}
	pokeRate := 1.0
	switch fluteInfo.CountLevel {
	case 0:
		pokeRate = 1.0
	case 1:
		pokeRate = 1.0 + 0.2
	case 2:
		pokeRate = 1.0 + 0.5
	case 3:
		pokeRate = 1.0 + 1.0
	case 4:
		pokeRate = 1.0 + 1.3
	case 5:
		pokeRate = 1.0 + 1.8
	case 6:
		pokeRate = 1.0 + 2.1
	case 7:
		pokeRate = 1.0 + 2.4
	case 8:
		pokeRate = 1.0 + 2.6
	case 9:
		pokeRate = 1.0 + 3.0
	}
	intPart, fracPart := math.Modf(pokeRate) // 分离整数和小数部分
	pokeCount := int(intPart)

	if rand.Float64() < fracPart {
		pokeCount++ // 命中小数概率，多加 1
	}
	appearedPokes := make([]*MainServer.Poke, 0)

	lockedRate := float32(0.05)
	switch fluteInfo.LockLevel {
	case 0:
		lockedRate = 0.05
	case 1:
		lockedRate = 0.1
	case 2:
		lockedRate = 0.15
	case 3:
		lockedRate = 0.2
	case 4:
		lockedRate = 0.25
	case 5:
		lockedRate = 0.3
	case 6:
		lockedRate = 0.35
	case 7:
		lockedRate = 0.4
	case 8:
		lockedRate = 0.45
	case 9:
		lockedRate = 0.5
	}
	lockedRate = lockedRate + 0.1
	locked := (rand.Float32()-lockedRate > 0)
	if locked && len(lockedPokeNames) > 0 {
		for _, pokeName := range lockedPokeNames {
			createPokeInfo := &MainServer.CreatePokeInfo{
				Name:  pokeName,
				Level: level,
			}
			pokeInfo, err := poke.CreatePoke(ctx, tx, createPokeInfo, &MainServer.BornInfo{
				BornType: MainServer.BornInfoType_BornInfoType_Summon,
				Tid:      trainer.Id,
				// BornTs:   time.Now().Unix(),
			})
			if err != nil {
				return nil, err
			}
			// pokeInfo.Born = &MainServer.BornInfo{
			// 	BornType: MainServer.BornInfoType_BornInfoType_Summon,
			// 	Tid:      trainer.Id,
			// 	BornTs:   time.Now().Unix(),
			// }
			appearedPokes = append(appearedPokes, pokeInfo)
		}
	} else {
		// 假设 nameList.Pokes 是 map[string]*Poke
		pokesSlice := nameList.Pokes

		// 把 map 的值放到 slice 里
		// pokesSlice := make([]string, 0, len(pokesMap))
		// for _, poke := range pokesMap {
		// 	pokesSlice = append(pokesSlice, poke)
		// }

		// 随机取 pokeCount 个（允许重复）
		for i := 0; i < pokeCount; i++ {
			index := rand.Intn(len(pokesSlice))
			randomPoke := pokesSlice[index]
			createPokeInfo := &MainServer.CreatePokeInfo{
				Name:  randomPoke,
				Level: level,
			}
			pokeInfo, err := poke.CreatePoke(ctx, tx, createPokeInfo, &MainServer.BornInfo{
				BornType: MainServer.BornInfoType_BornInfoType_Summon,
				Tid:      trainer.Id,
			})
			if err != nil {
				return nil, err
			}
			appearedPokes = append(appearedPokes, pokeInfo)
			// 用 randomPoke 做点什么
		}
		//从nameList.Pokes中随机取出pokeCount数量的值(pokes是一个字典类型)

		// for i := 0; i < pokeCount; i++ {
		// 	levelPokeCount := len(nameList.Pokes)
		// 	index := rand.Intn(levelPokeCount)

		// }
	}

	response := &MainServer.UseSummonItemInfoResponse{
		ItemName:      useItemInfo.ItemName,
		Quantity:      useItemInfo.Quantity,
		AppearedPokes: appearedPokes,
	}
	return response, nil
}
func TryUseTrainerItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, trainer *MainServer.Trainer, useItemInfo *MainServer.UseItemInfo) (*MainServer.UseTrainerItemInfoResponse, error) {
	localItem, ex := item.GetItemByName(useItemInfo.ItemName)
	if !ex {
		return nil, fmt.Errorf("failed to read item: %s", useItemInfo.ItemName)
	}
	tid := trainer.Id
	response := &MainServer.UseTrainerItemInfoResponse{
		ItemName: useItemInfo.ItemName,
		Quantity: useItemInfo.Quantity,
	}
	actionType := MainServer.TrainerActionType(trainer.ActionInfo.Action) // 转换为枚举类型

	// 检查道具是否可以在当前 actionType 下使用
	canUse, err := config.GetItemConfig(ctx, nk, useItemInfo.ItemName, actionType)
	if err != nil || !canUse {
		logger.Warn("用户 %s 在场景 %v 中无法使用道具 %s", tid, actionType, useItemInfo.ItemName)
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_ItemCannotUseHere)
	}
	if IsTrainerSpecialItem(useItemInfo.ItemName) {
		used, err := UseTrainerSpecialItem(ctx, logger, tx, trainer, localItem)
		if err != nil {
			return nil, err
		}
		if !used {
			return nil, nil
		}
	} else {
		_, err = inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, useItemInfo.ItemName, useItemInfo.Quantity)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		err = addItemStatus(ctx, logger, tx, trainer, localItem)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	response.TargetTrainer = trainer
	return response, nil
}
func addItemStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, localItem *MainServer.LocalItem) error {
	now := time.Now().Unix()
	trainer.Items[localItem.Name] = &MainServer.TrainerItemInfo{
		UseTs:    now,
		ExpireTs: now + (int64)(localItem.Duration),
	}
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	return err
}

// tryUseItem 负责处理道具使用逻辑
func TryUsePokeItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, useItemInfo *MainServer.UseItemInfo) (*MainServer.UseItemInfoResponse, error) {
	//通过itemname获取type
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tid := trainer.Id
	response := &MainServer.UseItemInfoResponse{
		ItemName: useItemInfo.ItemName,
		Quantity: useItemInfo.Quantity,
	}
	localItem, exists := item.GetItemByName(useItemInfo.ItemName)
	if !exists {
		return nil, runtime.NewError("道具不存在", 404)
	}
	if localItem.InventoryType == MainServer.InventoryType_inventory_summon || localItem.InventoryType == MainServer.InventoryType_inventory_seal {
		return nil, runtime.NewError("该类型的道具不能这样使用", 400)
	}
	// trainer, err := SelectTrainerProto(ctx, db, tid)
	// if err != nil {
	// 	return "", err
	// }
	// 获取用户的元数据
	// users, err := nk.UsersGetId(ctx, []string{userID}, []string{})
	// if err != nil || len(users) == 0 {
	// 	logger.Error("未找到用户 %s: %v", userID, err)
	// 	return "", runtime.NewError("未找到用户", 404)
	// }
	// user := users[0]

	// var metadata map[string]interface{}
	// if err := json.Unmarshal([]byte(user.Metadata), &metadata); err != nil {
	// 	logger.Error("解析用户 %s 的元数据失败: %v", userID, err)
	// 	return "", runtime.NewError("解析用户元数据失败", 500)
	// }

	// 获取用户状态（actionType 是一个枚举类型）
	// actionTypeInt, ok := metadata["actionType"].(float64) // JSON 解码出来的是 float64
	// actionTypeInt := trainer.Action
	// if !ok {
	// 	logger.Error("用户 %s 的 actionType 不合法", userID)
	// 	return "", runtime.NewError("用户状态不合法", 500)
	// }
	actionType := MainServer.TrainerActionType(trainer.ActionInfo.Action) // 转换为枚举类型

	// 检查道具是否可以在当前 actionType 下使用
	canUse, err := config.GetItemConfig(ctx, nk, useItemInfo.ItemName, actionType)
	if err != nil || !canUse {
		logger.Warn("用户 %s 在场景 %v 中无法使用道具 %s", tid, actionType, useItemInfo.ItemName)
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_ItemCannotUseHere)
	}
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, useItemInfo.TargetPokeId)
	if pokemon == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	// 调用 useItem 扣除道具数量并处理结果
	_, err = inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, useItemInfo.ItemName, useItemInfo.Quantity)
	if err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
		return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	if useItemInfo.ItemName == "rarecandy" {
		level := pokemon.Level + useItemInfo.Quantity
		if level > 100 {
			level = 100
		}
		pokemon, err := changeLevel(ctx, logger, tx, trainer, pokemon, level)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		response.TargetPoke = pokemon
	} else if useItemInfo.ItemName == "dynamaxcandy" {
		level := pokemon.SysExtra.DynamaxLevel + useItemInfo.Quantity
		if level > 10 {
			level = 10
		}
		pokemon, err := changeDynamaxLevel(ctx, logger, tx, trainer, pokemon, level)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		response.TargetPoke = pokemon
	} else if useItemInfo.ItemName == "abilitycapsule" {
		psInfo, exists := poke.GetPokemonInfo(pokemon.Name)
		if !exists {
			return nil, runtime.NewError("pokemon info not found", 404)
		}
		if pokemon.Ability == psInfo.Abilities.Key0 && psInfo.Abilities.Key1 != "" {
			pokemon.Ability = psInfo.Abilities.Key1
		} else {
			pokemon.Ability = psInfo.Abilities.Key0
		}
		err := poke.UpdatePokeData(ctx, tx, pokemon)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		response.TargetPoke = pokemon
	} else if useItemInfo.ItemName == "abilitypatch" {
		psInfo, exists := poke.GetPokemonInfo(pokemon.Name)
		if !exists {
			return nil, runtime.NewError("pokemon info not found", 404)
		}
		pokemon.Ability = psInfo.Abilities.H
		err := poke.UpdatePokeData(ctx, tx, pokemon)
		if err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, useItemInfo.ItemName, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
		response.TargetPoke = pokemon
	}

	return response, nil
}
func changeDynamaxLevel(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokemon *MainServer.Poke, level int32) (*MainServer.Poke, error) {
	pokemon.SysExtra.DynamaxLevel = level
	return pokemon, poke.UpdatePokeData(ctx, tx, pokemon)
}
func changeLevel(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokemon *MainServer.Poke, level int32) (*MainServer.Poke, error) {
	pokeInfo, exists := poke.GetPokemonInfo(pokemon.Name)
	if !exists {
		return nil, runtime.NewError("pokemon info not found", 404)
	}
	// 获取成长率数据
	growthRate, exists := poke.GetPokemonGrowthRate(pokeInfo.GrowthRate)
	if !exists {
		return nil, runtime.NewError("无效的growthRate: 找不到对应的成长率数据", 500)
	}
	exp := growthRate.Levels[level]
	pokemon.Experience = int64(exp)
	pokemon.Level = level
	//查看是否有技能
	return pokemon, poke.UpdatePokeData(ctx, tx, pokemon)
}
func RpcPokeChangeNature(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var changeNatureRequest = &MainServer.RpcPokeChangeNatureRequest{}
	if err := tool.Base64ToProto(payload, changeNatureRequest); err != nil {
		logger.Error("解析 useItemInfo 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	if changeNatureRequest.UseCount <= 0 {
		changeNatureRequest.UseCount = 1
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tid := trainer.Id
	response := &MainServer.RpcPokeChangeNatureResponse{
		PokeId:  changeNatureRequest.PokeId,
		Success: false,
		Nature:  changeNatureRequest.Nature,
	}

	success, err := changeNature(ctx, logger, tx, trainer, changeNatureRequest.PokeId, changeNatureRequest.Nature, changeNatureRequest.UseCount)
	if err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", tid, changeNatureRequest.Nature, err)
		return "", runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	response.Success = success
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}
func RpcUseCapItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var useCapRequest = &MainServer.RpcPokeUseCapRequest{}
	if err := tool.Base64ToProto(payload, useCapRequest); err != nil {
		logger.Error("解析 useItemInfo 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务

	// 调用 useCapItem 处理 poke 使用 cap 的逻辑
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tid := trainer.Id
	response, err := useCapItem(ctx, logger, tx, trainer, useCapRequest.PokeId, useCapRequest.CapName, useCapRequest.StatKey)
	if err != nil {
		logger.Error("用户 %s 使用 poke cap 失败: %v", tid, err)
		return "", runtime.NewError(fmt.Sprintf("poke cap 使用失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 返回结果
	return tool.ProtoToBase64(response)
}
func RpcChangeMove(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var changeMoveRequest = &MainServer.RpcPokeChangeMoveRequest{}
	if err := tool.Base64ToProto(payload, changeMoveRequest); err != nil {
		logger.Error("解析 useItemInfo 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tid := trainer.Id
	response := &MainServer.RpcPokeChangeMoveResponse{
		PokeId:  changeMoveRequest.PokeId,
		Success: false,
	}
	moves, err := changeMove(ctx, logger, tx, trainer, changeMoveRequest.PokeId, changeMoveRequest.MoveName, changeMoveRequest.MoveIndex, changeMoveRequest.IsRemove)
	if err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", tid, changeMoveRequest.MoveName, err)
		return "", runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	response.Success = true
	response.Moves = moves
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}

//	func RpcPokeChangeAbility(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
//		var changeAbilityRequest = &MainServer.RpcPokeChangeAbilityRequest{}
//		if err := tool.Base64ToProto(payload, changeAbilityRequest); err != nil {
//			logger.Error("解析 useItemInfo 失败: %v", err) // 记录解析失败
//			return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
//		}
//		if changeAbilityRequest.UseCount <= 0 {
//			changeAbilityRequest.UseCount = 1
//		}
//		// 开启事务
//		tx, err := db.BeginTx(ctx, nil)
//		if err != nil {
//			logger.Error("事务启动失败: %v", err)
//			return "", fmt.Errorf("failed to start transaction: %w", err)
//		}
//		defer tx.Rollback() // 确保失败时回滚事务
//		trainer := tool.GetActiveTrainerByCtx(ctx)
//		if trainer == nil {
//			userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
//			logger.Error("未找到用户的 active tid %s", userID)
//			return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
//		}
//		tid := trainer.Id
//		response := &MainServer.RpcPokeChangeAbilityResponse{
//			PokeId:  changeAbilityRequest.PokeId,
//			Success: false,
//		}
//		success, err := changeAbility(ctx, logger, tx, trainer, changeAbilityRequest.PokeId, changeAbilityRequest.Ability, changeAbilityRequest.UseCount)
//		if err != nil {
//			logger.Error("用户 %s 使用道具 %s 失败: %v", tid, changeAbilityRequest.Ability, err)
//			return "", runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
//		}
//		response.Success = success
//		// 提交事务
//		if err := tx.Commit(); err != nil {
//			logger.Error("事务提交失败: %v", err)
//			return "", fmt.Errorf("failed to commit transaction: %w", err)
//		}
//		return tool.ProtoToBase64(response)
//	}
func RpcPokeChangeEvs(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var changeEvsRequest = &MainServer.RpcPokeChangeEvsRequest{}
	if err := tool.Base64ToProto(payload, changeEvsRequest); err != nil {
		logger.Error("解析 useItemInfo 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("事务启动失败: %v", err)
		return "", fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback() // 确保失败时回滚事务
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	tid := trainer.Id
	response := &MainServer.RpcPokeChangeEvsResponse{
		PokeId: changeEvsRequest.PokeId,
	}
	evs, err := changeEvs(ctx, logger, tx, trainer, changeEvsRequest.PokeId, changeEvsRequest.Evs)
	if err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", tid, changeEvsRequest.Evs, err)
		return "", runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	response.Evs = evs
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(response)
}
func changeEvs(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeId int64, evs *MainServer.PokeStat) (*MainServer.PokeStat, error) {
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
	if pokemon == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	hpwing := "healthwing"
	musclewing := "musclewing"
	resistwing := "resistwing"
	geniuswing := "geniuswing"
	cleverwing := "cleverwing"
	swiftwing := "swiftwing"
	if evs.Hp != 0 {
		count := int32(math.Abs(float64(evs.Hp)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, hpwing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, hpwing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	if evs.Atk != 0 {
		count := int32(math.Abs(float64(evs.Atk)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, musclewing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, musclewing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	if evs.Def != 0 {
		count := int32(math.Abs(float64(evs.Def)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, resistwing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, resistwing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	if evs.Spa != 0 {
		count := int32(math.Abs(float64(evs.Spa)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, geniuswing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, geniuswing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	if evs.Spd != 0 {
		count := int32(math.Abs(float64(evs.Spd)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, cleverwing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, cleverwing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	if evs.Spe != 0 {
		count := int32(math.Abs(float64(evs.Spe)))
		if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, swiftwing, count); err != nil {
			logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, swiftwing, err)
			return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
		}
	}
	pokemon.Evs.Hp = evsAdd(pokemon.Evs.Hp, evs.Hp)
	pokemon.Evs.Atk = evsAdd(pokemon.Evs.Atk, evs.Atk)
	pokemon.Evs.Def = evsAdd(pokemon.Evs.Def, evs.Def)
	pokemon.Evs.Spa = evsAdd(pokemon.Evs.Spa, evs.Spa)
	pokemon.Evs.Spd = evsAdd(pokemon.Evs.Spd, evs.Spd)
	pokemon.Evs.Spe = evsAdd(pokemon.Evs.Spe, evs.Spe)
	return pokemon.Evs, poke.UpdatePokeData(ctx, tx, pokemon)
}
func evsAdd(value int32, num int32) int32 {
	value += num
	if value > 255 {
		value = 255
	}
	if value < 0 {
		value = 0
	}
	return value
}

// func changeAbility(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeId int64, abilityName string, count int32) (bool, error) {
// 	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
// 	if pokemon == nil {
// 		return false, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
// 	}

//		contains, err := poke.ContainsAbility(abilityName, pokemon.Name)
//		if err != nil {
//			return false, err
//		}
//		if !contains {
//			return false, runtime.NewError("ability not found", 404)
//		}
//		isHidden, err := poke.IsHiddenAbility(abilityName, pokemon.Name)
//		if err != nil {
//			return false, err
//		}
//		if isHidden {
//			changeAbilityItemName := "abilitypatch"
//			if err := inventory.RemoveItem(ctx, tx, trainer.Id, changeAbilityItemName, 1); err != nil {
//				logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, changeAbilityItemName, err)
//				return false, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
//			}
//		} else {
//			changeAbilityItemName := "abilitycapsule"
//			if err := inventory.RemoveItem(ctx, tx, trainer.Id, changeAbilityItemName, 1); err != nil {
//				logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, changeAbilityItemName, err)
//				return false, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
//			}
//		}
//		//成功概率10% 数量越多成功率越高
//		if rand.Float32() > 0.1*float32(count) {
//			return false, nil
//		}
//		pokemon.Ability = abilityName
//		return true, poke.UpdatePokeData(ctx, tx, pokemon)
//	}
func changeMove(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeId int64, moveName string, moveIndex int32, isRemove bool) ([]*MainServer.PokeSimpleMove, error) {
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
	if pokemon == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	if isRemove {
		if moveIndex >= int32(len(pokemon.Moves)) {
			return nil, runtime.NewError("move index out of range", 404)
		}
		pokemon.Moves[moveIndex] = nil
		return pokemon.Moves, poke.UpdatePokeData(ctx, tx, pokemon)
	}
	movefilter := &poke.MoveFilter{
		PokemonName: pokemon.Name,
		Gen:         0,
		Level:       int32(pokemon.Level),
		Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
		IsInit:      false,
	}
	moves := poke.GetFilteredMoves(*movefilter)
	if !contains(moves, moveName) {
		return nil, runtime.NewError("pokemon can not learn this move", 404)
	}
	changeMoveItemName := "heartscale"
	if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, changeMoveItemName, 1); err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, changeMoveItemName, err)
		return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	//成功概率10%
	// if rand.Float32() > 0.1 {
	// 	return nil, nil
	// }
	if moveIndex >= 4 {
		return nil, runtime.NewError("move index out of range", 404)
	}
	if moveIndex >= int32(len(pokemon.Moves)) {
		pokemon.Moves = append(pokemon.Moves, make([]*MainServer.PokeSimpleMove, moveIndex-int32(len(pokemon.Moves))+1)...)
	}
	pokemon.Moves[moveIndex] = &MainServer.PokeSimpleMove{Name: moveName}
	return pokemon.Moves, poke.UpdatePokeData(ctx, tx, pokemon)
}
func changeNature(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeId int64, nature MainServer.Nature, count int32) (bool, error) {
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
	if pokemon == nil {
		return false, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	mintName := mintNameByNature(nature)
	if mintName == "" {
		return false, runtime.NewError("nature not found", 404)
	}
	if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, mintName, 1); err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, mintName, err)
		return false, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}
	//成功概率10% 数量越多成功率越高
	if rand.Float32() > 0.1*float32(count) {
		return false, nil
	}
	pokemon.Nature = nature
	return true, poke.UpdatePokeData(ctx, tx, pokemon)
}
func wingNameByStatKey(statKey MainServer.PokemonStatKey) string {
	switch statKey {
	case MainServer.PokemonStatKey_StatKey_HP:
		return "healthwing"
	case MainServer.PokemonStatKey_StatKey_Attack:
		return "musclewing"
	case MainServer.PokemonStatKey_StatKey_Defense:
		return "resistwing"
	case MainServer.PokemonStatKey_StatKey_SpecialAttack:
		return "geniuswing"
	case MainServer.PokemonStatKey_StatKey_SpecialDefense:
		return "cleverwing"
	case MainServer.PokemonStatKey_StatKey_Speed:
		return "swiftwing"
	}
	return ""
}
func mintNameByNature(nature MainServer.Nature) string {
	switch nature {
	case MainServer.Nature_ADAMANT:
		return "adamantmint"
	case MainServer.Nature_BASHFUL:
		return ""
	case MainServer.Nature_BOLD:
		return "boldmint"
	case MainServer.Nature_BRAVE:
		return "bravemint"
	case MainServer.Nature_CALM:
		return "calmmint"
	case MainServer.Nature_CAREFUL:
		return "carefulmint"
	case MainServer.Nature_DOCILE:
		return ""
	case MainServer.Nature_GENTLE:
		return "gentlemint"
	case MainServer.Nature_HARDY:
		return ""
	case MainServer.Nature_HASTY:
		return "hastymint"
	case MainServer.Nature_IMPISH:
		return "impishmint"
	case MainServer.Nature_JOLLY:
		return "jollymint"
	case MainServer.Nature_LAX:
		return "laxmint"
	case MainServer.Nature_LONELY:
		return "lonelymint"
	case MainServer.Nature_MILD:
		return "mildmint"
	case MainServer.Nature_MODEST:
		return "modestmint"
	case MainServer.Nature_NAIVE:
		return "naivemint"
	case MainServer.Nature_NAUGHTY:
		return "naughtymint"
	case MainServer.Nature_QUIET:
		return "quietmint"
	case MainServer.Nature_QUIRKY:
		return ""
	case MainServer.Nature_RASH:
		return "rashmint"
	case MainServer.Nature_RELAXED:
		return "relaxedmint"
	case MainServer.Nature_SASSY:
		return "sassymint"
	case MainServer.Nature_SERIOUS:
		return "seriousmint"
	case MainServer.Nature_TIMID:
		return "timidmint"
	}
	return ""
}
func useCapItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeId int64, itemName string, statKey MainServer.PokemonStatKey) (*MainServer.RpcPokeUseCapResponse, error) {
	if _, err := inventory.RemoveNormalItemByItemName(ctx, tx, trainer.Id, itemName, 1); err != nil {
		logger.Error("用户 %s 使用道具 %s 失败: %v", trainer.Id, itemName, err)
		return nil, runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
	}

	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
	if pokemon == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}

	var ivPtr *int32
	switch statKey {
	case MainServer.PokemonStatKey_StatKey_HP:
		ivPtr = &pokemon.Ivs.Hp
	case MainServer.PokemonStatKey_StatKey_Attack:
		ivPtr = &pokemon.Ivs.Atk
	case MainServer.PokemonStatKey_StatKey_Defense:
		ivPtr = &pokemon.Ivs.Def
	case MainServer.PokemonStatKey_StatKey_SpecialAttack:
		ivPtr = &pokemon.Ivs.Spa
	case MainServer.PokemonStatKey_StatKey_SpecialDefense:
		ivPtr = &pokemon.Ivs.Spd
	case MainServer.PokemonStatKey_StatKey_Speed:
		ivPtr = &pokemon.Ivs.Spe
	default:
		return nil, runtime.NewError("未知的属性", 400)
	}

	iv := int(*ivPtr)
	if iv >= 31 {
		return nil, runtime.NewError("不需要对31以上的poke进行使用", 400)
	}

	type rule struct{ success, noEffect, decrease int }
	var r rule
	response := &MainServer.RpcPokeUseCapResponse{PokeId: pokeId, UseResult: 0}

	// 处理固定成功逻辑
	switch itemName {
	case "bottlecap":
		if iv < 20 {
			*ivPtr++
			response.UseResult = 1
			poke.UpdatePokeData(ctx, tx, pokemon)
			return response, nil
		} else if iv < 26 {
			r = rule{success: 50, noEffect: 40, decrease: 10}
		} else if iv < 31 {
			r = rule{success: 30, noEffect: 40, decrease: 30}
		} else {
			return nil, runtime.NewError("IV 超出处理范围", 400)
		}
	case "goldbottlecap":
		if iv < 25 {
			*ivPtr++
			response.UseResult = 1
			poke.UpdatePokeData(ctx, tx, pokemon)
			return response, nil
		} else if iv < 31 {
			r = rule{success: 50, noEffect: 30, decrease: 20}
		} else {
			return nil, runtime.NewError("IV 超出处理范围", 400)
		}
	default:
		return nil, runtime.NewError("未知的道具", 400)
	}

	// 概率判定
	rand.Seed(time.Now().UnixNano())
	roll := rand.Intn(100)

	switch {
	case roll < r.success:
		*ivPtr++
		response.UseResult = 1
	case roll < r.success+r.noEffect:
		// 无效，UseResult 保持为 0
	default:
		if iv > 0 {
			*ivPtr--
		}
		response.UseResult = -1
	}

	poke.UpdatePokeData(ctx, tx, pokemon)
	return response, nil
}
func contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// // UseItem 负责检查并扣除用户道具
// func UseItem(ctx context.Context, logger runtime.Logger, db *sql.DB, useItemInfo *MainServer.UseItemInfo) (string, error) {

// }

// // TryUseItem 负责处理道具使用逻辑
// func tryUseItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, itemPayload ItemPayload) (string, error) {
// 	// 解析传入的 payload
// 	// var itemPayload ItemPayload
// 	// if err := json.Unmarshal([]byte(payload), &itemPayload); err != nil {
// 	// 	logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
// 	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
// 	// }

// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

// 	// 从用户 Metadata 中获取当前用户状态
// 	users, err := nk.UsersGetId(ctx, []string{userID}, []string{})
// 	if err != nil || len(users) == 0 {
// 		logger.Error("未找到用户 %s: %v", userID, err) // 记录用户未找到错误
// 		return "", runtime.NewError("未找到用户", 404)
// 	}
// 	user := users[0]

// 	var metadata map[string]interface{}
// 	if err := json.Unmarshal([]byte(user.Metadata), &metadata); err != nil {
// 		logger.Error("解析用户 %s 的元数据失败: %v", userID, err) // 记录元数据解析失败
// 		return "", runtime.NewError("解析用户元数据失败", 500)
// 	}

// 	// 假设用户状态包含一个 actionType 字段 (normal, battle, etc.)
// 	actionType, ok := metadata["actionType"].(string)
// 	if !ok {
// 		logger.Error("用户 %s 的 actionType 不合法", userID) // 记录非法的 actionType 错误
// 		return "", runtime.NewError("用户状态不合法", 500)
// 	}

// 	// 根据 actionType 和道具配置表判断是否允许使用该道具
// 	itemConfig, err := getItemConfig(ctx, nk, itemPayload.ItemID, actionType)
// 	if err != nil || !itemConfig.CanUse {
// 		logger.Warn("用户 %s 在场景 %s 中无法使用道具 %s", userID, actionType, itemPayload.ItemID) // 记录道具无法使用警告
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_ItemCannotUseHere)
// 	}

// 	// 调用 useItem 扣除道具数量并处理结果
// 	result, err := useItem(ctx, logger, db, nk, itemPayload)
// 	if err != nil {
// 		logger.Error("用户 %s 使用道具 %s 失败: %v", userID, itemPayload.ItemID, err) // 记录道具使用失败
// 		return "", runtime.NewError(fmt.Sprintf("道具使用失败: %v", err), 500)
// 	}

// 	return result, nil
// }

// // useItem 负责检查并扣除用户道具
// func useItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, itemPayload ItemPayload) (string, error) {
// 	// 解析传入的 payload
// 	// var itemPayload ItemPayload
// 	// if err := json.Unmarshal([]byte(payload), &itemPayload); err != nil {
// 	// 	logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
// 	// 	return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
// 	// }

// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

// 	// 从存储中读取用户的道具数据（设置只有服务器可以读）
// 	objects, err := nk.StorageRead(ctx, []*runtime.StorageRead{
// 		{
// 			Collection: "item",
// 			Key:        userID,
// 			UserID:     userID,
// 		},
// 	})
// 	if err != nil || len(objects) == 0 {
// 		logger.Error("未找到用户 %s 的道具数据: %v", userID, err) // 记录读取失败
// 		return "", runtime.NewError("未找到用户的道具信息", 404)
// 	}

// 	// 获取存储的数据对象
// 	storageObject := objects[0]
// 	version := storageObject.Version // 获取版本信息

// 	// 解析存储的道具数据
// 	var userItems map[string]int
// 	if err := json.Unmarshal([]byte(storageObject.Value), &userItems); err != nil {
// 		logger.Error("解析用户 %s 的道具数据失败: %v", userID, err) // 记录解析失败
// 		return "", runtime.NewError("解析道具数据失败", 500)
// 	}

// 	// 检查用户是否有足够的道具
// 	currentQuantity, exists := userItems[itemPayload.ItemID]
// 	if !exists || currentQuantity < itemPayload.Quantity {
// 		logger.Warn("用户 %s 的道具 %s 数量不足: 当前数量 %d, 需要 %d", userID, itemPayload.ItemID, currentQuantity, itemPayload.Quantity) // 记录道具不足警告
// 		return "", runtime.NewError("道具数量不足", 400)
// 	}

// 	// 扣除道具数量
// 	userItems[itemPayload.ItemID] = currentQuantity - itemPayload.Quantity

// 	// 将更新后的数据写回存储，使用乐观锁机制，确保只有服务器可以写
// 	updatedData, err := json.Marshal(userItems)
// 	if err != nil {
// 		logger.Error("序列化更新后的道具数据失败: %v", err) // 记录序列化失败
// 		return "", runtime.NewError("更新道具数据失败", 500)
// 	}

// 	_, err = nk.StorageWrite(ctx, []*runtime.StorageWrite{
// 		{
// 			Collection:      itemCollectionName,
// 			Key:             userID,
// 			UserID:          userID,
// 			Value:           string(updatedData),
// 			Version:         version, // 使用读取时的版本信息
// 			PermissionRead:  2,       // 只有服务器可以读
// 			PermissionWrite: 0,       // 只有服务器可以写
// 		},
// 	})
// 	if err != nil {
// 		// 检查是否是版本冲突
// 		if err != nil {
// 			// 检查是否是版本冲突
// 			// if runtime.IsStorageVersionMismatch(err) {
// 			// 	logger.Warn("数据冲突: 用户 %s 的道具数据版本不匹配", userID) // 记录数据冲突警告
// 			// 	return "", runtime.NewError("数据冲突，请重试", 409)
// 			// }
// 			logger.Error("用户 %s 的道具数据保存失败: %v", userID, err) // 记录道具保存失败
// 			return "", runtime.NewError("道具数据保存失败", 500)
// 		}
// 		logger.Error("用户 %s 的道具数据保存失败: %v", userID, err) // 记录道具保存失败
// 		return "", runtime.NewError("道具数据保存失败", 500)
// 	}

// 	logger.Info("用户 %s 成功使用了 %d 个道具 %s", userID, itemPayload.Quantity, itemPayload.ItemID) // 记录成功操作
// 	return fmt.Sprintf("成功使用了 %d 个道具 %s", itemPayload.Quantity, itemPayload.ItemID), nil
// }

// // 模拟道具配置表的获取逻辑，根据 actionType 获取道具是否可用的配置信息
// type ItemConfig struct {
// 	CanUse bool
// }

// func getItemConfig(ctx context.Context, nk runtime.NakamaModule, itemID string, actionType string) (*ItemConfig, error) {
// 	// 假设配置表存储在某个系统 Collection 中，这里是模拟读取
// 	// 根据 actionType 和 itemID 返回对应的配置信息
// 	config := map[string]map[string]ItemConfig{
// 		"normal": {
// 			"item1": {CanUse: true},
// 			"item2": {CanUse: false},
// 		},
// 		"battle": {
// 			"item1": {CanUse: false},
// 			"item2": {CanUse: true},
// 		},
// 	}

// 	if actionConfig, exists := config[actionType][itemID]; exists {
// 		return &actionConfig, nil
// 	}

// 	return nil, runtime.NewError("未找到道具配置", 500)
// }

// func TryUserItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	//payloa会传输一个道具id和数量过来
// 	//尝试使用道具，这边会去读取用户的状态Metadata，然后根据用户状态判断是否可以使用道具，所以还有一个道具配置表，这边会去判断什么场景下什么道具可以使用 、、比如有一个actionType enum 包括normal、battle等
// 	//假如可以使用则调用useItem
// }

// func useItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	//payloa会传输一个道具id和数量过来
// 	//判断Collection name为item的，key为用户id的存储数据上，是否有道具，然后判断道具的数量是否足够
// 	 //够的话扣除相应数量的道具
// }
