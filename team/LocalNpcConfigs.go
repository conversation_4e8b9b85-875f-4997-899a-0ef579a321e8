package poketeam

import (
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"google.golang.org/protobuf/encoding/protojson"
)

var localNpcConfigs map[string]*MainServer.NpcRoleConfig

func LoadLocalNpcConfigsInfos() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/LubanNpcRoleConfig.json")
	if err != nil {
		log.Fatalf("Failed to read LubanNpcRoleConfig.json file: %v", err)
	}
	configs := &MainServer.NpcRoleConfigList{}
	// 解析 JSON 数据直接到全局变量
	if err := protojson.Unmarshal(data, configs); err != nil {
		log.Fatalf("Failed to parse LubanNpcRoleConfig.json: %v", err)
	}
	localNpcConfigs = configs.Configs
	log.Printf("Successfully loaded %d Npc entries into localNpcConfigs.", len(configs.Configs))
}

func GetNpcConfig(nameId string) (*MainServer.NpcRoleConfig, bool) {
	config, exists := localNpcConfigs[nameId]
	if !exists {
		return nil, exists
	}
	return config, true
}
