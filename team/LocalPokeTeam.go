package poketeam

import (
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"google.golang.org/protobuf/encoding/protojson"
)

var localPokeTeams map[string]*MainServer.PokeTeamConfig

func LoadLocalPokeTeamsInfos() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/PokeTeamConfig.json")
	if err != nil {
		log.Fatalf("Failed to read PokeTeamConfig.json file: %v", err)
	}
	configs := &MainServer.PokeTeamConfigList{}
	// 解析 JSON 数据直接到全局变量
	if err := protojson.Unmarshal(data, configs); err != nil {
		log.Fatalf("Failed to parse PokeTeamConfig.json: %v", err)
	}
	localPokeTeams = configs.Configs
	log.Printf("Successfully loaded %d PokeTeam entries into localPokeTeams.", len(configs.Configs))
}

func GetPokeTeam(nameId string) (*MainServer.PokeTeamConfig, bool) {
	config, exists := localPokeTeams[nameId]
	if !exists {
		return nil, exists
	}
	return config, true
}
