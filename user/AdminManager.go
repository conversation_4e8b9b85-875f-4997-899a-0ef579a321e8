package user

import (
	"context"
	"database/sql"
	"encoding/json"
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

func IsBattleServer(ctx context.Context, nk runtime.NakamaModule, userID string) (bool, error) {
	// 从用户元数据中获取用户信息
	users, err := nk.UsersGetId(ctx, []string{userID}, []string{})
	if err != nil || len(users) == 0 {
		return false, runtime.NewError("用户未找到", 404)
	}

	user := users[0]
	// 假设管理员权限存储在用户元数据的 "role" 字段中
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(user.Metadata), &metadata); err != nil {
		return false, runtime.NewError("元数据解析失败", 500)
	}

	role, exists := metadata["role"]
	if exists && role == "battle" {
		return true, nil
	}

	return false, nil
}
func IsDataServer(ctx context.Context, nk runtime.NakamaModule, userID string) (bool, error) {
	// 从用户元数据中获取用户信息
	users, err := nk.UsersGetId(ctx, []string{userID}, []string{})
	if err != nil || len(users) == 0 {
		return false, runtime.NewError("用户未找到", 404)
	}

	user := users[0]
	// 假设管理员权限存储在用户元数据的 "role" 字段中
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(user.Metadata), &metadata); err != nil {
		return false, runtime.NewError("元数据解析失败", 500)
	}

	role, exists := metadata["role"]
	if exists && role == "data" {
		return true, nil
	}

	return false, nil
}

// 检查用户是否是管理员
func IsAdmin(ctx context.Context, nk runtime.NakamaModule, userID string) (bool, error) {
	if userID == "00000000-0000-0000-0000-000000000000" {
		return true, nil
	}
	// return true, nil
	// 从用户元数据中获取用户信息
	users, err := nk.UsersGetId(ctx, []string{userID}, []string{})
	if err != nil || len(users) == 0 {
		return false, runtime.NewError("用户未找到", 404)
	}

	user := users[0]
	// 假设管理员权限存储在用户元数据的 "role" 字段中
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(user.Metadata), &metadata); err != nil {
		return false, runtime.NewError("元数据解析失败", 500)
	}

	role, exists := metadata["role"]
	if exists && role == "admin" {
		return true, nil
	}

	return false, nil
}

func UdpateBattleRole(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return updateUserRole(ctx, logger, db, nk, "{\"target_user_id\":\""+payload+"\",\"role\":\"battle\"}")
}

func UdpateDataRole(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return updateUserRole(ctx, logger, db, nk, "{\"target_user_id\":\""+payload+"\",\"role\":\"data\"}")
}

func updateUserRole(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前调用者的用户ID
	userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", runtime.NewError("无法获取用户ID", 500)
	}

	// 检查调用者是否是管理员
	isAdmin, err := IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有权限修改数据", 403)
	}

	// 解析请求数据，假设传入了目标用户ID和新角色
	var requestData struct {
		TargetUserID string `json:"target_user_id"`
		Role         string `json:"role"`
	}
	err = json.Unmarshal([]byte(payload), &requestData)
	if err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	// 更新目标用户的角色
	metadata := map[string]interface{}{
		"role": requestData.Role,
	}
	err = updateUserStatusMetadata(ctx, logger, nk, userID, metadata)
	// update := []*runtime.AccountUpdate{
	// 	{
	// 		UserID:   requestData.TargetUserID,
	// 		Metadata: metadata,
	// 	},
	// }

	// _, _, err = nk.MultiUpdate(ctx, update, nil, nil, nil, false)
	if err != nil {
		logger.Error("无法更新用户角色: %v", err)
		return "", runtime.NewError("更新用户角色失败", 500)
	}

	return "{\"message\":\"用户角色更新成功\"}", nil
}
