// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerBadge.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerBadgeType int32

const (
	TrainerBadgeType_TrainerBadge_None          TrainerBadgeType = 0
	TrainerBadgeType_TrainerBadge_Kanto_Boulder TrainerBadgeType = 1 //灰色徽章
	TrainerBadgeType_TrainerBadge_Kanto_Cascade TrainerBadgeType = 2 //蓝色徽章
	TrainerBadgeType_TrainerBadge_Kanto_Thunder TrainerBadgeType = 3 //橙色徽章
	TrainerBadgeType_TrainerBadge_Kanto_Rainbow TrainerBadgeType = 4 //彩虹徽章
	TrainerBadgeType_TrainerBadge_Kanto_Soul    TrainerBadgeType = 5 //粉红徽章
	TrainerBadgeType_TrainerBadge_Kanto_Marsh   TrainerBadgeType = 6 //金色徽章
	TrainerBadgeType_TrainerBadge_Kanto_Volcano TrainerBadgeType = 7 //深红徽章
	TrainerBadgeType_TrainerBadge_Kanto_Earth   TrainerBadgeType = 8 //绿色徽章
	TrainerBadgeType_TrainerBadge_Johto_Zephyr  TrainerBadgeType = 9
	TrainerBadgeType_TrainerBadge_Johto_Hive    TrainerBadgeType = 10
	TrainerBadgeType_TrainerBadge_Johto_Plain   TrainerBadgeType = 11
	TrainerBadgeType_TrainerBadge_Johto_Fog     TrainerBadgeType = 12
	TrainerBadgeType_TrainerBadge_Johto_Storm   TrainerBadgeType = 13
	TrainerBadgeType_TrainerBadge_Johto_Mineral TrainerBadgeType = 14
	TrainerBadgeType_TrainerBadge_Johto_Glacier TrainerBadgeType = 15
	TrainerBadgeType_TrainerBadge_Johto_Rising  TrainerBadgeType = 16
	TrainerBadgeType_TrainerBadge_Hoenn_Stone   TrainerBadgeType = 17
	TrainerBadgeType_TrainerBadge_Hoenn_Knuckle TrainerBadgeType = 18
	TrainerBadgeType_TrainerBadge_Hoenn_Dynamo  TrainerBadgeType = 19
	TrainerBadgeType_TrainerBadge_Hoenn_Heat    TrainerBadgeType = 20
	TrainerBadgeType_TrainerBadge_Hoenn_Balance TrainerBadgeType = 21
	TrainerBadgeType_TrainerBadge_Hoenn_Feather TrainerBadgeType = 22
	TrainerBadgeType_TrainerBadge_Hoenn_Mind    TrainerBadgeType = 23
	TrainerBadgeType_TrainerBadge_Hoenn_Rain    TrainerBadgeType = 24
	TrainerBadgeType_TrainerBadge_Sinnoh_Coal   TrainerBadgeType = 25
	TrainerBadgeType_TrainerBadge_Sinnoh_Forest TrainerBadgeType = 26
	TrainerBadgeType_TrainerBadge_Sinnoh_Cobble TrainerBadgeType = 27
	TrainerBadgeType_TrainerBadge_Sinnoh_Fen    TrainerBadgeType = 28
	TrainerBadgeType_TrainerBadge_Sinnoh_Relic  TrainerBadgeType = 29
	TrainerBadgeType_TrainerBadge_Sinnoh_Mine   TrainerBadgeType = 30
	TrainerBadgeType_TrainerBadge_Sinnoh_Icicle TrainerBadgeType = 31
	TrainerBadgeType_TrainerBadge_Sinnoh_Beacon TrainerBadgeType = 32
	TrainerBadgeType_TrainerBadge_Unova_Trio    TrainerBadgeType = 33
	TrainerBadgeType_TrainerBadge_Unova_Basic   TrainerBadgeType = 34
	TrainerBadgeType_TrainerBadge_Unova_Toxic   TrainerBadgeType = 35
	TrainerBadgeType_TrainerBadge_Unova_Insect  TrainerBadgeType = 36
	TrainerBadgeType_TrainerBadge_Unova_Bolt    TrainerBadgeType = 37
	TrainerBadgeType_TrainerBadge_Unova_Quake   TrainerBadgeType = 38
	TrainerBadgeType_TrainerBadge_Unova_Jet     TrainerBadgeType = 39
	TrainerBadgeType_TrainerBadge_Unova_Freeze  TrainerBadgeType = 40
	TrainerBadgeType_TrainerBadge_Unova_Legend  TrainerBadgeType = 41
	TrainerBadgeType_TrainerBadge_Unova_Wave    TrainerBadgeType = 42
)

// Enum value maps for TrainerBadgeType.
var (
	TrainerBadgeType_name = map[int32]string{
		0:  "TrainerBadge_None",
		1:  "TrainerBadge_Kanto_Boulder",
		2:  "TrainerBadge_Kanto_Cascade",
		3:  "TrainerBadge_Kanto_Thunder",
		4:  "TrainerBadge_Kanto_Rainbow",
		5:  "TrainerBadge_Kanto_Soul",
		6:  "TrainerBadge_Kanto_Marsh",
		7:  "TrainerBadge_Kanto_Volcano",
		8:  "TrainerBadge_Kanto_Earth",
		9:  "TrainerBadge_Johto_Zephyr",
		10: "TrainerBadge_Johto_Hive",
		11: "TrainerBadge_Johto_Plain",
		12: "TrainerBadge_Johto_Fog",
		13: "TrainerBadge_Johto_Storm",
		14: "TrainerBadge_Johto_Mineral",
		15: "TrainerBadge_Johto_Glacier",
		16: "TrainerBadge_Johto_Rising",
		17: "TrainerBadge_Hoenn_Stone",
		18: "TrainerBadge_Hoenn_Knuckle",
		19: "TrainerBadge_Hoenn_Dynamo",
		20: "TrainerBadge_Hoenn_Heat",
		21: "TrainerBadge_Hoenn_Balance",
		22: "TrainerBadge_Hoenn_Feather",
		23: "TrainerBadge_Hoenn_Mind",
		24: "TrainerBadge_Hoenn_Rain",
		25: "TrainerBadge_Sinnoh_Coal",
		26: "TrainerBadge_Sinnoh_Forest",
		27: "TrainerBadge_Sinnoh_Cobble",
		28: "TrainerBadge_Sinnoh_Fen",
		29: "TrainerBadge_Sinnoh_Relic",
		30: "TrainerBadge_Sinnoh_Mine",
		31: "TrainerBadge_Sinnoh_Icicle",
		32: "TrainerBadge_Sinnoh_Beacon",
		33: "TrainerBadge_Unova_Trio",
		34: "TrainerBadge_Unova_Basic",
		35: "TrainerBadge_Unova_Toxic",
		36: "TrainerBadge_Unova_Insect",
		37: "TrainerBadge_Unova_Bolt",
		38: "TrainerBadge_Unova_Quake",
		39: "TrainerBadge_Unova_Jet",
		40: "TrainerBadge_Unova_Freeze",
		41: "TrainerBadge_Unova_Legend",
		42: "TrainerBadge_Unova_Wave",
	}
	TrainerBadgeType_value = map[string]int32{
		"TrainerBadge_None":          0,
		"TrainerBadge_Kanto_Boulder": 1,
		"TrainerBadge_Kanto_Cascade": 2,
		"TrainerBadge_Kanto_Thunder": 3,
		"TrainerBadge_Kanto_Rainbow": 4,
		"TrainerBadge_Kanto_Soul":    5,
		"TrainerBadge_Kanto_Marsh":   6,
		"TrainerBadge_Kanto_Volcano": 7,
		"TrainerBadge_Kanto_Earth":   8,
		"TrainerBadge_Johto_Zephyr":  9,
		"TrainerBadge_Johto_Hive":    10,
		"TrainerBadge_Johto_Plain":   11,
		"TrainerBadge_Johto_Fog":     12,
		"TrainerBadge_Johto_Storm":   13,
		"TrainerBadge_Johto_Mineral": 14,
		"TrainerBadge_Johto_Glacier": 15,
		"TrainerBadge_Johto_Rising":  16,
		"TrainerBadge_Hoenn_Stone":   17,
		"TrainerBadge_Hoenn_Knuckle": 18,
		"TrainerBadge_Hoenn_Dynamo":  19,
		"TrainerBadge_Hoenn_Heat":    20,
		"TrainerBadge_Hoenn_Balance": 21,
		"TrainerBadge_Hoenn_Feather": 22,
		"TrainerBadge_Hoenn_Mind":    23,
		"TrainerBadge_Hoenn_Rain":    24,
		"TrainerBadge_Sinnoh_Coal":   25,
		"TrainerBadge_Sinnoh_Forest": 26,
		"TrainerBadge_Sinnoh_Cobble": 27,
		"TrainerBadge_Sinnoh_Fen":    28,
		"TrainerBadge_Sinnoh_Relic":  29,
		"TrainerBadge_Sinnoh_Mine":   30,
		"TrainerBadge_Sinnoh_Icicle": 31,
		"TrainerBadge_Sinnoh_Beacon": 32,
		"TrainerBadge_Unova_Trio":    33,
		"TrainerBadge_Unova_Basic":   34,
		"TrainerBadge_Unova_Toxic":   35,
		"TrainerBadge_Unova_Insect":  36,
		"TrainerBadge_Unova_Bolt":    37,
		"TrainerBadge_Unova_Quake":   38,
		"TrainerBadge_Unova_Jet":     39,
		"TrainerBadge_Unova_Freeze":  40,
		"TrainerBadge_Unova_Legend":  41,
		"TrainerBadge_Unova_Wave":    42,
	}
)

func (x TrainerBadgeType) Enum() *TrainerBadgeType {
	p := new(TrainerBadgeType)
	*p = x
	return p
}

func (x TrainerBadgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerBadgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerBadge_proto_enumTypes[0].Descriptor()
}

func (TrainerBadgeType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerBadge_proto_enumTypes[0]
}

func (x TrainerBadgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerBadgeType.Descriptor instead.
func (TrainerBadgeType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

type TrainerBadges struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Badges        []*TrainerBadge        `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
	LastUpdateTs  int64                  `protobuf:"varint,2,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerBadges) Reset() {
	*x = TrainerBadges{}
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBadges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadges) ProtoMessage() {}

func (x *TrainerBadges) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadges.ProtoReflect.Descriptor instead.
func (*TrainerBadges) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerBadges) GetBadges() []*TrainerBadge {
	if x != nil {
		return x.Badges
	}
	return nil
}

func (x *TrainerBadges) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

type TrainerBadge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerBadgeType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerBadgeType" json:"type,omitempty"`
	CreateTs      int64                  `protobuf:"varint,2,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerBadge) Reset() {
	*x = TrainerBadge{}
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBadge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadge) ProtoMessage() {}

func (x *TrainerBadge) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadge.ProtoReflect.Descriptor instead.
func (*TrainerBadge) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerBadge) GetType() TrainerBadgeType {
	if x != nil {
		return x.Type
	}
	return TrainerBadgeType_TrainerBadge_None
}

func (x *TrainerBadge) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

var File_MainServer_TrainerBadge_proto protoreflect.FileDescriptor

const file_MainServer_TrainerBadge_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/TrainerBadge.proto\x12\n" +
	"MainServer\"g\n" +
	"\rTrainerBadges\x120\n" +
	"\x06badges\x18\x01 \x03(\v2\x18.MainServer.TrainerBadgeR\x06badges\x12$\n" +
	"\x0elast_update_ts\x18\x02 \x01(\x03R\flastUpdateTs\"]\n" +
	"\fTrainerBadge\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerBadgeTypeR\x04type\x12\x1b\n" +
	"\tcreate_ts\x18\x02 \x01(\x03R\bcreateTs*\xab\n" +
	"\n" +
	"\x10TrainerBadgeType\x12\x15\n" +
	"\x11TrainerBadge_None\x10\x00\x12\x1e\n" +
	"\x1aTrainerBadge_Kanto_Boulder\x10\x01\x12\x1e\n" +
	"\x1aTrainerBadge_Kanto_Cascade\x10\x02\x12\x1e\n" +
	"\x1aTrainerBadge_Kanto_Thunder\x10\x03\x12\x1e\n" +
	"\x1aTrainerBadge_Kanto_Rainbow\x10\x04\x12\x1b\n" +
	"\x17TrainerBadge_Kanto_Soul\x10\x05\x12\x1c\n" +
	"\x18TrainerBadge_Kanto_Marsh\x10\x06\x12\x1e\n" +
	"\x1aTrainerBadge_Kanto_Volcano\x10\a\x12\x1c\n" +
	"\x18TrainerBadge_Kanto_Earth\x10\b\x12\x1d\n" +
	"\x19TrainerBadge_Johto_Zephyr\x10\t\x12\x1b\n" +
	"\x17TrainerBadge_Johto_Hive\x10\n" +
	"\x12\x1c\n" +
	"\x18TrainerBadge_Johto_Plain\x10\v\x12\x1a\n" +
	"\x16TrainerBadge_Johto_Fog\x10\f\x12\x1c\n" +
	"\x18TrainerBadge_Johto_Storm\x10\r\x12\x1e\n" +
	"\x1aTrainerBadge_Johto_Mineral\x10\x0e\x12\x1e\n" +
	"\x1aTrainerBadge_Johto_Glacier\x10\x0f\x12\x1d\n" +
	"\x19TrainerBadge_Johto_Rising\x10\x10\x12\x1c\n" +
	"\x18TrainerBadge_Hoenn_Stone\x10\x11\x12\x1e\n" +
	"\x1aTrainerBadge_Hoenn_Knuckle\x10\x12\x12\x1d\n" +
	"\x19TrainerBadge_Hoenn_Dynamo\x10\x13\x12\x1b\n" +
	"\x17TrainerBadge_Hoenn_Heat\x10\x14\x12\x1e\n" +
	"\x1aTrainerBadge_Hoenn_Balance\x10\x15\x12\x1e\n" +
	"\x1aTrainerBadge_Hoenn_Feather\x10\x16\x12\x1b\n" +
	"\x17TrainerBadge_Hoenn_Mind\x10\x17\x12\x1b\n" +
	"\x17TrainerBadge_Hoenn_Rain\x10\x18\x12\x1c\n" +
	"\x18TrainerBadge_Sinnoh_Coal\x10\x19\x12\x1e\n" +
	"\x1aTrainerBadge_Sinnoh_Forest\x10\x1a\x12\x1e\n" +
	"\x1aTrainerBadge_Sinnoh_Cobble\x10\x1b\x12\x1b\n" +
	"\x17TrainerBadge_Sinnoh_Fen\x10\x1c\x12\x1d\n" +
	"\x19TrainerBadge_Sinnoh_Relic\x10\x1d\x12\x1c\n" +
	"\x18TrainerBadge_Sinnoh_Mine\x10\x1e\x12\x1e\n" +
	"\x1aTrainerBadge_Sinnoh_Icicle\x10\x1f\x12\x1e\n" +
	"\x1aTrainerBadge_Sinnoh_Beacon\x10 \x12\x1b\n" +
	"\x17TrainerBadge_Unova_Trio\x10!\x12\x1c\n" +
	"\x18TrainerBadge_Unova_Basic\x10\"\x12\x1c\n" +
	"\x18TrainerBadge_Unova_Toxic\x10#\x12\x1d\n" +
	"\x19TrainerBadge_Unova_Insect\x10$\x12\x1b\n" +
	"\x17TrainerBadge_Unova_Bolt\x10%\x12\x1c\n" +
	"\x18TrainerBadge_Unova_Quake\x10&\x12\x1a\n" +
	"\x16TrainerBadge_Unova_Jet\x10'\x12\x1d\n" +
	"\x19TrainerBadge_Unova_Freeze\x10(\x12\x1d\n" +
	"\x19TrainerBadge_Unova_Legend\x10)\x12\x1b\n" +
	"\x17TrainerBadge_Unova_Wave\x10*B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerBadge_proto_rawDescOnce sync.Once
	file_MainServer_TrainerBadge_proto_rawDescData []byte
)

func file_MainServer_TrainerBadge_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerBadge_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerBadge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerBadge_proto_rawDesc), len(file_MainServer_TrainerBadge_proto_rawDesc)))
	})
	return file_MainServer_TrainerBadge_proto_rawDescData
}

var file_MainServer_TrainerBadge_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerBadge_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerBadge_proto_goTypes = []any{
	(TrainerBadgeType)(0), // 0: MainServer.TrainerBadgeType
	(*TrainerBadges)(nil), // 1: MainServer.TrainerBadges
	(*TrainerBadge)(nil),  // 2: MainServer.TrainerBadge
}
var file_MainServer_TrainerBadge_proto_depIdxs = []int32{
	2, // 0: MainServer.TrainerBadges.badges:type_name -> MainServer.TrainerBadge
	0, // 1: MainServer.TrainerBadge.type:type_name -> MainServer.TrainerBadgeType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerBadge_proto_init() }
func file_MainServer_TrainerBadge_proto_init() {
	if File_MainServer_TrainerBadge_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerBadge_proto_rawDesc), len(file_MainServer_TrainerBadge_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerBadge_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerBadge_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerBadge_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerBadge_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerBadge_proto = out.File
	file_MainServer_TrainerBadge_proto_goTypes = nil
	file_MainServer_TrainerBadge_proto_depIdxs = nil
}
