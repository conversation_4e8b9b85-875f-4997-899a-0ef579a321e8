// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ServerNotification.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerNotificationType int32

const (
	ServerNotificationType_ServerNotificationType_None               ServerNotificationType = 0
	ServerNotificationType_ServerNotificationType_NewVersion         ServerNotificationType = 1
	ServerNotificationType_ServerNotificationType_InviteBattle       ServerNotificationType = 2
	ServerNotificationType_ServerNotificationType_MatchJoin          ServerNotificationType = 3
	ServerNotificationType_ServerNotificationType_SwopInfo           ServerNotificationType = 4
	ServerNotificationType_ServerNotificationType_UpdateTrainer      ServerNotificationType = 5
	ServerNotificationType_ServerNotificationType_InviteParty        ServerNotificationType = 6 //组队邀请
	ServerNotificationType_ServerNotificationType_SyncPartyMatchInfo ServerNotificationType = 7 //队伍匹配信息同步
	ServerNotificationType_ServerNotificationType_Reward             ServerNotificationType = 8 //奖励
	ServerNotificationType_ServerNotificationType_PokeInfo           ServerNotificationType = 9 //精灵信息
	ServerNotificationType_ServerNotificationType_BattleMessage      ServerNotificationType = 100
	ServerNotificationType_ServerNotificationType_BattlePrepare      ServerNotificationType = 101
	ServerNotificationType_ServerNotificationType_BattleInit         ServerNotificationType = 102
	ServerNotificationType_ServerNotificationType_BattleChoice       ServerNotificationType = 103
	ServerNotificationType_ServerNotificationType_BattleResult       ServerNotificationType = 104
	ServerNotificationType_ServerNotificationType_BattleUrge         ServerNotificationType = 105
	ServerNotificationType_ServerNotificationType_NewEmail           ServerNotificationType = 200
)

// Enum value maps for ServerNotificationType.
var (
	ServerNotificationType_name = map[int32]string{
		0:   "ServerNotificationType_None",
		1:   "ServerNotificationType_NewVersion",
		2:   "ServerNotificationType_InviteBattle",
		3:   "ServerNotificationType_MatchJoin",
		4:   "ServerNotificationType_SwopInfo",
		5:   "ServerNotificationType_UpdateTrainer",
		6:   "ServerNotificationType_InviteParty",
		7:   "ServerNotificationType_SyncPartyMatchInfo",
		8:   "ServerNotificationType_Reward",
		9:   "ServerNotificationType_PokeInfo",
		100: "ServerNotificationType_BattleMessage",
		101: "ServerNotificationType_BattlePrepare",
		102: "ServerNotificationType_BattleInit",
		103: "ServerNotificationType_BattleChoice",
		104: "ServerNotificationType_BattleResult",
		105: "ServerNotificationType_BattleUrge",
		200: "ServerNotificationType_NewEmail",
	}
	ServerNotificationType_value = map[string]int32{
		"ServerNotificationType_None":               0,
		"ServerNotificationType_NewVersion":         1,
		"ServerNotificationType_InviteBattle":       2,
		"ServerNotificationType_MatchJoin":          3,
		"ServerNotificationType_SwopInfo":           4,
		"ServerNotificationType_UpdateTrainer":      5,
		"ServerNotificationType_InviteParty":        6,
		"ServerNotificationType_SyncPartyMatchInfo": 7,
		"ServerNotificationType_Reward":             8,
		"ServerNotificationType_PokeInfo":           9,
		"ServerNotificationType_BattleMessage":      100,
		"ServerNotificationType_BattlePrepare":      101,
		"ServerNotificationType_BattleInit":         102,
		"ServerNotificationType_BattleChoice":       103,
		"ServerNotificationType_BattleResult":       104,
		"ServerNotificationType_BattleUrge":         105,
		"ServerNotificationType_NewEmail":           200,
	}
)

func (x ServerNotificationType) Enum() *ServerNotificationType {
	p := new(ServerNotificationType)
	*p = x
	return p
}

func (x ServerNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[0].Descriptor()
}

func (ServerNotificationType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[0]
}

func (x ServerNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerNotificationType.Descriptor instead.
func (ServerNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

type InviteBattleType int32

const (
	InviteBattleType_InviteBattleType_None   InviteBattleType = 0
	InviteBattleType_InviteBattleType_Normal InviteBattleType = 1
	InviteBattleType_InviteBattleType_Force  InviteBattleType = 2
)

// Enum value maps for InviteBattleType.
var (
	InviteBattleType_name = map[int32]string{
		0: "InviteBattleType_None",
		1: "InviteBattleType_Normal",
		2: "InviteBattleType_Force",
	}
	InviteBattleType_value = map[string]int32{
		"InviteBattleType_None":   0,
		"InviteBattleType_Normal": 1,
		"InviteBattleType_Force":  2,
	}
)

func (x InviteBattleType) Enum() *InviteBattleType {
	p := new(InviteBattleType)
	*p = x
	return p
}

func (x InviteBattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InviteBattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[1].Descriptor()
}

func (InviteBattleType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[1]
}

func (x InviteBattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InviteBattleType.Descriptor instead.
func (InviteBattleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

type PokeInfoNotificationType int32

const (
	PokeInfoNotificationType_PokeInfoNotificationType_None  PokeInfoNotificationType = 0
	PokeInfoNotificationType_PokeInfoNotificationType_Hatch PokeInfoNotificationType = 1
)

// Enum value maps for PokeInfoNotificationType.
var (
	PokeInfoNotificationType_name = map[int32]string{
		0: "PokeInfoNotificationType_None",
		1: "PokeInfoNotificationType_Hatch",
	}
	PokeInfoNotificationType_value = map[string]int32{
		"PokeInfoNotificationType_None":  0,
		"PokeInfoNotificationType_Hatch": 1,
	}
)

func (x PokeInfoNotificationType) Enum() *PokeInfoNotificationType {
	p := new(PokeInfoNotificationType)
	*p = x
	return p
}

func (x PokeInfoNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeInfoNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[2].Descriptor()
}

func (PokeInfoNotificationType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[2]
}

func (x PokeInfoNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeInfoNotificationType.Descriptor instead.
func (PokeInfoNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{2}
}

type AllUserServerNotificationType int32

const (
	AllUserServerNotificationType_AllUserServerNotificationType_None      AllUserServerNotificationType = 0
	AllUserServerNotificationType_AllUserServerNotificationType_ShinePoke AllUserServerNotificationType = 1
)

// Enum value maps for AllUserServerNotificationType.
var (
	AllUserServerNotificationType_name = map[int32]string{
		0: "AllUserServerNotificationType_None",
		1: "AllUserServerNotificationType_ShinePoke",
	}
	AllUserServerNotificationType_value = map[string]int32{
		"AllUserServerNotificationType_None":      0,
		"AllUserServerNotificationType_ShinePoke": 1,
	}
)

func (x AllUserServerNotificationType) Enum() *AllUserServerNotificationType {
	p := new(AllUserServerNotificationType)
	*p = x
	return p
}

func (x AllUserServerNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AllUserServerNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[3].Descriptor()
}

func (AllUserServerNotificationType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[3]
}

func (x AllUserServerNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AllUserServerNotificationType.Descriptor instead.
func (AllUserServerNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{3}
}

type InviteBattleNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proposer      *Trainer               `protobuf:"bytes,1,opt,name=proposer,proto3" json:"proposer,omitempty"`
	InviteType    InviteBattleType       `protobuf:"varint,2,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleNotification) Reset() {
	*x = InviteBattleNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleNotification) ProtoMessage() {}

func (x *InviteBattleNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleNotification.ProtoReflect.Descriptor instead.
func (*InviteBattleNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

func (x *InviteBattleNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *InviteBattleNotification) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

type PokeInfoNotification struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	EventType     PokeInfoNotificationType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=MainServer.PokeInfoNotificationType" json:"event_type,omitempty"`
	CellInfo      *PokeBoxCellInfo         `protobuf:"bytes,3,opt,name=cell_info,json=cellInfo,proto3" json:"cell_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeInfoNotification) Reset() {
	*x = PokeInfoNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeInfoNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeInfoNotification) ProtoMessage() {}

func (x *PokeInfoNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeInfoNotification.ProtoReflect.Descriptor instead.
func (*PokeInfoNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

func (x *PokeInfoNotification) GetEventType() PokeInfoNotificationType {
	if x != nil {
		return x.EventType
	}
	return PokeInfoNotificationType_PokeInfoNotificationType_None
}

func (x *PokeInfoNotification) GetCellInfo() *PokeBoxCellInfo {
	if x != nil {
		return x.CellInfo
	}
	return nil
}

type PokeBoxCellInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Loc           int32                  `protobuf:"varint,1,opt,name=loc,proto3" json:"loc,omitempty"`
	BoxType       PokeBoxType            `protobuf:"varint,2,opt,name=box_type,json=boxType,proto3,enum=MainServer.PokeBoxType" json:"box_type,omitempty"`
	BoxIndex      int32                  `protobuf:"varint,3,opt,name=box_index,json=boxIndex,proto3" json:"box_index,omitempty"`
	Info          *BoxPokeInfo           `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
	Poke          *Poke                  `protobuf:"bytes,5,opt,name=poke,proto3" json:"poke,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBoxCellInfo) Reset() {
	*x = PokeBoxCellInfo{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBoxCellInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxCellInfo) ProtoMessage() {}

func (x *PokeBoxCellInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxCellInfo.ProtoReflect.Descriptor instead.
func (*PokeBoxCellInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{2}
}

func (x *PokeBoxCellInfo) GetLoc() int32 {
	if x != nil {
		return x.Loc
	}
	return 0
}

func (x *PokeBoxCellInfo) GetBoxType() PokeBoxType {
	if x != nil {
		return x.BoxType
	}
	return PokeBoxType_normal
}

func (x *PokeBoxCellInfo) GetBoxIndex() int32 {
	if x != nil {
		return x.BoxIndex
	}
	return 0
}

func (x *PokeBoxCellInfo) GetInfo() *BoxPokeInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *PokeBoxCellInfo) GetPoke() *Poke {
	if x != nil {
		return x.Poke
	}
	return nil
}

type MatchJoinNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MatchId       string                 `protobuf:"bytes,1,opt,name=matchId,proto3" json:"matchId,omitempty"`
	Proposer      *Trainer               `protobuf:"bytes,2,opt,name=proposer,proto3" json:"proposer,omitempty"`
	Target        *Trainer               `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchJoinNotification) Reset() {
	*x = MatchJoinNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchJoinNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchJoinNotification) ProtoMessage() {}

func (x *MatchJoinNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchJoinNotification.ProtoReflect.Descriptor instead.
func (*MatchJoinNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{3}
}

func (x *MatchJoinNotification) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchJoinNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *MatchJoinNotification) GetTarget() *Trainer {
	if x != nil {
		return x.Target
	}
	return nil
}

type SwopInfoNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Swop          *SwopInfo              `protobuf:"bytes,1,opt,name=swop,proto3" json:"swop,omitempty"`
	Sender        *Trainer               `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwopInfoNotification) Reset() {
	*x = SwopInfoNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopInfoNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopInfoNotification) ProtoMessage() {}

func (x *SwopInfoNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopInfoNotification.ProtoReflect.Descriptor instead.
func (*SwopInfoNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{4}
}

func (x *SwopInfoNotification) GetSwop() *SwopInfo {
	if x != nil {
		return x.Swop
	}
	return nil
}

func (x *SwopInfoNotification) GetSender() *Trainer {
	if x != nil {
		return x.Sender
	}
	return nil
}

type UpdateTrainerNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trainer       *Trainer               `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTrainerNotification) Reset() {
	*x = UpdateTrainerNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTrainerNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTrainerNotification) ProtoMessage() {}

func (x *UpdateTrainerNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTrainerNotification.ProtoReflect.Descriptor instead.
func (*UpdateTrainerNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateTrainerNotification) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

type InvitePartyNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proposer      *Trainer               `protobuf:"bytes,1,opt,name=proposer,proto3" json:"proposer,omitempty"`
	IsWantJoin    bool                   `protobuf:"varint,2,opt,name=is_want_join,json=isWantJoin,proto3" json:"is_want_join,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvitePartyNotification) Reset() {
	*x = InvitePartyNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvitePartyNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitePartyNotification) ProtoMessage() {}

func (x *InvitePartyNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitePartyNotification.ProtoReflect.Descriptor instead.
func (*InvitePartyNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{6}
}

func (x *InvitePartyNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *InvitePartyNotification) GetIsWantJoin() bool {
	if x != nil {
		return x.IsWantJoin
	}
	return false
}

type RewardNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Reward        *RewardDetail          `protobuf:"bytes,1,opt,name=reward,proto3" json:"reward,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RewardNotification) Reset() {
	*x = RewardNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardNotification) ProtoMessage() {}

func (x *RewardNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardNotification.ProtoReflect.Descriptor instead.
func (*RewardNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{7}
}

func (x *RewardNotification) GetReward() *RewardDetail {
	if x != nil {
		return x.Reward
	}
	return nil
}

type AllUserServerNotification struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Type          AllUserServerNotificationType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.AllUserServerNotificationType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AllUserServerNotification) Reset() {
	*x = AllUserServerNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AllUserServerNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllUserServerNotification) ProtoMessage() {}

func (x *AllUserServerNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllUserServerNotification.ProtoReflect.Descriptor instead.
func (*AllUserServerNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{8}
}

func (x *AllUserServerNotification) GetType() AllUserServerNotificationType {
	if x != nil {
		return x.Type
	}
	return AllUserServerNotificationType_AllUserServerNotificationType_None
}

var File_MainServer_ServerNotification_proto protoreflect.FileDescriptor

const file_MainServer_ServerNotification_proto_rawDesc = "" +
	"\n" +
	"#MainServer/ServerNotification.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Trainer.proto\x1a\x15MainServer/Swop.proto\x1a\x1bMainServer/RewardInfo.proto\x1a\x15MainServer/Poke.proto\x1a\x18MainServer/PokeBox.proto\"\x89\x01\n" +
	"\x18InviteBattleNotification\x12/\n" +
	"\bproposer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\bproposer\x12<\n" +
	"\n" +
	"inviteType\x18\x02 \x01(\x0e2\x1c.MainServer.InviteBattleTypeR\n" +
	"inviteType\"\x95\x01\n" +
	"\x14PokeInfoNotification\x12C\n" +
	"\n" +
	"event_type\x18\x02 \x01(\x0e2$.MainServer.PokeInfoNotificationTypeR\teventType\x128\n" +
	"\tcell_info\x18\x03 \x01(\v2\x1b.MainServer.PokeBoxCellInfoR\bcellInfo\"\xc7\x01\n" +
	"\x0fPokeBoxCellInfo\x12\x10\n" +
	"\x03loc\x18\x01 \x01(\x05R\x03loc\x122\n" +
	"\bbox_type\x18\x02 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\aboxType\x12\x1b\n" +
	"\tbox_index\x18\x03 \x01(\x05R\bboxIndex\x12+\n" +
	"\x04info\x18\x04 \x01(\v2\x17.MainServer.BoxPokeInfoR\x04info\x12$\n" +
	"\x04poke\x18\x05 \x01(\v2\x10.MainServer.PokeR\x04poke\"\x8f\x01\n" +
	"\x15MatchJoinNotification\x12\x18\n" +
	"\amatchId\x18\x01 \x01(\tR\amatchId\x12/\n" +
	"\bproposer\x18\x02 \x01(\v2\x13.MainServer.TrainerR\bproposer\x12+\n" +
	"\x06target\x18\x03 \x01(\v2\x13.MainServer.TrainerR\x06target\"m\n" +
	"\x14SwopInfoNotification\x12(\n" +
	"\x04swop\x18\x01 \x01(\v2\x14.MainServer.SwopInfoR\x04swop\x12+\n" +
	"\x06sender\x18\x02 \x01(\v2\x13.MainServer.TrainerR\x06sender\"J\n" +
	"\x19UpdateTrainerNotification\x12-\n" +
	"\atrainer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\atrainer\"l\n" +
	"\x17InvitePartyNotification\x12/\n" +
	"\bproposer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\bproposer\x12 \n" +
	"\fis_want_join\x18\x02 \x01(\bR\n" +
	"isWantJoin\"F\n" +
	"\x12RewardNotification\x120\n" +
	"\x06reward\x18\x01 \x01(\v2\x18.MainServer.RewardDetailR\x06reward\"Z\n" +
	"\x19AllUserServerNotification\x12=\n" +
	"\x04type\x18\x01 \x01(\x0e2).MainServer.AllUserServerNotificationTypeR\x04type*\xb7\x05\n" +
	"\x16ServerNotificationType\x12\x1f\n" +
	"\x1bServerNotificationType_None\x10\x00\x12%\n" +
	"!ServerNotificationType_NewVersion\x10\x01\x12'\n" +
	"#ServerNotificationType_InviteBattle\x10\x02\x12$\n" +
	" ServerNotificationType_MatchJoin\x10\x03\x12#\n" +
	"\x1fServerNotificationType_SwopInfo\x10\x04\x12(\n" +
	"$ServerNotificationType_UpdateTrainer\x10\x05\x12&\n" +
	"\"ServerNotificationType_InviteParty\x10\x06\x12-\n" +
	")ServerNotificationType_SyncPartyMatchInfo\x10\a\x12!\n" +
	"\x1dServerNotificationType_Reward\x10\b\x12#\n" +
	"\x1fServerNotificationType_PokeInfo\x10\t\x12(\n" +
	"$ServerNotificationType_BattleMessage\x10d\x12(\n" +
	"$ServerNotificationType_BattlePrepare\x10e\x12%\n" +
	"!ServerNotificationType_BattleInit\x10f\x12'\n" +
	"#ServerNotificationType_BattleChoice\x10g\x12'\n" +
	"#ServerNotificationType_BattleResult\x10h\x12%\n" +
	"!ServerNotificationType_BattleUrge\x10i\x12$\n" +
	"\x1fServerNotificationType_NewEmail\x10\xc8\x01*f\n" +
	"\x10InviteBattleType\x12\x19\n" +
	"\x15InviteBattleType_None\x10\x00\x12\x1b\n" +
	"\x17InviteBattleType_Normal\x10\x01\x12\x1a\n" +
	"\x16InviteBattleType_Force\x10\x02*a\n" +
	"\x18PokeInfoNotificationType\x12!\n" +
	"\x1dPokeInfoNotificationType_None\x10\x00\x12\"\n" +
	"\x1ePokeInfoNotificationType_Hatch\x10\x01*t\n" +
	"\x1dAllUserServerNotificationType\x12&\n" +
	"\"AllUserServerNotificationType_None\x10\x00\x12+\n" +
	"'AllUserServerNotificationType_ShinePoke\x10\x01B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ServerNotification_proto_rawDescOnce sync.Once
	file_MainServer_ServerNotification_proto_rawDescData []byte
)

func file_MainServer_ServerNotification_proto_rawDescGZIP() []byte {
	file_MainServer_ServerNotification_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerNotification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ServerNotification_proto_rawDesc), len(file_MainServer_ServerNotification_proto_rawDesc)))
	})
	return file_MainServer_ServerNotification_proto_rawDescData
}

var file_MainServer_ServerNotification_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_MainServer_ServerNotification_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_MainServer_ServerNotification_proto_goTypes = []any{
	(ServerNotificationType)(0),        // 0: MainServer.ServerNotificationType
	(InviteBattleType)(0),              // 1: MainServer.InviteBattleType
	(PokeInfoNotificationType)(0),      // 2: MainServer.PokeInfoNotificationType
	(AllUserServerNotificationType)(0), // 3: MainServer.AllUserServerNotificationType
	(*InviteBattleNotification)(nil),   // 4: MainServer.InviteBattleNotification
	(*PokeInfoNotification)(nil),       // 5: MainServer.PokeInfoNotification
	(*PokeBoxCellInfo)(nil),            // 6: MainServer.PokeBoxCellInfo
	(*MatchJoinNotification)(nil),      // 7: MainServer.MatchJoinNotification
	(*SwopInfoNotification)(nil),       // 8: MainServer.SwopInfoNotification
	(*UpdateTrainerNotification)(nil),  // 9: MainServer.UpdateTrainerNotification
	(*InvitePartyNotification)(nil),    // 10: MainServer.InvitePartyNotification
	(*RewardNotification)(nil),         // 11: MainServer.RewardNotification
	(*AllUserServerNotification)(nil),  // 12: MainServer.AllUserServerNotification
	(*Trainer)(nil),                    // 13: MainServer.Trainer
	(PokeBoxType)(0),                   // 14: MainServer.PokeBoxType
	(*BoxPokeInfo)(nil),                // 15: MainServer.BoxPokeInfo
	(*Poke)(nil),                       // 16: MainServer.Poke
	(*SwopInfo)(nil),                   // 17: MainServer.SwopInfo
	(*RewardDetail)(nil),               // 18: MainServer.RewardDetail
}
var file_MainServer_ServerNotification_proto_depIdxs = []int32{
	13, // 0: MainServer.InviteBattleNotification.proposer:type_name -> MainServer.Trainer
	1,  // 1: MainServer.InviteBattleNotification.inviteType:type_name -> MainServer.InviteBattleType
	2,  // 2: MainServer.PokeInfoNotification.event_type:type_name -> MainServer.PokeInfoNotificationType
	6,  // 3: MainServer.PokeInfoNotification.cell_info:type_name -> MainServer.PokeBoxCellInfo
	14, // 4: MainServer.PokeBoxCellInfo.box_type:type_name -> MainServer.PokeBoxType
	15, // 5: MainServer.PokeBoxCellInfo.info:type_name -> MainServer.BoxPokeInfo
	16, // 6: MainServer.PokeBoxCellInfo.poke:type_name -> MainServer.Poke
	13, // 7: MainServer.MatchJoinNotification.proposer:type_name -> MainServer.Trainer
	13, // 8: MainServer.MatchJoinNotification.target:type_name -> MainServer.Trainer
	17, // 9: MainServer.SwopInfoNotification.swop:type_name -> MainServer.SwopInfo
	13, // 10: MainServer.SwopInfoNotification.sender:type_name -> MainServer.Trainer
	13, // 11: MainServer.UpdateTrainerNotification.trainer:type_name -> MainServer.Trainer
	13, // 12: MainServer.InvitePartyNotification.proposer:type_name -> MainServer.Trainer
	18, // 13: MainServer.RewardNotification.reward:type_name -> MainServer.RewardDetail
	3,  // 14: MainServer.AllUserServerNotification.type:type_name -> MainServer.AllUserServerNotificationType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_MainServer_ServerNotification_proto_init() }
func file_MainServer_ServerNotification_proto_init() {
	if File_MainServer_ServerNotification_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_Swop_proto_init()
	file_MainServer_RewardInfo_proto_init()
	file_MainServer_Poke_proto_init()
	file_MainServer_PokeBox_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ServerNotification_proto_rawDesc), len(file_MainServer_ServerNotification_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerNotification_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerNotification_proto_depIdxs,
		EnumInfos:         file_MainServer_ServerNotification_proto_enumTypes,
		MessageInfos:      file_MainServer_ServerNotification_proto_msgTypes,
	}.Build()
	File_MainServer_ServerNotification_proto = out.File
	file_MainServer_ServerNotification_proto_goTypes = nil
	file_MainServer_ServerNotification_proto_depIdxs = nil
}
