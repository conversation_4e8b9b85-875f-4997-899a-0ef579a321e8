// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerQuest.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ThroughPointInfoType int32

const (
	ThroughPointInfoType_ThroughPointInfoType_none  ThroughPointInfoType = 0
	ThroughPointInfoType_ThroughPointInfoType_start ThroughPointInfoType = 1
	ThroughPointInfoType_ThroughPointInfoType_end   ThroughPointInfoType = 2
)

// Enum value maps for ThroughPointInfoType.
var (
	ThroughPointInfoType_name = map[int32]string{
		0: "ThroughPointInfoType_none",
		1: "ThroughPointInfoType_start",
		2: "ThroughPointInfoType_end",
	}
	ThroughPointInfoType_value = map[string]int32{
		"ThroughPointInfoType_none":  0,
		"ThroughPointInfoType_start": 1,
		"ThroughPointInfoType_end":   2,
	}
)

func (x ThroughPointInfoType) Enum() *ThroughPointInfoType {
	p := new(ThroughPointInfoType)
	*p = x
	return p
}

func (x ThroughPointInfoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThroughPointInfoType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerQuest_proto_enumTypes[0].Descriptor()
}

func (ThroughPointInfoType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerQuest_proto_enumTypes[0]
}

func (x ThroughPointInfoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThroughPointInfoType.Descriptor instead.
func (ThroughPointInfoType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{0}
}

type TrainerQuestStatus int32

const (
	TrainerQuestStatus_TrainerQuestStatus_none        TrainerQuestStatus = 0 // 未接受
	TrainerQuestStatus_TrainerQuestStatus_accept      TrainerQuestStatus = 1 // 已接受
	TrainerQuestStatus_TrainerQuestStatus_finish_half TrainerQuestStatus = 2 // 不完美完成
	TrainerQuestStatus_TrainerQuestStatus_finish      TrainerQuestStatus = 3 // 已完成
	TrainerQuestStatus_TrainerQuestStatus_reward      TrainerQuestStatus = 4 // 已领取奖励
	TrainerQuestStatus_TrainerQuestStatus_cancel      TrainerQuestStatus = 5 // 已取消
	TrainerQuestStatus_TrainerQuestStatus_timeout     TrainerQuestStatus = 6 // 已超时
	TrainerQuestStatus_TrainerQuestStatus_close       TrainerQuestStatus = 7 // 已关闭
	TrainerQuestStatus_TrainerQuestStatus_failed      TrainerQuestStatus = 8 // 已失败
)

// Enum value maps for TrainerQuestStatus.
var (
	TrainerQuestStatus_name = map[int32]string{
		0: "TrainerQuestStatus_none",
		1: "TrainerQuestStatus_accept",
		2: "TrainerQuestStatus_finish_half",
		3: "TrainerQuestStatus_finish",
		4: "TrainerQuestStatus_reward",
		5: "TrainerQuestStatus_cancel",
		6: "TrainerQuestStatus_timeout",
		7: "TrainerQuestStatus_close",
		8: "TrainerQuestStatus_failed",
	}
	TrainerQuestStatus_value = map[string]int32{
		"TrainerQuestStatus_none":        0,
		"TrainerQuestStatus_accept":      1,
		"TrainerQuestStatus_finish_half": 2,
		"TrainerQuestStatus_finish":      3,
		"TrainerQuestStatus_reward":      4,
		"TrainerQuestStatus_cancel":      5,
		"TrainerQuestStatus_timeout":     6,
		"TrainerQuestStatus_close":       7,
		"TrainerQuestStatus_failed":      8,
	}
)

func (x TrainerQuestStatus) Enum() *TrainerQuestStatus {
	p := new(TrainerQuestStatus)
	*p = x
	return p
}

func (x TrainerQuestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerQuestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerQuest_proto_enumTypes[1].Descriptor()
}

func (TrainerQuestStatus) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerQuest_proto_enumTypes[1]
}

func (x TrainerQuestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerQuestStatus.Descriptor instead.
func (TrainerQuestStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{1}
}

type QuestOperationType int32

const (
	QuestOperationType_QuestOperationType_none         QuestOperationType = 0
	QuestOperationType_QuestOperationType_accept       QuestOperationType = 1
	QuestOperationType_QuestOperationType_complete     QuestOperationType = 2
	QuestOperationType_QuestOperationType_claim_reward QuestOperationType = 3
	QuestOperationType_QuestOperationType_cancel       QuestOperationType = 4
	QuestOperationType_QuestOperationType_start        QuestOperationType = 5
)

// Enum value maps for QuestOperationType.
var (
	QuestOperationType_name = map[int32]string{
		0: "QuestOperationType_none",
		1: "QuestOperationType_accept",
		2: "QuestOperationType_complete",
		3: "QuestOperationType_claim_reward",
		4: "QuestOperationType_cancel",
		5: "QuestOperationType_start",
	}
	QuestOperationType_value = map[string]int32{
		"QuestOperationType_none":         0,
		"QuestOperationType_accept":       1,
		"QuestOperationType_complete":     2,
		"QuestOperationType_claim_reward": 3,
		"QuestOperationType_cancel":       4,
		"QuestOperationType_start":        5,
	}
)

func (x QuestOperationType) Enum() *QuestOperationType {
	p := new(QuestOperationType)
	*p = x
	return p
}

func (x QuestOperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestOperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerQuest_proto_enumTypes[2].Descriptor()
}

func (QuestOperationType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerQuest_proto_enumTypes[2]
}

func (x QuestOperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestOperationType.Descriptor instead.
func (QuestOperationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{2}
}

type TrainerQuest struct {
	state            protoimpl.MessageState   `protogen:"open.v1"`
	Id               int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid              int64                    `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	QuestId          string                   `protobuf:"bytes,3,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	QuestType        QuestType                `protobuf:"varint,4,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"` //这个在领取的时候就确定了
	QuestStatus      TrainerQuestStatus       `protobuf:"varint,5,opt,name=quest_status,json=questStatus,proto3,enum=MainServer.TrainerQuestStatus" json:"quest_status,omitempty"`
	QuestCurrentInfo *TrainerQuestCurrentInfo `protobuf:"bytes,6,opt,name=quest_current_info,json=questCurrentInfo,proto3" json:"quest_current_info,omitempty"`
	// map<string, int32> quest_progress = 6; // 进度 表示当前完成了多少，比如击败了多少个poke啥的
	QuestStartTime       int64                 `protobuf:"varint,7,opt,name=quest_start_time,json=questStartTime,proto3" json:"quest_start_time,omitempty"`                     // 开始时间
	QuestEndTime         int64                 `protobuf:"varint,8,opt,name=quest_end_time,json=questEndTime,proto3" json:"quest_end_time,omitempty"`                           // 结束时间 （可能是完成，可能是超时等）
	QuestRepeatLimitTime int32                 `protobuf:"varint,9,opt,name=quest_repeat_limit_time,json=questRepeatLimitTime,proto3" json:"quest_repeat_limit_time,omitempty"` // 时间限制
	QuestInfo            *QuestInfo            `protobuf:"bytes,10,opt,name=quest_info,json=questInfo,proto3" json:"quest_info,omitempty"`                                      // 任务信息 可能要隐藏部分信息
	YarnInfo             *TrainerQuestYarnInfo `protobuf:"bytes,11,opt,name=yarn_info,json=yarnInfo,proto3" json:"yarn_info,omitempty"`                                         // Yarn节点的信息
	UpdateTs             int64                 `protobuf:"varint,12,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                        // 更新时间
	CreateTs             int64                 `protobuf:"varint,13,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                        // 创建时间
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TrainerQuest) Reset() {
	*x = TrainerQuest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerQuest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerQuest) ProtoMessage() {}

func (x *TrainerQuest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerQuest.ProtoReflect.Descriptor instead.
func (*TrainerQuest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerQuest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerQuest) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TrainerQuest) GetQuestId() string {
	if x != nil {
		return x.QuestId
	}
	return ""
}

func (x *TrainerQuest) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

func (x *TrainerQuest) GetQuestStatus() TrainerQuestStatus {
	if x != nil {
		return x.QuestStatus
	}
	return TrainerQuestStatus_TrainerQuestStatus_none
}

func (x *TrainerQuest) GetQuestCurrentInfo() *TrainerQuestCurrentInfo {
	if x != nil {
		return x.QuestCurrentInfo
	}
	return nil
}

func (x *TrainerQuest) GetQuestStartTime() int64 {
	if x != nil {
		return x.QuestStartTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestEndTime() int64 {
	if x != nil {
		return x.QuestEndTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestRepeatLimitTime() int32 {
	if x != nil {
		return x.QuestRepeatLimitTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestInfo() *QuestInfo {
	if x != nil {
		return x.QuestInfo
	}
	return nil
}

func (x *TrainerQuest) GetYarnInfo() *TrainerQuestYarnInfo {
	if x != nil {
		return x.YarnInfo
	}
	return nil
}

func (x *TrainerQuest) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *TrainerQuest) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

type TrainerQuestCurrentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// QuestInfo current_quest = 1; //(当前任务的线性任务的id) 因为有可能会随机任务所以这个地方记录下确定的任务才能知道也好完成什么，完成的条件是什么
	QuestIdList           []int64            `protobuf:"varint,1,rep,packed,name=quest_id_list,json=questIdList,proto3" json:"quest_id_list,omitempty"`                                                                                                  // 任务id列表（数据库id）
	ParentQuestId         int64              `protobuf:"varint,2,opt,name=parent_quest_id,json=parentQuestId,proto3" json:"parent_quest_id,omitempty"`                                                                                                   // 父任务id （数据库id）
	QuestDefaultCondition map[string]int32   `protobuf:"bytes,3,rep,name=quest_default_condition,json=questDefaultCondition,proto3" json:"quest_default_condition,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"` //需要完成的条件 //接受的时候记录
	QuestProgress         map[string]int32   `protobuf:"bytes,4,rep,name=quest_progress,json=questProgress,proto3" json:"quest_progress,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`                           // 进度 表示当前完成了多少，比如击败了多少个poke啥的 上面的好用于对比
	CompleteQuestMap      map[int64]bool     `protobuf:"bytes,5,rep,name=complete_quest_map,json=completeQuestMap,proto3" json:"complete_quest_map,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`               // 已经完成的任务id列表
	QuestCompleteInfo     *QuestCompleteInfo `protobuf:"bytes,6,opt,name=quest_complete_info,json=questCompleteInfo,proto3" json:"quest_complete_info,omitempty"`                                                                                        // 完成信息 （有些信息是随机产生的，所以必须在接受任务的时候，就确定完成条件）
	Count                 int32              `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`                                                                                                                                          // 计数器
	ContinueCount         int32              `protobuf:"varint,8,opt,name=continue_count,json=continueCount,proto3" json:"continue_count,omitempty"`                                                                                                     // 连续计数器
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *TrainerQuestCurrentInfo) Reset() {
	*x = TrainerQuestCurrentInfo{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerQuestCurrentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerQuestCurrentInfo) ProtoMessage() {}

func (x *TrainerQuestCurrentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerQuestCurrentInfo.ProtoReflect.Descriptor instead.
func (*TrainerQuestCurrentInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerQuestCurrentInfo) GetQuestIdList() []int64 {
	if x != nil {
		return x.QuestIdList
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetParentQuestId() int64 {
	if x != nil {
		return x.ParentQuestId
	}
	return 0
}

func (x *TrainerQuestCurrentInfo) GetQuestDefaultCondition() map[string]int32 {
	if x != nil {
		return x.QuestDefaultCondition
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetQuestProgress() map[string]int32 {
	if x != nil {
		return x.QuestProgress
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetCompleteQuestMap() map[int64]bool {
	if x != nil {
		return x.CompleteQuestMap
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetQuestCompleteInfo() *QuestCompleteInfo {
	if x != nil {
		return x.QuestCompleteInfo
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TrainerQuestCurrentInfo) GetContinueCount() int32 {
	if x != nil {
		return x.ContinueCount
	}
	return 0
}

type TrainerQuestYarnInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// map<string, string> open_transfer_point = 1; // Yarn开启的传送点
	ThroughPoints map[string]*ThroughPointInfo `protobuf:"bytes,1,rep,name=through_points,json=throughPoints,proto3" json:"through_points,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerQuestYarnInfo) Reset() {
	*x = TrainerQuestYarnInfo{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerQuestYarnInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerQuestYarnInfo) ProtoMessage() {}

func (x *TrainerQuestYarnInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerQuestYarnInfo.ProtoReflect.Descriptor instead.
func (*TrainerQuestYarnInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{2}
}

func (x *TrainerQuestYarnInfo) GetThroughPoints() map[string]*ThroughPointInfo {
	if x != nil {
		return x.ThroughPoints
	}
	return nil
}

type ThroughPointInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PointTitle    string                 `protobuf:"bytes,1,opt,name=point_title,json=pointTitle,proto3" json:"point_title,omitempty"`
	InfoType      ThroughPointInfoType   `protobuf:"varint,2,opt,name=info_type,json=infoType,proto3,enum=MainServer.ThroughPointInfoType" json:"info_type,omitempty"`
	InfoValue     string                 `protobuf:"bytes,3,opt,name=info_value,json=infoValue,proto3" json:"info_value,omitempty"`
	Index         int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThroughPointInfo) Reset() {
	*x = ThroughPointInfo{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThroughPointInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThroughPointInfo) ProtoMessage() {}

func (x *ThroughPointInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThroughPointInfo.ProtoReflect.Descriptor instead.
func (*ThroughPointInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{3}
}

func (x *ThroughPointInfo) GetPointTitle() string {
	if x != nil {
		return x.PointTitle
	}
	return ""
}

func (x *ThroughPointInfo) GetInfoType() ThroughPointInfoType {
	if x != nil {
		return x.InfoType
	}
	return ThroughPointInfoType_ThroughPointInfoType_none
}

func (x *ThroughPointInfo) GetInfoValue() string {
	if x != nil {
		return x.InfoValue
	}
	return ""
}

func (x *ThroughPointInfo) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

// Quest RPC 请求和响应消息
type QuestOperationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OperationType QuestOperationType     `protobuf:"varint,1,opt,name=operation_type,json=operationType,proto3,enum=MainServer.QuestOperationType" json:"operation_type,omitempty"`
	QuestId       int64                  `protobuf:"varint,2,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	ParentQuestId int64                  `protobuf:"varint,3,opt,name=parent_quest_id,json=parentQuestId,proto3" json:"parent_quest_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuestOperationRequest) Reset() {
	*x = QuestOperationRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestOperationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestOperationRequest) ProtoMessage() {}

func (x *QuestOperationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestOperationRequest.ProtoReflect.Descriptor instead.
func (*QuestOperationRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{4}
}

func (x *QuestOperationRequest) GetOperationType() QuestOperationType {
	if x != nil {
		return x.OperationType
	}
	return QuestOperationType_QuestOperationType_none
}

func (x *QuestOperationRequest) GetQuestId() int64 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *QuestOperationRequest) GetParentQuestId() int64 {
	if x != nil {
		return x.ParentQuestId
	}
	return 0
}

type QuestOperationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TrainerQuest  *TrainerQuest          `protobuf:"bytes,3,opt,name=trainer_quest,json=trainerQuest,proto3" json:"trainer_quest,omitempty"`
	Value         string                 `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuestOperationResponse) Reset() {
	*x = QuestOperationResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestOperationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestOperationResponse) ProtoMessage() {}

func (x *QuestOperationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestOperationResponse.ProtoReflect.Descriptor instead.
func (*QuestOperationResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{5}
}

func (x *QuestOperationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QuestOperationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QuestOperationResponse) GetTrainerQuest() *TrainerQuest {
	if x != nil {
		return x.TrainerQuest
	}
	return nil
}

func (x *QuestOperationResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 接受任务
type RpcAcceptQuestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QuestId       string                 `protobuf:"bytes,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAcceptQuestRequest) Reset() {
	*x = RpcAcceptQuestRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAcceptQuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAcceptQuestRequest) ProtoMessage() {}

func (x *RpcAcceptQuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAcceptQuestRequest.ProtoReflect.Descriptor instead.
func (*RpcAcceptQuestRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{6}
}

func (x *RpcAcceptQuestRequest) GetQuestId() string {
	if x != nil {
		return x.QuestId
	}
	return ""
}

type RpcAcceptQuestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TrainerQuest  *TrainerQuest          `protobuf:"bytes,3,opt,name=trainer_quest,json=trainerQuest,proto3" json:"trainer_quest,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAcceptQuestResponse) Reset() {
	*x = RpcAcceptQuestResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAcceptQuestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAcceptQuestResponse) ProtoMessage() {}

func (x *RpcAcceptQuestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAcceptQuestResponse.ProtoReflect.Descriptor instead.
func (*RpcAcceptQuestResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{7}
}

func (x *RpcAcceptQuestResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcAcceptQuestResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcAcceptQuestResponse) GetTrainerQuest() *TrainerQuest {
	if x != nil {
		return x.TrainerQuest
	}
	return nil
}

// 完成任务
type RpcCompleteQuestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DbId          int64                  `protobuf:"varint,1,opt,name=db_id,json=dbId,proto3" json:"db_id,omitempty"`
	ParentDbId    int64                  `protobuf:"varint,2,opt,name=parent_db_id,json=parentDbId,proto3" json:"parent_db_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcCompleteQuestRequest) Reset() {
	*x = RpcCompleteQuestRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcCompleteQuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcCompleteQuestRequest) ProtoMessage() {}

func (x *RpcCompleteQuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcCompleteQuestRequest.ProtoReflect.Descriptor instead.
func (*RpcCompleteQuestRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{8}
}

func (x *RpcCompleteQuestRequest) GetDbId() int64 {
	if x != nil {
		return x.DbId
	}
	return 0
}

func (x *RpcCompleteQuestRequest) GetParentDbId() int64 {
	if x != nil {
		return x.ParentDbId
	}
	return 0
}

type RpcCompleteQuestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcCompleteQuestResponse) Reset() {
	*x = RpcCompleteQuestResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcCompleteQuestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcCompleteQuestResponse) ProtoMessage() {}

func (x *RpcCompleteQuestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcCompleteQuestResponse.ProtoReflect.Descriptor instead.
func (*RpcCompleteQuestResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{9}
}

func (x *RpcCompleteQuestResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcCompleteQuestResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 领取任务奖励
type RpcClaimQuestRewardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QuestId       int64                  `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcClaimQuestRewardRequest) Reset() {
	*x = RpcClaimQuestRewardRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcClaimQuestRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcClaimQuestRewardRequest) ProtoMessage() {}

func (x *RpcClaimQuestRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcClaimQuestRewardRequest.ProtoReflect.Descriptor instead.
func (*RpcClaimQuestRewardRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{10}
}

func (x *RpcClaimQuestRewardRequest) GetQuestId() int64 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

type RpcClaimQuestRewardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcClaimQuestRewardResponse) Reset() {
	*x = RpcClaimQuestRewardResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcClaimQuestRewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcClaimQuestRewardResponse) ProtoMessage() {}

func (x *RpcClaimQuestRewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcClaimQuestRewardResponse.ProtoReflect.Descriptor instead.
func (*RpcClaimQuestRewardResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{11}
}

func (x *RpcClaimQuestRewardResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcClaimQuestRewardResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取可用任务列表
type RpcGetAvailableQuestsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QuestType     QuestType              `protobuf:"varint,1,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"` // 可选，筛选特定类型的任务
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetAvailableQuestsRequest) Reset() {
	*x = RpcGetAvailableQuestsRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetAvailableQuestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAvailableQuestsRequest) ProtoMessage() {}

func (x *RpcGetAvailableQuestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAvailableQuestsRequest.ProtoReflect.Descriptor instead.
func (*RpcGetAvailableQuestsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{12}
}

func (x *RpcGetAvailableQuestsRequest) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

type RpcGetAvailableQuestsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Quests        []*QuestInfo           `protobuf:"bytes,3,rep,name=quests,proto3" json:"quests,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetAvailableQuestsResponse) Reset() {
	*x = RpcGetAvailableQuestsResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetAvailableQuestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAvailableQuestsResponse) ProtoMessage() {}

func (x *RpcGetAvailableQuestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAvailableQuestsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetAvailableQuestsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{13}
}

func (x *RpcGetAvailableQuestsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcGetAvailableQuestsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcGetAvailableQuestsResponse) GetQuests() []*QuestInfo {
	if x != nil {
		return x.Quests
	}
	return nil
}

// 获取训练师任务列表
type RpcGetTrainerQuestsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TrainerQuests []*TrainerQuest        `protobuf:"bytes,3,rep,name=trainer_quests,json=trainerQuests,proto3" json:"trainer_quests,omitempty"`
	CurrentTs     int64                  `protobuf:"varint,4,opt,name=current_ts,json=currentTs,proto3" json:"current_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTrainerQuestsResponse) Reset() {
	*x = RpcGetTrainerQuestsResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTrainerQuestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerQuestsResponse) ProtoMessage() {}

func (x *RpcGetTrainerQuestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerQuestsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerQuestsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{14}
}

func (x *RpcGetTrainerQuestsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcGetTrainerQuestsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcGetTrainerQuestsResponse) GetTrainerQuests() []*TrainerQuest {
	if x != nil {
		return x.TrainerQuests
	}
	return nil
}

func (x *RpcGetTrainerQuestsResponse) GetCurrentTs() int64 {
	if x != nil {
		return x.CurrentTs
	}
	return 0
}

// 更新任务进度
type UpdateQuestThroughPointInfoRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	QuestId          int64                  `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	ThroughPointInfo *ThroughPointInfo      `protobuf:"bytes,2,opt,name=throughPointInfo,proto3" json:"throughPointInfo,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateQuestThroughPointInfoRequest) Reset() {
	*x = UpdateQuestThroughPointInfoRequest{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateQuestThroughPointInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuestThroughPointInfoRequest) ProtoMessage() {}

func (x *UpdateQuestThroughPointInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuestThroughPointInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateQuestThroughPointInfoRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateQuestThroughPointInfoRequest) GetQuestId() int64 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *UpdateQuestThroughPointInfoRequest) GetThroughPointInfo() *ThroughPointInfo {
	if x != nil {
		return x.ThroughPointInfo
	}
	return nil
}

type UpdateYarnThroughPointInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateYarnThroughPointInfoResponse) Reset() {
	*x = UpdateYarnThroughPointInfoResponse{}
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateYarnThroughPointInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateYarnThroughPointInfoResponse) ProtoMessage() {}

func (x *UpdateYarnThroughPointInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateYarnThroughPointInfoResponse.ProtoReflect.Descriptor instead.
func (*UpdateYarnThroughPointInfoResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateYarnThroughPointInfoResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateYarnThroughPointInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_TrainerQuest_proto protoreflect.FileDescriptor

const file_MainServer_TrainerQuest_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/TrainerQuest.proto\x12\n" +
	"MainServer\x1a\x1aMainServer/QuestType.proto\x1a\x1aMainServer/QuestInfo.proto\"\xcd\x04\n" +
	"\fTrainerQuest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x19\n" +
	"\bquest_id\x18\x03 \x01(\tR\aquestId\x124\n" +
	"\n" +
	"quest_type\x18\x04 \x01(\x0e2\x15.MainServer.QuestTypeR\tquestType\x12A\n" +
	"\fquest_status\x18\x05 \x01(\x0e2\x1e.MainServer.TrainerQuestStatusR\vquestStatus\x12Q\n" +
	"\x12quest_current_info\x18\x06 \x01(\v2#.MainServer.TrainerQuestCurrentInfoR\x10questCurrentInfo\x12(\n" +
	"\x10quest_start_time\x18\a \x01(\x03R\x0equestStartTime\x12$\n" +
	"\x0equest_end_time\x18\b \x01(\x03R\fquestEndTime\x125\n" +
	"\x17quest_repeat_limit_time\x18\t \x01(\x05R\x14questRepeatLimitTime\x124\n" +
	"\n" +
	"quest_info\x18\n" +
	" \x01(\v2\x15.MainServer.QuestInfoR\tquestInfo\x12=\n" +
	"\tyarn_info\x18\v \x01(\v2 .MainServer.TrainerQuestYarnInfoR\byarnInfo\x12\x1b\n" +
	"\tupdate_ts\x18\f \x01(\x03R\bupdateTs\x12\x1b\n" +
	"\tcreate_ts\x18\r \x01(\x03R\bcreateTs\"\x82\x06\n" +
	"\x17TrainerQuestCurrentInfo\x12\"\n" +
	"\rquest_id_list\x18\x01 \x03(\x03R\vquestIdList\x12&\n" +
	"\x0fparent_quest_id\x18\x02 \x01(\x03R\rparentQuestId\x12v\n" +
	"\x17quest_default_condition\x18\x03 \x03(\v2>.MainServer.TrainerQuestCurrentInfo.QuestDefaultConditionEntryR\x15questDefaultCondition\x12]\n" +
	"\x0equest_progress\x18\x04 \x03(\v26.MainServer.TrainerQuestCurrentInfo.QuestProgressEntryR\rquestProgress\x12g\n" +
	"\x12complete_quest_map\x18\x05 \x03(\v29.MainServer.TrainerQuestCurrentInfo.CompleteQuestMapEntryR\x10completeQuestMap\x12M\n" +
	"\x13quest_complete_info\x18\x06 \x01(\v2\x1d.MainServer.QuestCompleteInfoR\x11questCompleteInfo\x12\x14\n" +
	"\x05count\x18\a \x01(\x05R\x05count\x12%\n" +
	"\x0econtinue_count\x18\b \x01(\x05R\rcontinueCount\x1aH\n" +
	"\x1aQuestDefaultConditionEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a@\n" +
	"\x12QuestProgressEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1aC\n" +
	"\x15CompleteQuestMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\bR\x05value:\x028\x01\"\xd2\x01\n" +
	"\x14TrainerQuestYarnInfo\x12Z\n" +
	"\x0ethrough_points\x18\x01 \x03(\v23.MainServer.TrainerQuestYarnInfo.ThroughPointsEntryR\rthroughPoints\x1a^\n" +
	"\x12ThroughPointsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x122\n" +
	"\x05value\x18\x02 \x01(\v2\x1c.MainServer.ThroughPointInfoR\x05value:\x028\x01\"\xa7\x01\n" +
	"\x10ThroughPointInfo\x12\x1f\n" +
	"\vpoint_title\x18\x01 \x01(\tR\n" +
	"pointTitle\x12=\n" +
	"\tinfo_type\x18\x02 \x01(\x0e2 .MainServer.ThroughPointInfoTypeR\binfoType\x12\x1d\n" +
	"\n" +
	"info_value\x18\x03 \x01(\tR\tinfoValue\x12\x14\n" +
	"\x05index\x18\x04 \x01(\x05R\x05index\"\xa1\x01\n" +
	"\x15QuestOperationRequest\x12E\n" +
	"\x0eoperation_type\x18\x01 \x01(\x0e2\x1e.MainServer.QuestOperationTypeR\roperationType\x12\x19\n" +
	"\bquest_id\x18\x02 \x01(\x03R\aquestId\x12&\n" +
	"\x0fparent_quest_id\x18\x03 \x01(\x03R\rparentQuestId\"\xa1\x01\n" +
	"\x16QuestOperationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12=\n" +
	"\rtrainer_quest\x18\x03 \x01(\v2\x18.MainServer.TrainerQuestR\ftrainerQuest\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\"2\n" +
	"\x15RpcAcceptQuestRequest\x12\x19\n" +
	"\bquest_id\x18\x01 \x01(\tR\aquestId\"\x8b\x01\n" +
	"\x16RpcAcceptQuestResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12=\n" +
	"\rtrainer_quest\x18\x03 \x01(\v2\x18.MainServer.TrainerQuestR\ftrainerQuest\"P\n" +
	"\x17RpcCompleteQuestRequest\x12\x13\n" +
	"\x05db_id\x18\x01 \x01(\x03R\x04dbId\x12 \n" +
	"\fparent_db_id\x18\x02 \x01(\x03R\n" +
	"parentDbId\"N\n" +
	"\x18RpcCompleteQuestResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"7\n" +
	"\x1aRpcClaimQuestRewardRequest\x12\x19\n" +
	"\bquest_id\x18\x01 \x01(\x03R\aquestId\"Q\n" +
	"\x1bRpcClaimQuestRewardResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"T\n" +
	"\x1cRpcGetAvailableQuestsRequest\x124\n" +
	"\n" +
	"quest_type\x18\x01 \x01(\x0e2\x15.MainServer.QuestTypeR\tquestType\"\x82\x01\n" +
	"\x1dRpcGetAvailableQuestsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\x06quests\x18\x03 \x03(\v2\x15.MainServer.QuestInfoR\x06quests\"\xb1\x01\n" +
	"\x1bRpcGetTrainerQuestsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12?\n" +
	"\x0etrainer_quests\x18\x03 \x03(\v2\x18.MainServer.TrainerQuestR\rtrainerQuests\x12\x1d\n" +
	"\n" +
	"current_ts\x18\x04 \x01(\x03R\tcurrentTs\"\x89\x01\n" +
	"\"UpdateQuestThroughPointInfoRequest\x12\x19\n" +
	"\bquest_id\x18\x01 \x01(\x03R\aquestId\x12H\n" +
	"\x10throughPointInfo\x18\x02 \x01(\v2\x1c.MainServer.ThroughPointInfoR\x10throughPointInfo\"X\n" +
	"\"UpdateYarnThroughPointInfoResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*s\n" +
	"\x14ThroughPointInfoType\x12\x1d\n" +
	"\x19ThroughPointInfoType_none\x10\x00\x12\x1e\n" +
	"\x1aThroughPointInfoType_start\x10\x01\x12\x1c\n" +
	"\x18ThroughPointInfoType_end\x10\x02*\xae\x02\n" +
	"\x12TrainerQuestStatus\x12\x1b\n" +
	"\x17TrainerQuestStatus_none\x10\x00\x12\x1d\n" +
	"\x19TrainerQuestStatus_accept\x10\x01\x12\"\n" +
	"\x1eTrainerQuestStatus_finish_half\x10\x02\x12\x1d\n" +
	"\x19TrainerQuestStatus_finish\x10\x03\x12\x1d\n" +
	"\x19TrainerQuestStatus_reward\x10\x04\x12\x1d\n" +
	"\x19TrainerQuestStatus_cancel\x10\x05\x12\x1e\n" +
	"\x1aTrainerQuestStatus_timeout\x10\x06\x12\x1c\n" +
	"\x18TrainerQuestStatus_close\x10\a\x12\x1d\n" +
	"\x19TrainerQuestStatus_failed\x10\b*\xd3\x01\n" +
	"\x12QuestOperationType\x12\x1b\n" +
	"\x17QuestOperationType_none\x10\x00\x12\x1d\n" +
	"\x19QuestOperationType_accept\x10\x01\x12\x1f\n" +
	"\x1bQuestOperationType_complete\x10\x02\x12#\n" +
	"\x1fQuestOperationType_claim_reward\x10\x03\x12\x1d\n" +
	"\x19QuestOperationType_cancel\x10\x04\x12\x1c\n" +
	"\x18QuestOperationType_start\x10\x05B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerQuest_proto_rawDescOnce sync.Once
	file_MainServer_TrainerQuest_proto_rawDescData []byte
)

func file_MainServer_TrainerQuest_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerQuest_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerQuest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerQuest_proto_rawDesc), len(file_MainServer_TrainerQuest_proto_rawDesc)))
	})
	return file_MainServer_TrainerQuest_proto_rawDescData
}

var file_MainServer_TrainerQuest_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_MainServer_TrainerQuest_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_MainServer_TrainerQuest_proto_goTypes = []any{
	(ThroughPointInfoType)(0),                  // 0: MainServer.ThroughPointInfoType
	(TrainerQuestStatus)(0),                    // 1: MainServer.TrainerQuestStatus
	(QuestOperationType)(0),                    // 2: MainServer.QuestOperationType
	(*TrainerQuest)(nil),                       // 3: MainServer.TrainerQuest
	(*TrainerQuestCurrentInfo)(nil),            // 4: MainServer.TrainerQuestCurrentInfo
	(*TrainerQuestYarnInfo)(nil),               // 5: MainServer.TrainerQuestYarnInfo
	(*ThroughPointInfo)(nil),                   // 6: MainServer.ThroughPointInfo
	(*QuestOperationRequest)(nil),              // 7: MainServer.QuestOperationRequest
	(*QuestOperationResponse)(nil),             // 8: MainServer.QuestOperationResponse
	(*RpcAcceptQuestRequest)(nil),              // 9: MainServer.RpcAcceptQuestRequest
	(*RpcAcceptQuestResponse)(nil),             // 10: MainServer.RpcAcceptQuestResponse
	(*RpcCompleteQuestRequest)(nil),            // 11: MainServer.RpcCompleteQuestRequest
	(*RpcCompleteQuestResponse)(nil),           // 12: MainServer.RpcCompleteQuestResponse
	(*RpcClaimQuestRewardRequest)(nil),         // 13: MainServer.RpcClaimQuestRewardRequest
	(*RpcClaimQuestRewardResponse)(nil),        // 14: MainServer.RpcClaimQuestRewardResponse
	(*RpcGetAvailableQuestsRequest)(nil),       // 15: MainServer.RpcGetAvailableQuestsRequest
	(*RpcGetAvailableQuestsResponse)(nil),      // 16: MainServer.RpcGetAvailableQuestsResponse
	(*RpcGetTrainerQuestsResponse)(nil),        // 17: MainServer.RpcGetTrainerQuestsResponse
	(*UpdateQuestThroughPointInfoRequest)(nil), // 18: MainServer.UpdateQuestThroughPointInfoRequest
	(*UpdateYarnThroughPointInfoResponse)(nil), // 19: MainServer.UpdateYarnThroughPointInfoResponse
	nil,                       // 20: MainServer.TrainerQuestCurrentInfo.QuestDefaultConditionEntry
	nil,                       // 21: MainServer.TrainerQuestCurrentInfo.QuestProgressEntry
	nil,                       // 22: MainServer.TrainerQuestCurrentInfo.CompleteQuestMapEntry
	nil,                       // 23: MainServer.TrainerQuestYarnInfo.ThroughPointsEntry
	(QuestType)(0),            // 24: MainServer.QuestType
	(*QuestInfo)(nil),         // 25: MainServer.QuestInfo
	(*QuestCompleteInfo)(nil), // 26: MainServer.QuestCompleteInfo
}
var file_MainServer_TrainerQuest_proto_depIdxs = []int32{
	24, // 0: MainServer.TrainerQuest.quest_type:type_name -> MainServer.QuestType
	1,  // 1: MainServer.TrainerQuest.quest_status:type_name -> MainServer.TrainerQuestStatus
	4,  // 2: MainServer.TrainerQuest.quest_current_info:type_name -> MainServer.TrainerQuestCurrentInfo
	25, // 3: MainServer.TrainerQuest.quest_info:type_name -> MainServer.QuestInfo
	5,  // 4: MainServer.TrainerQuest.yarn_info:type_name -> MainServer.TrainerQuestYarnInfo
	20, // 5: MainServer.TrainerQuestCurrentInfo.quest_default_condition:type_name -> MainServer.TrainerQuestCurrentInfo.QuestDefaultConditionEntry
	21, // 6: MainServer.TrainerQuestCurrentInfo.quest_progress:type_name -> MainServer.TrainerQuestCurrentInfo.QuestProgressEntry
	22, // 7: MainServer.TrainerQuestCurrentInfo.complete_quest_map:type_name -> MainServer.TrainerQuestCurrentInfo.CompleteQuestMapEntry
	26, // 8: MainServer.TrainerQuestCurrentInfo.quest_complete_info:type_name -> MainServer.QuestCompleteInfo
	23, // 9: MainServer.TrainerQuestYarnInfo.through_points:type_name -> MainServer.TrainerQuestYarnInfo.ThroughPointsEntry
	0,  // 10: MainServer.ThroughPointInfo.info_type:type_name -> MainServer.ThroughPointInfoType
	2,  // 11: MainServer.QuestOperationRequest.operation_type:type_name -> MainServer.QuestOperationType
	3,  // 12: MainServer.QuestOperationResponse.trainer_quest:type_name -> MainServer.TrainerQuest
	3,  // 13: MainServer.RpcAcceptQuestResponse.trainer_quest:type_name -> MainServer.TrainerQuest
	24, // 14: MainServer.RpcGetAvailableQuestsRequest.quest_type:type_name -> MainServer.QuestType
	25, // 15: MainServer.RpcGetAvailableQuestsResponse.quests:type_name -> MainServer.QuestInfo
	3,  // 16: MainServer.RpcGetTrainerQuestsResponse.trainer_quests:type_name -> MainServer.TrainerQuest
	6,  // 17: MainServer.UpdateQuestThroughPointInfoRequest.throughPointInfo:type_name -> MainServer.ThroughPointInfo
	6,  // 18: MainServer.TrainerQuestYarnInfo.ThroughPointsEntry.value:type_name -> MainServer.ThroughPointInfo
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerQuest_proto_init() }
func file_MainServer_TrainerQuest_proto_init() {
	if File_MainServer_TrainerQuest_proto != nil {
		return
	}
	file_MainServer_QuestType_proto_init()
	file_MainServer_QuestInfo_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerQuest_proto_rawDesc), len(file_MainServer_TrainerQuest_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerQuest_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerQuest_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerQuest_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerQuest_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerQuest_proto = out.File
	file_MainServer_TrainerQuest_proto_goTypes = nil
	file_MainServer_TrainerQuest_proto_depIdxs = nil
}
