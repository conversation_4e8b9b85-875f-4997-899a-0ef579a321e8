// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/LocInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MainLandType int32

const (
	MainLandType_MainLand_None         MainLandType = 0
	MainLandType_MainLand_HeartGold    MainLandType = 1
	MainLandType_MainLand_Platinum     MainLandType = 2
	MainLandType_MainLand_White        MainLandType = 3
	MainLandType_MainLand_Instance     MainLandType = 100 //副本地图id锚点
	MainLandType_MainLand_MewtwoAndMew MainLandType = 101 //超梦与梦幻
)

// Enum value maps for MainLandType.
var (
	MainLandType_name = map[int32]string{
		0:   "MainLand_None",
		1:   "MainLand_HeartGold",
		2:   "MainLand_Platinum",
		3:   "MainLand_White",
		100: "MainLand_Instance",
		101: "MainLand_MewtwoAndMew",
	}
	MainLandType_value = map[string]int32{
		"MainLand_None":         0,
		"MainLand_HeartGold":    1,
		"MainLand_Platinum":     2,
		"MainLand_White":        3,
		"MainLand_Instance":     100,
		"MainLand_MewtwoAndMew": 101,
	}
)

func (x MainLandType) Enum() *MainLandType {
	p := new(MainLandType)
	*p = x
	return p
}

func (x MainLandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MainLandType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[0].Descriptor()
}

func (MainLandType) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[0]
}

func (x MainLandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MainLandType.Descriptor instead.
func (MainLandType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{0}
}

type TrainerLocStatus int32

const (
	TrainerLocStatus_LocStatus_None TrainerLocStatus = 0
	// LocStatus_Normal = 1;
	TrainerLocStatus_LocStatus_Battle  TrainerLocStatus = 2
	TrainerLocStatus_LocStatus_Fishing TrainerLocStatus = 3
	// LocStatus_Walk = 4;
	TrainerLocStatus_LocStatus_Run    TrainerLocStatus = 5
	TrainerLocStatus_LocStatus_Surf   TrainerLocStatus = 6
	TrainerLocStatus_LocStatus_Remove TrainerLocStatus = 7 //移除
)

// Enum value maps for TrainerLocStatus.
var (
	TrainerLocStatus_name = map[int32]string{
		0: "LocStatus_None",
		2: "LocStatus_Battle",
		3: "LocStatus_Fishing",
		5: "LocStatus_Run",
		6: "LocStatus_Surf",
		7: "LocStatus_Remove",
	}
	TrainerLocStatus_value = map[string]int32{
		"LocStatus_None":    0,
		"LocStatus_Battle":  2,
		"LocStatus_Fishing": 3,
		"LocStatus_Run":     5,
		"LocStatus_Surf":    6,
		"LocStatus_Remove":  7,
	}
)

func (x TrainerLocStatus) Enum() *TrainerLocStatus {
	p := new(TrainerLocStatus)
	*p = x
	return p
}

func (x TrainerLocStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerLocStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[1].Descriptor()
}

func (TrainerLocStatus) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[1]
}

func (x TrainerLocStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerLocStatus.Descriptor instead.
func (TrainerLocStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{1}
}

type LocAreaType int32

const (
	LocAreaType_LocAreaType_None     LocAreaType = 0
	LocAreaType_LocAreaType_Grass    LocAreaType = 1
	LocAreaType_LocAreaType_Forest   LocAreaType = 2
	LocAreaType_LocAreaType_Water    LocAreaType = 3
	LocAreaType_LocAreaType_Rock     LocAreaType = 4
	LocAreaType_LocAreaType_Snow     LocAreaType = 5
	LocAreaType_LocAreaType_Desert   LocAreaType = 6
	LocAreaType_LocAreaType_Cave     LocAreaType = 7
	LocAreaType_LocAreaType_Building LocAreaType = 8
	LocAreaType_LocAreaType_Other    LocAreaType = 9
)

// Enum value maps for LocAreaType.
var (
	LocAreaType_name = map[int32]string{
		0: "LocAreaType_None",
		1: "LocAreaType_Grass",
		2: "LocAreaType_Forest",
		3: "LocAreaType_Water",
		4: "LocAreaType_Rock",
		5: "LocAreaType_Snow",
		6: "LocAreaType_Desert",
		7: "LocAreaType_Cave",
		8: "LocAreaType_Building",
		9: "LocAreaType_Other",
	}
	LocAreaType_value = map[string]int32{
		"LocAreaType_None":     0,
		"LocAreaType_Grass":    1,
		"LocAreaType_Forest":   2,
		"LocAreaType_Water":    3,
		"LocAreaType_Rock":     4,
		"LocAreaType_Snow":     5,
		"LocAreaType_Desert":   6,
		"LocAreaType_Cave":     7,
		"LocAreaType_Building": 8,
		"LocAreaType_Other":    9,
	}
)

func (x LocAreaType) Enum() *LocAreaType {
	p := new(LocAreaType)
	*p = x
	return p
}

func (x LocAreaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocAreaType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[2].Descriptor()
}

func (LocAreaType) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[2]
}

func (x LocAreaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LocAreaType.Descriptor instead.
func (LocAreaType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{2}
}

type EncounterMethod int32

const (
	// 默认未知方式
	EncounterMethod_ENCOUNTER_METHOD_UNSPECIFIED EncounterMethod = 0
	// 冲浪（在水面上）
	EncounterMethod_SURF EncounterMethod = 1
	// 超级钓竿
	EncounterMethod_SUPER_ROD EncounterMethod = 2
	// 老旧钓竿
	EncounterMethod_OLD_ROD EncounterMethod = 3
	// 好钓竿
	EncounterMethod_GOOD_ROD EncounterMethod = 4
	// 赠送蛋
	EncounterMethod_GIFT_EGG EncounterMethod = 5
	// 普通走路遇敌（草地或洞穴中）
	EncounterMethod_WALK EncounterMethod = 6
	// 仅限一次的特殊遭遇（如传说宝可梦）
	EncounterMethod_ONLY_ONE EncounterMethod = 7
	// NPC 直接赠送
	EncounterMethod_GIFT EncounterMethod = 8
	// 使用碎石技能撞碎岩石
	EncounterMethod_ROCK_SMASH EncounterMethod = 9
	// 使用宝可梦之笛触发的遭遇
	EncounterMethod_POKEFLUTE EncounterMethod = 10
	// 使用喷水壶唤醒树木状的宝可梦
	EncounterMethod_SQUIRT_BOTTLE EncounterMethod = 11
	// 超级钓竿的特定钓鱼点
	EncounterMethod_SUPER_ROD_SPOTS EncounterMethod = 12
	// 冲浪的特定水域
	EncounterMethod_SURF_SPOTS EncounterMethod = 13
	// 黑草丛（可遇到双战/更强敌）
	EncounterMethod_DARK_GRASS EncounterMethod = 14
	// 草丛闪光点
	EncounterMethod_GRASS_SPOTS EncounterMethod = 15
	// 洞穴中罕见的遭遇点
	EncounterMethod_CAVE_SPOTS EncounterMethod = 16
	// 桥上的遭遇点（如天空之桥）
	EncounterMethod_BRIDGE_SPOTS EncounterMethod = 17
	// 使用探测镜（Devon Scope）后可见的宝可梦
	EncounterMethod_DEVON_SCOPE EncounterMethod = 18
	// 黄色花丛中的遭遇
	EncounterMethod_YELLOW_FLOWERS EncounterMethod = 19
	// 红色花丛中的遭遇
	EncounterMethod_RED_FLOWERS EncounterMethod = 20
	// 紫色花丛中的遭遇
	EncounterMethod_PURPLE_FLOWERS EncounterMethod = 21
	// 粗糙地形（岩石区等）
	EncounterMethod_ROUGH_TERRAIN EncounterMethod = 22
	// SOS 呼救战（宝可梦呼叫援军）
	EncounterMethod_SOS_ENCOUNTER EncounterMethod = 23
	// 岛屿扫描功能触发的遭遇
	EncounterMethod_ISLAND_SCAN EncounterMethod = 24
	// 冒泡水域（特殊钓鱼点）
	EncounterMethod_BUBBLING_SPOTS EncounterMethod = 25
	// 树果堆附近的遭遇
	EncounterMethod_BERRY_PILES EncounterMethod = 26
	// 与 NPC 交换获得
	EncounterMethod_NPC_TRADE EncounterMethod = 27
	// 从冒泡点中触发的 SOS 遭遇
	EncounterMethod_SOS_FROM_BUBBLING_SPOT EncounterMethod = 28
	// 草地中游走宝可梦的遭遇
	EncounterMethod_ROAMING_GRASS EncounterMethod = 29
	// 水面中游走宝可梦的遭遇
	EncounterMethod_ROAMING_WATER EncounterMethod = 30
)

// Enum value maps for EncounterMethod.
var (
	EncounterMethod_name = map[int32]string{
		0:  "ENCOUNTER_METHOD_UNSPECIFIED",
		1:  "SURF",
		2:  "SUPER_ROD",
		3:  "OLD_ROD",
		4:  "GOOD_ROD",
		5:  "GIFT_EGG",
		6:  "WALK",
		7:  "ONLY_ONE",
		8:  "GIFT",
		9:  "ROCK_SMASH",
		10: "POKEFLUTE",
		11: "SQUIRT_BOTTLE",
		12: "SUPER_ROD_SPOTS",
		13: "SURF_SPOTS",
		14: "DARK_GRASS",
		15: "GRASS_SPOTS",
		16: "CAVE_SPOTS",
		17: "BRIDGE_SPOTS",
		18: "DEVON_SCOPE",
		19: "YELLOW_FLOWERS",
		20: "RED_FLOWERS",
		21: "PURPLE_FLOWERS",
		22: "ROUGH_TERRAIN",
		23: "SOS_ENCOUNTER",
		24: "ISLAND_SCAN",
		25: "BUBBLING_SPOTS",
		26: "BERRY_PILES",
		27: "NPC_TRADE",
		28: "SOS_FROM_BUBBLING_SPOT",
		29: "ROAMING_GRASS",
		30: "ROAMING_WATER",
	}
	EncounterMethod_value = map[string]int32{
		"ENCOUNTER_METHOD_UNSPECIFIED": 0,
		"SURF":                         1,
		"SUPER_ROD":                    2,
		"OLD_ROD":                      3,
		"GOOD_ROD":                     4,
		"GIFT_EGG":                     5,
		"WALK":                         6,
		"ONLY_ONE":                     7,
		"GIFT":                         8,
		"ROCK_SMASH":                   9,
		"POKEFLUTE":                    10,
		"SQUIRT_BOTTLE":                11,
		"SUPER_ROD_SPOTS":              12,
		"SURF_SPOTS":                   13,
		"DARK_GRASS":                   14,
		"GRASS_SPOTS":                  15,
		"CAVE_SPOTS":                   16,
		"BRIDGE_SPOTS":                 17,
		"DEVON_SCOPE":                  18,
		"YELLOW_FLOWERS":               19,
		"RED_FLOWERS":                  20,
		"PURPLE_FLOWERS":               21,
		"ROUGH_TERRAIN":                22,
		"SOS_ENCOUNTER":                23,
		"ISLAND_SCAN":                  24,
		"BUBBLING_SPOTS":               25,
		"BERRY_PILES":                  26,
		"NPC_TRADE":                    27,
		"SOS_FROM_BUBBLING_SPOT":       28,
		"ROAMING_GRASS":                29,
		"ROAMING_WATER":                30,
	}
)

func (x EncounterMethod) Enum() *EncounterMethod {
	p := new(EncounterMethod)
	*p = x
	return p
}

func (x EncounterMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EncounterMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[3].Descriptor()
}

func (EncounterMethod) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[3]
}

func (x EncounterMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EncounterMethod.Descriptor instead.
func (EncounterMethod) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{3}
}

//	message AoiUploadLoc {
//	    float x = 1;
//	    float y = 2;
//	    float z = 3;
//	    MainLandType main_land_type = 4;
//	    // InstanceMapTpye instance_map_tpye = 5;
//	    TrainerLocStatus status = 6;
//	    int64 ts_ms = 7;
//	}
type TrainerLoc struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	X            float32                `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y            float32                `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Z            float32                `protobuf:"fixed32,3,opt,name=z,proto3" json:"z,omitempty"`
	MainLandType MainLandType           `protobuf:"varint,4,opt,name=main_land_type,json=mainLandType,proto3,enum=MainServer.MainLandType" json:"main_land_type,omitempty"`
	// InstanceMapTpye instance_map_tpye = 5;
	PcName        string           `protobuf:"bytes,5,opt,name=pc_name,json=pcName,proto3" json:"pc_name,omitempty"`
	Status        TrainerLocStatus `protobuf:"varint,6,opt,name=status,proto3,enum=MainServer.TrainerLocStatus" json:"status,omitempty"`
	TsMs          int64            `protobuf:"varint,7,opt,name=ts_ms,json=tsMs,proto3" json:"ts_ms,omitempty"`
	Flag          uint32           `protobuf:"varint,8,opt,name=flag,proto3" json:"flag,omitempty"` //客户端收到flag的结构不同，就需要去更新Trainer
	TrainerId     int64            `protobuf:"varint,9,opt,name=trainer_id,json=trainerId,proto3" json:"trainer_id,omitempty"`
	FlagOther     uint32           `protobuf:"varint,10,opt,name=flag_other,json=flagOther,proto3" json:"flag_other,omitempty"` //客户端收到flag_other的结构不同，暂不需要去更新Trainer,相差10更新一次
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerLoc) Reset() {
	*x = TrainerLoc{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerLoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLoc) ProtoMessage() {}

func (x *TrainerLoc) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLoc.ProtoReflect.Descriptor instead.
func (*TrainerLoc) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerLoc) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *TrainerLoc) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *TrainerLoc) GetZ() float32 {
	if x != nil {
		return x.Z
	}
	return 0
}

func (x *TrainerLoc) GetMainLandType() MainLandType {
	if x != nil {
		return x.MainLandType
	}
	return MainLandType_MainLand_None
}

func (x *TrainerLoc) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *TrainerLoc) GetStatus() TrainerLocStatus {
	if x != nil {
		return x.Status
	}
	return TrainerLocStatus_LocStatus_None
}

func (x *TrainerLoc) GetTsMs() int64 {
	if x != nil {
		return x.TsMs
	}
	return 0
}

func (x *TrainerLoc) GetFlag() uint32 {
	if x != nil {
		return x.Flag
	}
	return 0
}

func (x *TrainerLoc) GetTrainerId() int64 {
	if x != nil {
		return x.TrainerId
	}
	return 0
}

func (x *TrainerLoc) GetFlagOther() uint32 {
	if x != nil {
		return x.FlagOther
	}
	return 0
}

type AOIBroadcast struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	MainLandType MainLandType           `protobuf:"varint,1,opt,name=main_land_type,json=mainLandType,proto3,enum=MainServer.MainLandType" json:"main_land_type,omitempty"`
	// InstanceMapTpye instance_map_tpye = 2;
	// uint64 server_tick = 2;
	Players       []*TrainerLoc `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"` // 或者 PlayerStateInt
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AOIBroadcast) Reset() {
	*x = AOIBroadcast{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AOIBroadcast) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AOIBroadcast) ProtoMessage() {}

func (x *AOIBroadcast) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AOIBroadcast.ProtoReflect.Descriptor instead.
func (*AOIBroadcast) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{1}
}

func (x *AOIBroadcast) GetMainLandType() MainLandType {
	if x != nil {
		return x.MainLandType
	}
	return MainLandType_MainLand_None
}

func (x *AOIBroadcast) GetPlayers() []*TrainerLoc {
	if x != nil {
		return x.Players
	}
	return nil
}

type EncounterMethodPokeDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MinLevel      int32                  `protobuf:"varint,1,opt,name=min_level,json=minLevel,proto3" json:"min_level,omitempty"`
	MaxLevel      int32                  `protobuf:"varint,2,opt,name=max_level,json=maxLevel,proto3" json:"max_level,omitempty"`
	MaxChance     int32                  `protobuf:"varint,3,opt,name=max_chance,json=maxChance,proto3" json:"max_chance,omitempty"`
	MethodString  string                 `protobuf:"bytes,4,opt,name=method_string,json=methodString,proto3" json:"method_string,omitempty"`
	Method        EncounterMethod        `protobuf:"varint,5,opt,name=method,proto3,enum=MainServer.EncounterMethod" json:"method,omitempty"`
	Version       string                 `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EncounterMethodPokeDetail) Reset() {
	*x = EncounterMethodPokeDetail{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EncounterMethodPokeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncounterMethodPokeDetail) ProtoMessage() {}

func (x *EncounterMethodPokeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncounterMethodPokeDetail.ProtoReflect.Descriptor instead.
func (*EncounterMethodPokeDetail) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{2}
}

func (x *EncounterMethodPokeDetail) GetMinLevel() int32 {
	if x != nil {
		return x.MinLevel
	}
	return 0
}

func (x *EncounterMethodPokeDetail) GetMaxLevel() int32 {
	if x != nil {
		return x.MaxLevel
	}
	return 0
}

func (x *EncounterMethodPokeDetail) GetMaxChance() int32 {
	if x != nil {
		return x.MaxChance
	}
	return 0
}

func (x *EncounterMethodPokeDetail) GetMethodString() string {
	if x != nil {
		return x.MethodString
	}
	return ""
}

func (x *EncounterMethodPokeDetail) GetMethod() EncounterMethod {
	if x != nil {
		return x.Method
	}
	return EncounterMethod_ENCOUNTER_METHOD_UNSPECIFIED
}

func (x *EncounterMethodPokeDetail) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type EncounterMethodData struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Pokes         map[string]*EncounterMethodPokeDetail `protobuf:"bytes,1,rep,name=pokes,proto3" json:"pokes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MaxChance     int32                                 `protobuf:"varint,2,opt,name=max_chance,json=maxChance,proto3" json:"max_chance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EncounterMethodData) Reset() {
	*x = EncounterMethodData{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EncounterMethodData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncounterMethodData) ProtoMessage() {}

func (x *EncounterMethodData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncounterMethodData.ProtoReflect.Descriptor instead.
func (*EncounterMethodData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{3}
}

func (x *EncounterMethodData) GetPokes() map[string]*EncounterMethodPokeDetail {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *EncounterMethodData) GetMaxChance() int32 {
	if x != nil {
		return x.MaxChance
	}
	return 0
}

type RegionAreaEncounterMethodData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// EncounterMethod -> EncounterMethodData
	EncounterMethodData map[int32]*EncounterMethodData `protobuf:"bytes,1,rep,name=encounter_method_data,json=encounterMethodData,proto3" json:"encounter_method_data,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RegionAreaEncounterMethodData) Reset() {
	*x = RegionAreaEncounterMethodData{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionAreaEncounterMethodData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionAreaEncounterMethodData) ProtoMessage() {}

func (x *RegionAreaEncounterMethodData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionAreaEncounterMethodData.ProtoReflect.Descriptor instead.
func (*RegionAreaEncounterMethodData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{4}
}

func (x *RegionAreaEncounterMethodData) GetEncounterMethodData() map[int32]*EncounterMethodData {
	if x != nil {
		return x.EncounterMethodData
	}
	return nil
}

type RegionAreaEncounterMethodDataList struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// region area ->
	EncounterMethodDatas map[string]*RegionAreaEncounterMethodData `protobuf:"bytes,1,rep,name=encounter_method_datas,json=encounterMethodDatas,proto3" json:"encounter_method_datas,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RegionAreaEncounterMethodDataList) Reset() {
	*x = RegionAreaEncounterMethodDataList{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionAreaEncounterMethodDataList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionAreaEncounterMethodDataList) ProtoMessage() {}

func (x *RegionAreaEncounterMethodDataList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionAreaEncounterMethodDataList.ProtoReflect.Descriptor instead.
func (*RegionAreaEncounterMethodDataList) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{5}
}

func (x *RegionAreaEncounterMethodDataList) GetEncounterMethodDatas() map[string]*RegionAreaEncounterMethodData {
	if x != nil {
		return x.EncounterMethodDatas
	}
	return nil
}

type RegionEncounterMethodData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// region ->
	EncounterMethodData map[string]*RegionAreaEncounterMethodDataList `protobuf:"bytes,1,rep,name=encounter_method_data,json=encounterMethodData,proto3" json:"encounter_method_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RegionEncounterMethodData) Reset() {
	*x = RegionEncounterMethodData{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionEncounterMethodData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionEncounterMethodData) ProtoMessage() {}

func (x *RegionEncounterMethodData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionEncounterMethodData.ProtoReflect.Descriptor instead.
func (*RegionEncounterMethodData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{6}
}

func (x *RegionEncounterMethodData) GetEncounterMethodData() map[string]*RegionAreaEncounterMethodDataList {
	if x != nil {
		return x.EncounterMethodData
	}
	return nil
}

var File_MainServer_LocInfo_proto protoreflect.FileDescriptor

const file_MainServer_LocInfo_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/LocInfo.proto\x12\n" +
	"MainServer\"\xac\x02\n" +
	"\n" +
	"TrainerLoc\x12\f\n" +
	"\x01x\x18\x01 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x02R\x01y\x12\f\n" +
	"\x01z\x18\x03 \x01(\x02R\x01z\x12>\n" +
	"\x0emain_land_type\x18\x04 \x01(\x0e2\x18.MainServer.MainLandTypeR\fmainLandType\x12\x17\n" +
	"\apc_name\x18\x05 \x01(\tR\x06pcName\x124\n" +
	"\x06status\x18\x06 \x01(\x0e2\x1c.MainServer.TrainerLocStatusR\x06status\x12\x13\n" +
	"\x05ts_ms\x18\a \x01(\x03R\x04tsMs\x12\x12\n" +
	"\x04flag\x18\b \x01(\rR\x04flag\x12\x1d\n" +
	"\n" +
	"trainer_id\x18\t \x01(\x03R\ttrainerId\x12\x1d\n" +
	"\n" +
	"flag_other\x18\n" +
	" \x01(\rR\tflagOther\"\x80\x01\n" +
	"\fAOIBroadcast\x12>\n" +
	"\x0emain_land_type\x18\x01 \x01(\x0e2\x18.MainServer.MainLandTypeR\fmainLandType\x120\n" +
	"\aplayers\x18\x03 \x03(\v2\x16.MainServer.TrainerLocR\aplayers\"\xe8\x01\n" +
	"\x19EncounterMethodPokeDetail\x12\x1b\n" +
	"\tmin_level\x18\x01 \x01(\x05R\bminLevel\x12\x1b\n" +
	"\tmax_level\x18\x02 \x01(\x05R\bmaxLevel\x12\x1d\n" +
	"\n" +
	"max_chance\x18\x03 \x01(\x05R\tmaxChance\x12#\n" +
	"\rmethod_string\x18\x04 \x01(\tR\fmethodString\x123\n" +
	"\x06method\x18\x05 \x01(\x0e2\x1b.MainServer.EncounterMethodR\x06method\x12\x18\n" +
	"\aversion\x18\x06 \x01(\tR\aversion\"\xd7\x01\n" +
	"\x13EncounterMethodData\x12@\n" +
	"\x05pokes\x18\x01 \x03(\v2*.MainServer.EncounterMethodData.PokesEntryR\x05pokes\x12\x1d\n" +
	"\n" +
	"max_chance\x18\x02 \x01(\x05R\tmaxChance\x1a_\n" +
	"\n" +
	"PokesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12;\n" +
	"\x05value\x18\x02 \x01(\v2%.MainServer.EncounterMethodPokeDetailR\x05value:\x028\x01\"\x80\x02\n" +
	"\x1dRegionAreaEncounterMethodData\x12v\n" +
	"\x15encounter_method_data\x18\x01 \x03(\v2B.MainServer.RegionAreaEncounterMethodData.EncounterMethodDataEntryR\x13encounterMethodData\x1ag\n" +
	"\x18EncounterMethodDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.MainServer.EncounterMethodDataR\x05value:\x028\x01\"\x96\x02\n" +
	"!RegionAreaEncounterMethodDataList\x12}\n" +
	"\x16encounter_method_datas\x18\x01 \x03(\v2G.MainServer.RegionAreaEncounterMethodDataList.EncounterMethodDatasEntryR\x14encounterMethodDatas\x1ar\n" +
	"\x19EncounterMethodDatasEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12?\n" +
	"\x05value\x18\x02 \x01(\v2).MainServer.RegionAreaEncounterMethodDataR\x05value:\x028\x01\"\x86\x02\n" +
	"\x19RegionEncounterMethodData\x12r\n" +
	"\x15encounter_method_data\x18\x01 \x03(\v2>.MainServer.RegionEncounterMethodData.EncounterMethodDataEntryR\x13encounterMethodData\x1au\n" +
	"\x18EncounterMethodDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12C\n" +
	"\x05value\x18\x02 \x01(\v2-.MainServer.RegionAreaEncounterMethodDataListR\x05value:\x028\x01*\x96\x01\n" +
	"\fMainLandType\x12\x11\n" +
	"\rMainLand_None\x10\x00\x12\x16\n" +
	"\x12MainLand_HeartGold\x10\x01\x12\x15\n" +
	"\x11MainLand_Platinum\x10\x02\x12\x12\n" +
	"\x0eMainLand_White\x10\x03\x12\x15\n" +
	"\x11MainLand_Instance\x10d\x12\x19\n" +
	"\x15MainLand_MewtwoAndMew\x10e*\x90\x01\n" +
	"\x10TrainerLocStatus\x12\x12\n" +
	"\x0eLocStatus_None\x10\x00\x12\x14\n" +
	"\x10LocStatus_Battle\x10\x02\x12\x15\n" +
	"\x11LocStatus_Fishing\x10\x03\x12\x11\n" +
	"\rLocStatus_Run\x10\x05\x12\x12\n" +
	"\x0eLocStatus_Surf\x10\x06\x12\x14\n" +
	"\x10LocStatus_Remove\x10\a*\xf4\x01\n" +
	"\vLocAreaType\x12\x14\n" +
	"\x10LocAreaType_None\x10\x00\x12\x15\n" +
	"\x11LocAreaType_Grass\x10\x01\x12\x16\n" +
	"\x12LocAreaType_Forest\x10\x02\x12\x15\n" +
	"\x11LocAreaType_Water\x10\x03\x12\x14\n" +
	"\x10LocAreaType_Rock\x10\x04\x12\x14\n" +
	"\x10LocAreaType_Snow\x10\x05\x12\x16\n" +
	"\x12LocAreaType_Desert\x10\x06\x12\x14\n" +
	"\x10LocAreaType_Cave\x10\a\x12\x18\n" +
	"\x14LocAreaType_Building\x10\b\x12\x15\n" +
	"\x11LocAreaType_Other\x10\t*\xa8\x04\n" +
	"\x0fEncounterMethod\x12 \n" +
	"\x1cENCOUNTER_METHOD_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04SURF\x10\x01\x12\r\n" +
	"\tSUPER_ROD\x10\x02\x12\v\n" +
	"\aOLD_ROD\x10\x03\x12\f\n" +
	"\bGOOD_ROD\x10\x04\x12\f\n" +
	"\bGIFT_EGG\x10\x05\x12\b\n" +
	"\x04WALK\x10\x06\x12\f\n" +
	"\bONLY_ONE\x10\a\x12\b\n" +
	"\x04GIFT\x10\b\x12\x0e\n" +
	"\n" +
	"ROCK_SMASH\x10\t\x12\r\n" +
	"\tPOKEFLUTE\x10\n" +
	"\x12\x11\n" +
	"\rSQUIRT_BOTTLE\x10\v\x12\x13\n" +
	"\x0fSUPER_ROD_SPOTS\x10\f\x12\x0e\n" +
	"\n" +
	"SURF_SPOTS\x10\r\x12\x0e\n" +
	"\n" +
	"DARK_GRASS\x10\x0e\x12\x0f\n" +
	"\vGRASS_SPOTS\x10\x0f\x12\x0e\n" +
	"\n" +
	"CAVE_SPOTS\x10\x10\x12\x10\n" +
	"\fBRIDGE_SPOTS\x10\x11\x12\x0f\n" +
	"\vDEVON_SCOPE\x10\x12\x12\x12\n" +
	"\x0eYELLOW_FLOWERS\x10\x13\x12\x0f\n" +
	"\vRED_FLOWERS\x10\x14\x12\x12\n" +
	"\x0ePURPLE_FLOWERS\x10\x15\x12\x11\n" +
	"\rROUGH_TERRAIN\x10\x16\x12\x11\n" +
	"\rSOS_ENCOUNTER\x10\x17\x12\x0f\n" +
	"\vISLAND_SCAN\x10\x18\x12\x12\n" +
	"\x0eBUBBLING_SPOTS\x10\x19\x12\x0f\n" +
	"\vBERRY_PILES\x10\x1a\x12\r\n" +
	"\tNPC_TRADE\x10\x1b\x12\x1a\n" +
	"\x16SOS_FROM_BUBBLING_SPOT\x10\x1c\x12\x11\n" +
	"\rROAMING_GRASS\x10\x1d\x12\x11\n" +
	"\rROAMING_WATER\x10\x1eB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_LocInfo_proto_rawDescOnce sync.Once
	file_MainServer_LocInfo_proto_rawDescData []byte
)

func file_MainServer_LocInfo_proto_rawDescGZIP() []byte {
	file_MainServer_LocInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_LocInfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_LocInfo_proto_rawDesc), len(file_MainServer_LocInfo_proto_rawDesc)))
	})
	return file_MainServer_LocInfo_proto_rawDescData
}

var file_MainServer_LocInfo_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_MainServer_LocInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_MainServer_LocInfo_proto_goTypes = []any{
	(MainLandType)(0),                         // 0: MainServer.MainLandType
	(TrainerLocStatus)(0),                     // 1: MainServer.TrainerLocStatus
	(LocAreaType)(0),                          // 2: MainServer.LocAreaType
	(EncounterMethod)(0),                      // 3: MainServer.EncounterMethod
	(*TrainerLoc)(nil),                        // 4: MainServer.TrainerLoc
	(*AOIBroadcast)(nil),                      // 5: MainServer.AOIBroadcast
	(*EncounterMethodPokeDetail)(nil),         // 6: MainServer.EncounterMethodPokeDetail
	(*EncounterMethodData)(nil),               // 7: MainServer.EncounterMethodData
	(*RegionAreaEncounterMethodData)(nil),     // 8: MainServer.RegionAreaEncounterMethodData
	(*RegionAreaEncounterMethodDataList)(nil), // 9: MainServer.RegionAreaEncounterMethodDataList
	(*RegionEncounterMethodData)(nil),         // 10: MainServer.RegionEncounterMethodData
	nil,                                       // 11: MainServer.EncounterMethodData.PokesEntry
	nil,                                       // 12: MainServer.RegionAreaEncounterMethodData.EncounterMethodDataEntry
	nil,                                       // 13: MainServer.RegionAreaEncounterMethodDataList.EncounterMethodDatasEntry
	nil,                                       // 14: MainServer.RegionEncounterMethodData.EncounterMethodDataEntry
}
var file_MainServer_LocInfo_proto_depIdxs = []int32{
	0,  // 0: MainServer.TrainerLoc.main_land_type:type_name -> MainServer.MainLandType
	1,  // 1: MainServer.TrainerLoc.status:type_name -> MainServer.TrainerLocStatus
	0,  // 2: MainServer.AOIBroadcast.main_land_type:type_name -> MainServer.MainLandType
	4,  // 3: MainServer.AOIBroadcast.players:type_name -> MainServer.TrainerLoc
	3,  // 4: MainServer.EncounterMethodPokeDetail.method:type_name -> MainServer.EncounterMethod
	11, // 5: MainServer.EncounterMethodData.pokes:type_name -> MainServer.EncounterMethodData.PokesEntry
	12, // 6: MainServer.RegionAreaEncounterMethodData.encounter_method_data:type_name -> MainServer.RegionAreaEncounterMethodData.EncounterMethodDataEntry
	13, // 7: MainServer.RegionAreaEncounterMethodDataList.encounter_method_datas:type_name -> MainServer.RegionAreaEncounterMethodDataList.EncounterMethodDatasEntry
	14, // 8: MainServer.RegionEncounterMethodData.encounter_method_data:type_name -> MainServer.RegionEncounterMethodData.EncounterMethodDataEntry
	6,  // 9: MainServer.EncounterMethodData.PokesEntry.value:type_name -> MainServer.EncounterMethodPokeDetail
	7,  // 10: MainServer.RegionAreaEncounterMethodData.EncounterMethodDataEntry.value:type_name -> MainServer.EncounterMethodData
	8,  // 11: MainServer.RegionAreaEncounterMethodDataList.EncounterMethodDatasEntry.value:type_name -> MainServer.RegionAreaEncounterMethodData
	9,  // 12: MainServer.RegionEncounterMethodData.EncounterMethodDataEntry.value:type_name -> MainServer.RegionAreaEncounterMethodDataList
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_MainServer_LocInfo_proto_init() }
func file_MainServer_LocInfo_proto_init() {
	if File_MainServer_LocInfo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_LocInfo_proto_rawDesc), len(file_MainServer_LocInfo_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_LocInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_LocInfo_proto_depIdxs,
		EnumInfos:         file_MainServer_LocInfo_proto_enumTypes,
		MessageInfos:      file_MainServer_LocInfo_proto_msgTypes,
	}.Build()
	File_MainServer_LocInfo_proto = out.File
	file_MainServer_LocInfo_proto_goTypes = nil
	file_MainServer_LocInfo_proto_depIdxs = nil
}
