// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/MoveLearnset.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义学习方式的枚举
type MoveLearnMethod int32

const (
	MoveLearnMethod_OTHER    MoveLearnMethod = 0 // O: 其他或未识别的方式
	MoveLearnMethod_MACHINE  MoveLearnMethod = 1 // M: 通过技术机器 (TM/HM)
	MoveLearnMethod_EGG_MOVE MoveLearnMethod = 2 // E: 通过遗传 (Egg Move)
	MoveLearnMethod_TUTOR    MoveLearnMethod = 3 // T: 通过教学 (Tutor)
	MoveLearnMethod_EVENT_S  MoveLearnMethod = 4 // S: 通过事件 (Event)
	MoveLearnMethod_LEVEL_UP MoveLearnMethod = 5 // L: 通过升级 (Level Up)
	MoveLearnMethod_VIRTUAL  MoveLearnMethod = 6 // V: 虚拟控制台 (Virtual Console)
)

// Enum value maps for MoveLearnMethod.
var (
	MoveLearnMethod_name = map[int32]string{
		0: "OTHER",
		1: "MACHINE",
		2: "EGG_MOVE",
		3: "TUTOR",
		4: "EVENT_S",
		5: "LEVEL_UP",
		6: "VIRTUAL",
	}
	MoveLearnMethod_value = map[string]int32{
		"OTHER":    0,
		"MACHINE":  1,
		"EGG_MOVE": 2,
		"TUTOR":    3,
		"EVENT_S":  4,
		"LEVEL_UP": 5,
		"VIRTUAL":  6,
	}
)

func (x MoveLearnMethod) Enum() *MoveLearnMethod {
	p := new(MoveLearnMethod)
	*p = x
	return p
}

func (x MoveLearnMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MoveLearnMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_MoveLearnset_proto_enumTypes[0].Descriptor()
}

func (MoveLearnMethod) Type() protoreflect.EnumType {
	return &file_MainServer_MoveLearnset_proto_enumTypes[0]
}

func (x MoveLearnMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MoveLearnMethod.Descriptor instead.
func (MoveLearnMethod) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{0}
}

// 定义 GenerationMove 消息
type GenerationMove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MoveName      string                 `protobuf:"bytes,1,opt,name=move_name,json=moveName,proto3" json:"move_name,omitempty"`
	Generation    int32                  `protobuf:"varint,2,opt,name=generation,proto3" json:"generation,omitempty"`                         // 世代编号
	Method        MoveLearnMethod        `protobuf:"varint,3,opt,name=method,proto3,enum=MainServer.MoveLearnMethod" json:"method,omitempty"` // 学习方式
	Level         *int32                 `protobuf:"varint,4,opt,name=level,proto3,oneof" json:"level,omitempty"`                             // 学习等级，如果有的话
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerationMove) Reset() {
	*x = GenerationMove{}
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerationMove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerationMove) ProtoMessage() {}

func (x *GenerationMove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerationMove.ProtoReflect.Descriptor instead.
func (*GenerationMove) Descriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{0}
}

func (x *GenerationMove) GetMoveName() string {
	if x != nil {
		return x.MoveName
	}
	return ""
}

func (x *GenerationMove) GetGeneration() int32 {
	if x != nil {
		return x.Generation
	}
	return 0
}

func (x *GenerationMove) GetMethod() MoveLearnMethod {
	if x != nil {
		return x.Method
	}
	return MoveLearnMethod_OTHER
}

func (x *GenerationMove) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

type PokemonMoves struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Moves         []*GenerationMove      `protobuf:"bytes,1,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokemonMoves) Reset() {
	*x = PokemonMoves{}
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokemonMoves) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokemonMoves) ProtoMessage() {}

func (x *PokemonMoves) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokemonMoves.ProtoReflect.Descriptor instead.
func (*PokemonMoves) Descriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{1}
}

func (x *PokemonMoves) GetMoves() []*GenerationMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

type Learnsets struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Pokemons      map[string]*PokemonMoves `protobuf:"bytes,1,rep,name=pokemons,proto3" json:"pokemons,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Learnsets) Reset() {
	*x = Learnsets{}
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Learnsets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Learnsets) ProtoMessage() {}

func (x *Learnsets) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Learnsets.ProtoReflect.Descriptor instead.
func (*Learnsets) Descriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{2}
}

func (x *Learnsets) GetPokemons() map[string]*PokemonMoves {
	if x != nil {
		return x.Pokemons
	}
	return nil
}

var File_MainServer_MoveLearnset_proto protoreflect.FileDescriptor

const file_MainServer_MoveLearnset_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/MoveLearnset.proto\x12\n" +
	"MainServer\"\xa7\x01\n" +
	"\x0eGenerationMove\x12\x1b\n" +
	"\tmove_name\x18\x01 \x01(\tR\bmoveName\x12\x1e\n" +
	"\n" +
	"generation\x18\x02 \x01(\x05R\n" +
	"generation\x123\n" +
	"\x06method\x18\x03 \x01(\x0e2\x1b.MainServer.MoveLearnMethodR\x06method\x12\x19\n" +
	"\x05level\x18\x04 \x01(\x05H\x00R\x05level\x88\x01\x01B\b\n" +
	"\x06_level\"@\n" +
	"\fPokemonMoves\x120\n" +
	"\x05moves\x18\x01 \x03(\v2\x1a.MainServer.GenerationMoveR\x05moves\"\xa3\x01\n" +
	"\tLearnsets\x12?\n" +
	"\bpokemons\x18\x01 \x03(\v2#.MainServer.Learnsets.PokemonsEntryR\bpokemons\x1aU\n" +
	"\rPokemonsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.MainServer.PokemonMovesR\x05value:\x028\x01*j\n" +
	"\x0fMoveLearnMethod\x12\t\n" +
	"\x05OTHER\x10\x00\x12\v\n" +
	"\aMACHINE\x10\x01\x12\f\n" +
	"\bEGG_MOVE\x10\x02\x12\t\n" +
	"\x05TUTOR\x10\x03\x12\v\n" +
	"\aEVENT_S\x10\x04\x12\f\n" +
	"\bLEVEL_UP\x10\x05\x12\v\n" +
	"\aVIRTUAL\x10\x06B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_MoveLearnset_proto_rawDescOnce sync.Once
	file_MainServer_MoveLearnset_proto_rawDescData []byte
)

func file_MainServer_MoveLearnset_proto_rawDescGZIP() []byte {
	file_MainServer_MoveLearnset_proto_rawDescOnce.Do(func() {
		file_MainServer_MoveLearnset_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_MoveLearnset_proto_rawDesc), len(file_MainServer_MoveLearnset_proto_rawDesc)))
	})
	return file_MainServer_MoveLearnset_proto_rawDescData
}

var file_MainServer_MoveLearnset_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_MoveLearnset_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_MoveLearnset_proto_goTypes = []any{
	(MoveLearnMethod)(0),   // 0: MainServer.MoveLearnMethod
	(*GenerationMove)(nil), // 1: MainServer.GenerationMove
	(*PokemonMoves)(nil),   // 2: MainServer.PokemonMoves
	(*Learnsets)(nil),      // 3: MainServer.Learnsets
	nil,                    // 4: MainServer.Learnsets.PokemonsEntry
}
var file_MainServer_MoveLearnset_proto_depIdxs = []int32{
	0, // 0: MainServer.GenerationMove.method:type_name -> MainServer.MoveLearnMethod
	1, // 1: MainServer.PokemonMoves.moves:type_name -> MainServer.GenerationMove
	4, // 2: MainServer.Learnsets.pokemons:type_name -> MainServer.Learnsets.PokemonsEntry
	2, // 3: MainServer.Learnsets.PokemonsEntry.value:type_name -> MainServer.PokemonMoves
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_MoveLearnset_proto_init() }
func file_MainServer_MoveLearnset_proto_init() {
	if File_MainServer_MoveLearnset_proto != nil {
		return
	}
	file_MainServer_MoveLearnset_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_MoveLearnset_proto_rawDesc), len(file_MainServer_MoveLearnset_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_MoveLearnset_proto_goTypes,
		DependencyIndexes: file_MainServer_MoveLearnset_proto_depIdxs,
		EnumInfos:         file_MainServer_MoveLearnset_proto_enumTypes,
		MessageInfos:      file_MainServer_MoveLearnset_proto_msgTypes,
	}.Build()
	File_MainServer_MoveLearnset_proto = out.File
	file_MainServer_MoveLearnset_proto_goTypes = nil
	file_MainServer_MoveLearnset_proto_depIdxs = nil
}
