// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeTeam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
	CreateTs      int64                  `protobuf:"varint,5,opt,name=createTs,proto3" json:"createTs,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,6,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
	HireTids      []int64                `protobuf:"varint,7,rep,packed,name=hireTids,proto3" json:"hireTids,omitempty"` //租赁人
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeam) Reset() {
	*x = PokeTeam{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeam) ProtoMessage() {}

func (x *PokeTeam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeam.ProtoReflect.Descriptor instead.
func (*PokeTeam) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{0}
}

func (x *PokeTeam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeTeam) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeTeam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeTeam) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeTeam) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeTeam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeTeam) GetHireTids() []int64 {
	if x != nil {
		return x.HireTids
	}
	return nil
}

type PokeTeamConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// poke_team_id	team_name	poke_init_boost	poke_min_boost	poke_immunity_type	poke_name_1	poke_ivs_1	poke_name_2	poke_ivs_2	poke_name_3	poke_ivs_3	poke_name_4	poke_ivs_4	poke_name_5	poke_ivs_5	poke_name_6	poke_ivs_6
	PokeTeamId    string            `protobuf:"bytes,1,opt,name=pokeTeamId,proto3" json:"pokeTeamId,omitempty"`
	TeamName      string            `protobuf:"bytes,2,opt,name=teamName,proto3" json:"teamName,omitempty"`
	Pokes         []*BattlePokeInfo `protobuf:"bytes,3,rep,name=pokes,proto3" json:"pokes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeamConfig) Reset() {
	*x = PokeTeamConfig{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeamConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeamConfig) ProtoMessage() {}

func (x *PokeTeamConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeamConfig.ProtoReflect.Descriptor instead.
func (*PokeTeamConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{1}
}

func (x *PokeTeamConfig) GetPokeTeamId() string {
	if x != nil {
		return x.PokeTeamId
	}
	return ""
}

func (x *PokeTeamConfig) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *PokeTeamConfig) GetPokes() []*BattlePokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type BattlePokeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Poke          *Poke                  `protobuf:"bytes,1,opt,name=poke,proto3" json:"poke,omitempty"`
	PokeInitBoost *PokeBoostStat         `protobuf:"bytes,2,opt,name=poke_init_boost,json=pokeInitBoost,proto3" json:"poke_init_boost,omitempty"`
	PokeMinBoost  *PokeBoostStat         `protobuf:"bytes,3,opt,name=poke_min_boost,json=pokeMinBoost,proto3" json:"poke_min_boost,omitempty"`
	ImmunityTypes []PokeTypeEnum         `protobuf:"varint,4,rep,packed,name=immunity_types,json=immunityTypes,proto3,enum=MainServer.PokeTypeEnum" json:"immunity_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattlePokeInfo) Reset() {
	*x = BattlePokeInfo{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattlePokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePokeInfo) ProtoMessage() {}

func (x *BattlePokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePokeInfo.ProtoReflect.Descriptor instead.
func (*BattlePokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{2}
}

func (x *BattlePokeInfo) GetPoke() *Poke {
	if x != nil {
		return x.Poke
	}
	return nil
}

func (x *BattlePokeInfo) GetPokeInitBoost() *PokeBoostStat {
	if x != nil {
		return x.PokeInitBoost
	}
	return nil
}

func (x *BattlePokeInfo) GetPokeMinBoost() *PokeBoostStat {
	if x != nil {
		return x.PokeMinBoost
	}
	return nil
}

func (x *BattlePokeInfo) GetImmunityTypes() []PokeTypeEnum {
	if x != nil {
		return x.ImmunityTypes
	}
	return nil
}

type PokeConfigMap struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Datas         map[string]*BattlePokeInfo `protobuf:"bytes,1,rep,name=datas,proto3" json:"datas,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeConfigMap) Reset() {
	*x = PokeConfigMap{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeConfigMap) ProtoMessage() {}

func (x *PokeConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeConfigMap.ProtoReflect.Descriptor instead.
func (*PokeConfigMap) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{3}
}

func (x *PokeConfigMap) GetDatas() map[string]*BattlePokeInfo {
	if x != nil {
		return x.Datas
	}
	return nil
}

type PokeTeamConfigList struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Configs       map[string]*PokeTeamConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeamConfigList) Reset() {
	*x = PokeTeamConfigList{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeamConfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeamConfigList) ProtoMessage() {}

func (x *PokeTeamConfigList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeamConfigList.ProtoReflect.Descriptor instead.
func (*PokeTeamConfigList) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{4}
}

func (x *PokeTeamConfigList) GetConfigs() map[string]*PokeTeamConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type PSInitPokeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	PokeInitBoost *PokeBoostStat         `protobuf:"bytes,4,opt,name=poke_init_boost,json=pokeInitBoost,proto3" json:"poke_init_boost,omitempty"`
	PokeMinBoost  *PokeBoostStat         `protobuf:"bytes,5,opt,name=poke_min_boost,json=pokeMinBoost,proto3" json:"poke_min_boost,omitempty"`
	ImmunityTypes []PokeTypeEnum         `protobuf:"varint,6,rep,packed,name=immunity_types,json=immunityTypes,proto3,enum=MainServer.PokeTypeEnum" json:"immunity_types,omitempty"`
	CaptureRate   int32                  `protobuf:"varint,7,opt,name=capture_rate,json=captureRate,proto3" json:"capture_rate,omitempty"`
	CanCapture    bool                   `protobuf:"varint,8,opt,name=can_capture,json=canCapture,proto3" json:"can_capture,omitempty"`
	HpSub         int32                  `protobuf:"varint,9,opt,name=hp_sub,json=hpSub,proto3" json:"hp_sub,omitempty"`
	Moves         []*PokeSimpleMove      `protobuf:"bytes,10,rep,name=moves,proto3" json:"moves,omitempty"` // 宝可梦的招式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSInitPokeInfo) Reset() {
	*x = PSInitPokeInfo{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSInitPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSInitPokeInfo) ProtoMessage() {}

func (x *PSInitPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSInitPokeInfo.ProtoReflect.Descriptor instead.
func (*PSInitPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{5}
}

func (x *PSInitPokeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PSInitPokeInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PSInitPokeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PSInitPokeInfo) GetPokeInitBoost() *PokeBoostStat {
	if x != nil {
		return x.PokeInitBoost
	}
	return nil
}

func (x *PSInitPokeInfo) GetPokeMinBoost() *PokeBoostStat {
	if x != nil {
		return x.PokeMinBoost
	}
	return nil
}

func (x *PSInitPokeInfo) GetImmunityTypes() []PokeTypeEnum {
	if x != nil {
		return x.ImmunityTypes
	}
	return nil
}

func (x *PSInitPokeInfo) GetCaptureRate() int32 {
	if x != nil {
		return x.CaptureRate
	}
	return 0
}

func (x *PSInitPokeInfo) GetCanCapture() bool {
	if x != nil {
		return x.CanCapture
	}
	return false
}

func (x *PSInitPokeInfo) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

func (x *PSInitPokeInfo) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

var File_MainServer_PokeTeam_proto protoreflect.FileDescriptor

const file_MainServer_PokeTeam_proto_rawDesc = "" +
	"\n" +
	"\x19MainServer/PokeTeam.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\"\xbc\x01\n" +
	"\bPokeTeam\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12&\n" +
	"\x05pokes\x18\x04 \x03(\v2\x10.MainServer.PokeR\x05pokes\x12\x1a\n" +
	"\bcreateTs\x18\x05 \x01(\x03R\bcreateTs\x12\x1a\n" +
	"\bupdateTs\x18\x06 \x01(\x03R\bupdateTs\x12\x1a\n" +
	"\bhireTids\x18\a \x03(\x03R\bhireTids\"~\n" +
	"\x0ePokeTeamConfig\x12\x1e\n" +
	"\n" +
	"pokeTeamId\x18\x01 \x01(\tR\n" +
	"pokeTeamId\x12\x1a\n" +
	"\bteamName\x18\x02 \x01(\tR\bteamName\x120\n" +
	"\x05pokes\x18\x03 \x03(\v2\x1a.MainServer.BattlePokeInfoR\x05pokes\"\xfb\x01\n" +
	"\x0eBattlePokeInfo\x12$\n" +
	"\x04poke\x18\x01 \x01(\v2\x10.MainServer.PokeR\x04poke\x12A\n" +
	"\x0fpoke_init_boost\x18\x02 \x01(\v2\x19.MainServer.PokeBoostStatR\rpokeInitBoost\x12?\n" +
	"\x0epoke_min_boost\x18\x03 \x01(\v2\x19.MainServer.PokeBoostStatR\fpokeMinBoost\x12?\n" +
	"\x0eimmunity_types\x18\x04 \x03(\x0e2\x18.MainServer.PokeTypeEnumR\rimmunityTypes\"\xa1\x01\n" +
	"\rPokeConfigMap\x12:\n" +
	"\x05datas\x18\x01 \x03(\v2$.MainServer.PokeConfigMap.DatasEntryR\x05datas\x1aT\n" +
	"\n" +
	"DatasEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.BattlePokeInfoR\x05value:\x028\x01\"\xb3\x01\n" +
	"\x12PokeTeamConfigList\x12E\n" +
	"\aconfigs\x18\x01 \x03(\v2+.MainServer.PokeTeamConfigList.ConfigsEntryR\aconfigs\x1aV\n" +
	"\fConfigsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.PokeTeamConfigR\x05value:\x028\x01\"\x98\x03\n" +
	"\x0ePSInitPokeInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12A\n" +
	"\x0fpoke_init_boost\x18\x04 \x01(\v2\x19.MainServer.PokeBoostStatR\rpokeInitBoost\x12?\n" +
	"\x0epoke_min_boost\x18\x05 \x01(\v2\x19.MainServer.PokeBoostStatR\fpokeMinBoost\x12?\n" +
	"\x0eimmunity_types\x18\x06 \x03(\x0e2\x18.MainServer.PokeTypeEnumR\rimmunityTypes\x12!\n" +
	"\fcapture_rate\x18\a \x01(\x05R\vcaptureRate\x12\x1f\n" +
	"\vcan_capture\x18\b \x01(\bR\n" +
	"canCapture\x12\x15\n" +
	"\x06hp_sub\x18\t \x01(\x05R\x05hpSub\x120\n" +
	"\x05moves\x18\n" +
	" \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05movesB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeTeam_proto_rawDescOnce sync.Once
	file_MainServer_PokeTeam_proto_rawDescData []byte
)

func file_MainServer_PokeTeam_proto_rawDescGZIP() []byte {
	file_MainServer_PokeTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeTeam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)))
	})
	return file_MainServer_PokeTeam_proto_rawDescData
}

var file_MainServer_PokeTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_MainServer_PokeTeam_proto_goTypes = []any{
	(*PokeTeam)(nil),           // 0: MainServer.PokeTeam
	(*PokeTeamConfig)(nil),     // 1: MainServer.PokeTeamConfig
	(*BattlePokeInfo)(nil),     // 2: MainServer.BattlePokeInfo
	(*PokeConfigMap)(nil),      // 3: MainServer.PokeConfigMap
	(*PokeTeamConfigList)(nil), // 4: MainServer.PokeTeamConfigList
	(*PSInitPokeInfo)(nil),     // 5: MainServer.PSInitPokeInfo
	nil,                        // 6: MainServer.PokeConfigMap.DatasEntry
	nil,                        // 7: MainServer.PokeTeamConfigList.ConfigsEntry
	(*Poke)(nil),               // 8: MainServer.Poke
	(*PokeBoostStat)(nil),      // 9: MainServer.PokeBoostStat
	(PokeTypeEnum)(0),          // 10: MainServer.PokeTypeEnum
	(*PokeSimpleMove)(nil),     // 11: MainServer.PokeSimpleMove
}
var file_MainServer_PokeTeam_proto_depIdxs = []int32{
	8,  // 0: MainServer.PokeTeam.pokes:type_name -> MainServer.Poke
	2,  // 1: MainServer.PokeTeamConfig.pokes:type_name -> MainServer.BattlePokeInfo
	8,  // 2: MainServer.BattlePokeInfo.poke:type_name -> MainServer.Poke
	9,  // 3: MainServer.BattlePokeInfo.poke_init_boost:type_name -> MainServer.PokeBoostStat
	9,  // 4: MainServer.BattlePokeInfo.poke_min_boost:type_name -> MainServer.PokeBoostStat
	10, // 5: MainServer.BattlePokeInfo.immunity_types:type_name -> MainServer.PokeTypeEnum
	6,  // 6: MainServer.PokeConfigMap.datas:type_name -> MainServer.PokeConfigMap.DatasEntry
	7,  // 7: MainServer.PokeTeamConfigList.configs:type_name -> MainServer.PokeTeamConfigList.ConfigsEntry
	9,  // 8: MainServer.PSInitPokeInfo.poke_init_boost:type_name -> MainServer.PokeBoostStat
	9,  // 9: MainServer.PSInitPokeInfo.poke_min_boost:type_name -> MainServer.PokeBoostStat
	10, // 10: MainServer.PSInitPokeInfo.immunity_types:type_name -> MainServer.PokeTypeEnum
	11, // 11: MainServer.PSInitPokeInfo.moves:type_name -> MainServer.PokeSimpleMove
	2,  // 12: MainServer.PokeConfigMap.DatasEntry.value:type_name -> MainServer.BattlePokeInfo
	1,  // 13: MainServer.PokeTeamConfigList.ConfigsEntry.value:type_name -> MainServer.PokeTeamConfig
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_MainServer_PokeTeam_proto_init() }
func file_MainServer_PokeTeam_proto_init() {
	if File_MainServer_PokeTeam_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeTeam_proto_depIdxs,
		MessageInfos:      file_MainServer_PokeTeam_proto_msgTypes,
	}.Build()
	File_MainServer_PokeTeam_proto = out.File
	file_MainServer_PokeTeam_proto_goTypes = nil
	file_MainServer_PokeTeam_proto_depIdxs = nil
}
