// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeBook.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 枚举
type PokedexStatus int32

const (
	PokedexStatus_PokedexStatus_NotSeen     PokedexStatus = 0
	PokedexStatus_PokedexStatus_Seen        PokedexStatus = 1
	PokedexStatus_PokedexStatus_Caught      PokedexStatus = 2
	PokedexStatus_PokedexStatus_CaughtShiny PokedexStatus = 3
)

// Enum value maps for PokedexStatus.
var (
	PokedexStatus_name = map[int32]string{
		0: "PokedexStatus_NotSeen",
		1: "PokedexStatus_Seen",
		2: "PokedexStatus_Caught",
		3: "PokedexStatus_CaughtShiny",
	}
	PokedexStatus_value = map[string]int32{
		"PokedexStatus_NotSeen":     0,
		"PokedexStatus_Seen":        1,
		"PokedexStatus_Caught":      2,
		"PokedexStatus_CaughtShiny": 3,
	}
)

func (x PokedexStatus) Enum() *PokedexStatus {
	p := new(PokedexStatus)
	*p = x
	return p
}

func (x PokedexStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokedexStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeBook_proto_enumTypes[0].Descriptor()
}

func (PokedexStatus) Type() protoreflect.EnumType {
	return &file_MainServer_PokeBook_proto_enumTypes[0]
}

func (x PokedexStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokedexStatus.Descriptor instead.
func (PokedexStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{0}
}

// 训练师图鉴数据
type TrainerPokebook struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                          // 数据库ID
	Tid               int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                        // 训练师ID
	PokedexData       []byte                 `protobuf:"bytes,3,opt,name=pokedex_data,json=pokedexData,proto3" json:"pokedex_data,omitempty"`                      // 图鉴数据（每只精灵2bit）
	TotalPokemonCount int32                  `protobuf:"varint,4,opt,name=total_pokemon_count,json=totalPokemonCount,proto3" json:"total_pokemon_count,omitempty"` // 总精灵数量
	CreateTs          int64                  `protobuf:"varint,5,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                              // 创建时间戳
	UpdateTs          int64                  `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                              // 更新时间戳
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TrainerPokebook) Reset() {
	*x = TrainerPokebook{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerPokebook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerPokebook) ProtoMessage() {}

func (x *TrainerPokebook) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerPokebook.ProtoReflect.Descriptor instead.
func (*TrainerPokebook) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerPokebook) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerPokebook) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TrainerPokebook) GetPokedexData() []byte {
	if x != nil {
		return x.PokedexData
	}
	return nil
}

func (x *TrainerPokebook) GetTotalPokemonCount() int32 {
	if x != nil {
		return x.TotalPokemonCount
	}
	return 0
}

func (x *TrainerPokebook) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *TrainerPokebook) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 图鉴统计信息
type PokebookStats struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TotalPokemon   int32                  `protobuf:"varint,1,opt,name=total_pokemon,json=totalPokemon,proto3" json:"total_pokemon,omitempty"`        // 总精灵数
	SeenCount      int32                  `protobuf:"varint,2,opt,name=seen_count,json=seenCount,proto3" json:"seen_count,omitempty"`                 // 见过的精灵数
	CaughtCount    int32                  `protobuf:"varint,3,opt,name=caught_count,json=caughtCount,proto3" json:"caught_count,omitempty"`           // 捕捉的精灵数
	ShinyCount     int32                  `protobuf:"varint,4,opt,name=shiny_count,json=shinyCount,proto3" json:"shiny_count,omitempty"`              // 闪光精灵数
	CompletionRate float32                `protobuf:"fixed32,5,opt,name=completion_rate,json=completionRate,proto3" json:"completion_rate,omitempty"` // 完成度百分比
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PokebookStats) Reset() {
	*x = PokebookStats{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokebookStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokebookStats) ProtoMessage() {}

func (x *PokebookStats) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokebookStats.ProtoReflect.Descriptor instead.
func (*PokebookStats) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{1}
}

func (x *PokebookStats) GetTotalPokemon() int32 {
	if x != nil {
		return x.TotalPokemon
	}
	return 0
}

func (x *PokebookStats) GetSeenCount() int32 {
	if x != nil {
		return x.SeenCount
	}
	return 0
}

func (x *PokebookStats) GetCaughtCount() int32 {
	if x != nil {
		return x.CaughtCount
	}
	return 0
}

func (x *PokebookStats) GetShinyCount() int32 {
	if x != nil {
		return x.ShinyCount
	}
	return 0
}

func (x *PokebookStats) GetCompletionRate() float32 {
	if x != nil {
		return x.CompletionRate
	}
	return 0
}

// 精灵状态更新
type PokemonStatusUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokemonId     int32                  `protobuf:"varint,1,opt,name=pokemon_id,json=pokemonId,proto3" json:"pokemon_id,omitempty"`        // 精灵ID
	Status        PokedexStatus          `protobuf:"varint,2,opt,name=status,proto3,enum=MainServer.PokedexStatus" json:"status,omitempty"` // 新状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokemonStatusUpdate) Reset() {
	*x = PokemonStatusUpdate{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokemonStatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokemonStatusUpdate) ProtoMessage() {}

func (x *PokemonStatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokemonStatusUpdate.ProtoReflect.Descriptor instead.
func (*PokemonStatusUpdate) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{2}
}

func (x *PokemonStatusUpdate) GetPokemonId() int32 {
	if x != nil {
		return x.PokemonId
	}
	return 0
}

func (x *PokemonStatusUpdate) GetStatus() PokedexStatus {
	if x != nil {
		return x.Status
	}
	return PokedexStatus_PokedexStatus_NotSeen
}

// 设置精灵状态请求
type RpcSetPokemonStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokemonId     int32                  `protobuf:"varint,1,opt,name=pokemon_id,json=pokemonId,proto3" json:"pokemon_id,omitempty"`        // 精灵ID
	Status        PokedexStatus          `protobuf:"varint,2,opt,name=status,proto3,enum=MainServer.PokedexStatus" json:"status,omitempty"` // 状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcSetPokemonStatusRequest) Reset() {
	*x = RpcSetPokemonStatusRequest{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcSetPokemonStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcSetPokemonStatusRequest) ProtoMessage() {}

func (x *RpcSetPokemonStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcSetPokemonStatusRequest.ProtoReflect.Descriptor instead.
func (*RpcSetPokemonStatusRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{3}
}

func (x *RpcSetPokemonStatusRequest) GetPokemonId() int32 {
	if x != nil {
		return x.PokemonId
	}
	return 0
}

func (x *RpcSetPokemonStatusRequest) GetStatus() PokedexStatus {
	if x != nil {
		return x.Status
	}
	return PokedexStatus_PokedexStatus_NotSeen
}

// 设置精灵状态响应
type RpcSetPokemonStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcSetPokemonStatusResponse) Reset() {
	*x = RpcSetPokemonStatusResponse{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcSetPokemonStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcSetPokemonStatusResponse) ProtoMessage() {}

func (x *RpcSetPokemonStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcSetPokemonStatusResponse.ProtoReflect.Descriptor instead.
func (*RpcSetPokemonStatusResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{4}
}

func (x *RpcSetPokemonStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcSetPokemonStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 批量设置精灵状态请求
type RpcBatchSetPokemonStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Updates       []*PokemonStatusUpdate `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"` // 状态更新列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcBatchSetPokemonStatusRequest) Reset() {
	*x = RpcBatchSetPokemonStatusRequest{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcBatchSetPokemonStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcBatchSetPokemonStatusRequest) ProtoMessage() {}

func (x *RpcBatchSetPokemonStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcBatchSetPokemonStatusRequest.ProtoReflect.Descriptor instead.
func (*RpcBatchSetPokemonStatusRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{5}
}

func (x *RpcBatchSetPokemonStatusRequest) GetUpdates() []*PokemonStatusUpdate {
	if x != nil {
		return x.Updates
	}
	return nil
}

// 批量设置精灵状态响应
type RpcBatchSetPokemonStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                            // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                             // 消息
	UpdateCount   int32                  `protobuf:"varint,3,opt,name=update_count,json=updateCount,proto3" json:"update_count,omitempty"` // 更新数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcBatchSetPokemonStatusResponse) Reset() {
	*x = RpcBatchSetPokemonStatusResponse{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcBatchSetPokemonStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcBatchSetPokemonStatusResponse) ProtoMessage() {}

func (x *RpcBatchSetPokemonStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcBatchSetPokemonStatusResponse.ProtoReflect.Descriptor instead.
func (*RpcBatchSetPokemonStatusResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{6}
}

func (x *RpcBatchSetPokemonStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcBatchSetPokemonStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcBatchSetPokemonStatusResponse) GetUpdateCount() int32 {
	if x != nil {
		return x.UpdateCount
	}
	return 0
}

// 扩展图鉴容量请求
type RpcExtendPokebookCapacityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NewTotalCount int32                  `protobuf:"varint,1,opt,name=new_total_count,json=newTotalCount,proto3" json:"new_total_count,omitempty"` // 新的总精灵数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcExtendPokebookCapacityRequest) Reset() {
	*x = RpcExtendPokebookCapacityRequest{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcExtendPokebookCapacityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcExtendPokebookCapacityRequest) ProtoMessage() {}

func (x *RpcExtendPokebookCapacityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcExtendPokebookCapacityRequest.ProtoReflect.Descriptor instead.
func (*RpcExtendPokebookCapacityRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{7}
}

func (x *RpcExtendPokebookCapacityRequest) GetNewTotalCount() int32 {
	if x != nil {
		return x.NewTotalCount
	}
	return 0
}

// 扩展图鉴容量响应
type RpcExtendPokebookCapacityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                                    // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                                     // 消息
	NewTotalCount int32                  `protobuf:"varint,3,opt,name=new_total_count,json=newTotalCount,proto3" json:"new_total_count,omitempty"` // 新的总精灵数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcExtendPokebookCapacityResponse) Reset() {
	*x = RpcExtendPokebookCapacityResponse{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcExtendPokebookCapacityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcExtendPokebookCapacityResponse) ProtoMessage() {}

func (x *RpcExtendPokebookCapacityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcExtendPokebookCapacityResponse.ProtoReflect.Descriptor instead.
func (*RpcExtendPokebookCapacityResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{8}
}

func (x *RpcExtendPokebookCapacityResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcExtendPokebookCapacityResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcExtendPokebookCapacityResponse) GetNewTotalCount() int32 {
	if x != nil {
		return x.NewTotalCount
	}
	return 0
}

// 查询特定状态精灵请求
type RpcGetPokemonByStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        PokedexStatus          `protobuf:"varint,1,opt,name=status,proto3,enum=MainServer.PokedexStatus" json:"status,omitempty"` // 要查询的状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetPokemonByStatusRequest) Reset() {
	*x = RpcGetPokemonByStatusRequest{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetPokemonByStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetPokemonByStatusRequest) ProtoMessage() {}

func (x *RpcGetPokemonByStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetPokemonByStatusRequest.ProtoReflect.Descriptor instead.
func (*RpcGetPokemonByStatusRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{9}
}

func (x *RpcGetPokemonByStatusRequest) GetStatus() PokedexStatus {
	if x != nil {
		return x.Status
	}
	return PokedexStatus_PokedexStatus_NotSeen
}

// 查询特定状态精灵响应
type RpcGetPokemonByStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokemonIds    []int32                `protobuf:"varint,1,rep,packed,name=pokemon_ids,json=pokemonIds,proto3" json:"pokemon_ids,omitempty"` // 精灵ID列表
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                    // 数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetPokemonByStatusResponse) Reset() {
	*x = RpcGetPokemonByStatusResponse{}
	mi := &file_MainServer_PokeBook_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetPokemonByStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetPokemonByStatusResponse) ProtoMessage() {}

func (x *RpcGetPokemonByStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBook_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetPokemonByStatusResponse.ProtoReflect.Descriptor instead.
func (*RpcGetPokemonByStatusResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBook_proto_rawDescGZIP(), []int{10}
}

func (x *RpcGetPokemonByStatusResponse) GetPokemonIds() []int32 {
	if x != nil {
		return x.PokemonIds
	}
	return nil
}

func (x *RpcGetPokemonByStatusResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_MainServer_PokeBook_proto protoreflect.FileDescriptor

const file_MainServer_PokeBook_proto_rawDesc = "" +
	"\n" +
	"\x19MainServer/PokeBook.proto\x12\n" +
	"MainServer\"\xc0\x01\n" +
	"\x0fTrainerPokebook\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12!\n" +
	"\fpokedex_data\x18\x03 \x01(\fR\vpokedexData\x12.\n" +
	"\x13total_pokemon_count\x18\x04 \x01(\x05R\x11totalPokemonCount\x12\x1b\n" +
	"\tcreate_ts\x18\x05 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x06 \x01(\x03R\bupdateTs\"\xc0\x01\n" +
	"\rPokebookStats\x12#\n" +
	"\rtotal_pokemon\x18\x01 \x01(\x05R\ftotalPokemon\x12\x1d\n" +
	"\n" +
	"seen_count\x18\x02 \x01(\x05R\tseenCount\x12!\n" +
	"\fcaught_count\x18\x03 \x01(\x05R\vcaughtCount\x12\x1f\n" +
	"\vshiny_count\x18\x04 \x01(\x05R\n" +
	"shinyCount\x12'\n" +
	"\x0fcompletion_rate\x18\x05 \x01(\x02R\x0ecompletionRate\"g\n" +
	"\x13PokemonStatusUpdate\x12\x1d\n" +
	"\n" +
	"pokemon_id\x18\x01 \x01(\x05R\tpokemonId\x121\n" +
	"\x06status\x18\x02 \x01(\x0e2\x19.MainServer.PokedexStatusR\x06status\"n\n" +
	"\x1aRpcSetPokemonStatusRequest\x12\x1d\n" +
	"\n" +
	"pokemon_id\x18\x01 \x01(\x05R\tpokemonId\x121\n" +
	"\x06status\x18\x02 \x01(\x0e2\x19.MainServer.PokedexStatusR\x06status\"Q\n" +
	"\x1bRpcSetPokemonStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\\\n" +
	"\x1fRpcBatchSetPokemonStatusRequest\x129\n" +
	"\aupdates\x18\x01 \x03(\v2\x1f.MainServer.PokemonStatusUpdateR\aupdates\"y\n" +
	" RpcBatchSetPokemonStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\fupdate_count\x18\x03 \x01(\x05R\vupdateCount\"J\n" +
	" RpcExtendPokebookCapacityRequest\x12&\n" +
	"\x0fnew_total_count\x18\x01 \x01(\x05R\rnewTotalCount\"\x7f\n" +
	"!RpcExtendPokebookCapacityResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x0fnew_total_count\x18\x03 \x01(\x05R\rnewTotalCount\"Q\n" +
	"\x1cRpcGetPokemonByStatusRequest\x121\n" +
	"\x06status\x18\x01 \x01(\x0e2\x19.MainServer.PokedexStatusR\x06status\"V\n" +
	"\x1dRpcGetPokemonByStatusResponse\x12\x1f\n" +
	"\vpokemon_ids\x18\x01 \x03(\x05R\n" +
	"pokemonIds\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count*{\n" +
	"\rPokedexStatus\x12\x19\n" +
	"\x15PokedexStatus_NotSeen\x10\x00\x12\x16\n" +
	"\x12PokedexStatus_Seen\x10\x01\x12\x18\n" +
	"\x14PokedexStatus_Caught\x10\x02\x12\x1d\n" +
	"\x19PokedexStatus_CaughtShiny\x10\x03B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeBook_proto_rawDescOnce sync.Once
	file_MainServer_PokeBook_proto_rawDescData []byte
)

func file_MainServer_PokeBook_proto_rawDescGZIP() []byte {
	file_MainServer_PokeBook_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeBook_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeBook_proto_rawDesc), len(file_MainServer_PokeBook_proto_rawDesc)))
	})
	return file_MainServer_PokeBook_proto_rawDescData
}

var file_MainServer_PokeBook_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_PokeBook_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_MainServer_PokeBook_proto_goTypes = []any{
	(PokedexStatus)(0),                        // 0: MainServer.PokedexStatus
	(*TrainerPokebook)(nil),                   // 1: MainServer.TrainerPokebook
	(*PokebookStats)(nil),                     // 2: MainServer.PokebookStats
	(*PokemonStatusUpdate)(nil),               // 3: MainServer.PokemonStatusUpdate
	(*RpcSetPokemonStatusRequest)(nil),        // 4: MainServer.RpcSetPokemonStatusRequest
	(*RpcSetPokemonStatusResponse)(nil),       // 5: MainServer.RpcSetPokemonStatusResponse
	(*RpcBatchSetPokemonStatusRequest)(nil),   // 6: MainServer.RpcBatchSetPokemonStatusRequest
	(*RpcBatchSetPokemonStatusResponse)(nil),  // 7: MainServer.RpcBatchSetPokemonStatusResponse
	(*RpcExtendPokebookCapacityRequest)(nil),  // 8: MainServer.RpcExtendPokebookCapacityRequest
	(*RpcExtendPokebookCapacityResponse)(nil), // 9: MainServer.RpcExtendPokebookCapacityResponse
	(*RpcGetPokemonByStatusRequest)(nil),      // 10: MainServer.RpcGetPokemonByStatusRequest
	(*RpcGetPokemonByStatusResponse)(nil),     // 11: MainServer.RpcGetPokemonByStatusResponse
}
var file_MainServer_PokeBook_proto_depIdxs = []int32{
	0, // 0: MainServer.PokemonStatusUpdate.status:type_name -> MainServer.PokedexStatus
	0, // 1: MainServer.RpcSetPokemonStatusRequest.status:type_name -> MainServer.PokedexStatus
	3, // 2: MainServer.RpcBatchSetPokemonStatusRequest.updates:type_name -> MainServer.PokemonStatusUpdate
	0, // 3: MainServer.RpcGetPokemonByStatusRequest.status:type_name -> MainServer.PokedexStatus
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_PokeBook_proto_init() }
func file_MainServer_PokeBook_proto_init() {
	if File_MainServer_PokeBook_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeBook_proto_rawDesc), len(file_MainServer_PokeBook_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeBook_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeBook_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeBook_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeBook_proto_msgTypes,
	}.Build()
	File_MainServer_PokeBook_proto = out.File
	file_MainServer_PokeBook_proto_goTypes = nil
	file_MainServer_PokeBook_proto_depIdxs = nil
}
