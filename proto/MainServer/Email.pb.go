// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Email.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmainType int32

const (
	EmainType_EmainType_System  EmainType = 0
	EmainType_EmainType_Deposit EmainType = 1
)

// Enum value maps for EmainType.
var (
	EmainType_name = map[int32]string{
		0: "EmainType_System",
		1: "EmainType_Deposit",
	}
	EmainType_value = map[string]int32{
		"EmainType_System":  0,
		"EmainType_Deposit": 1,
	}
)

func (x EmainType) Enum() *EmainType {
	p := new(EmainType)
	*p = x
	return p
}

func (x EmainType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmainType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[0].Descriptor()
}

func (EmainType) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[0]
}

func (x EmainType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmainType.Descriptor instead.
func (EmainType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{0}
}

type EmailStatus int32

const (
	EmailStatus_EMAIL_STATUS_UNREAD              EmailStatus = 0
	EmailStatus_EMAIL_STATUS_READ                EmailStatus = 1
	EmailStatus_EMAIL_STATUS_DELETED             EmailStatus = 2
	EmailStatus_EMAIL_STATUS_RECEIVED_ATTACHMENT EmailStatus = 3
)

// Enum value maps for EmailStatus.
var (
	EmailStatus_name = map[int32]string{
		0: "EMAIL_STATUS_UNREAD",
		1: "EMAIL_STATUS_READ",
		2: "EMAIL_STATUS_DELETED",
		3: "EMAIL_STATUS_RECEIVED_ATTACHMENT",
	}
	EmailStatus_value = map[string]int32{
		"EMAIL_STATUS_UNREAD":              0,
		"EMAIL_STATUS_READ":                1,
		"EMAIL_STATUS_DELETED":             2,
		"EMAIL_STATUS_RECEIVED_ATTACHMENT": 3,
	}
)

func (x EmailStatus) Enum() *EmailStatus {
	p := new(EmailStatus)
	*p = x
	return p
}

func (x EmailStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[1].Descriptor()
}

func (EmailStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[1]
}

func (x EmailStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailStatus.Descriptor instead.
func (EmailStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{1}
}

type EmailLanguageKey int32

const (
	EmailLanguageKey_EmailLanguageKey_EN EmailLanguageKey = 0
	EmailLanguageKey_EmailLanguageKey_ZH EmailLanguageKey = 1
	EmailLanguageKey_EmailLanguageKey_JP EmailLanguageKey = 2
)

// Enum value maps for EmailLanguageKey.
var (
	EmailLanguageKey_name = map[int32]string{
		0: "EmailLanguageKey_EN",
		1: "EmailLanguageKey_ZH",
		2: "EmailLanguageKey_JP",
	}
	EmailLanguageKey_value = map[string]int32{
		"EmailLanguageKey_EN": 0,
		"EmailLanguageKey_ZH": 1,
		"EmailLanguageKey_JP": 2,
	}
)

func (x EmailLanguageKey) Enum() *EmailLanguageKey {
	p := new(EmailLanguageKey)
	*p = x
	return p
}

func (x EmailLanguageKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailLanguageKey) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[2].Descriptor()
}

func (EmailLanguageKey) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[2]
}

func (x EmailLanguageKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailLanguageKey.Descriptor instead.
func (EmailLanguageKey) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{2}
}

type EmailOp int32

const (
	EmailOp_EMAIL_OP_READ               EmailOp = 0
	EmailOp_EMAIL_OP_DELETE             EmailOp = 1
	EmailOp_EMAIL_OP_RECEIVE_ATTACHMENT EmailOp = 2
)

// Enum value maps for EmailOp.
var (
	EmailOp_name = map[int32]string{
		0: "EMAIL_OP_READ",
		1: "EMAIL_OP_DELETE",
		2: "EMAIL_OP_RECEIVE_ATTACHMENT",
	}
	EmailOp_value = map[string]int32{
		"EMAIL_OP_READ":               0,
		"EMAIL_OP_DELETE":             1,
		"EMAIL_OP_RECEIVE_ATTACHMENT": 2,
	}
)

func (x EmailOp) Enum() *EmailOp {
	p := new(EmailOp)
	*p = x
	return p
}

func (x EmailOp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailOp) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[3].Descriptor()
}

func (EmailOp) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[3]
}

func (x EmailOp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailOp.Descriptor instead.
func (EmailOp) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{3}
}

type EmailInfo struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SenderId   int64                  `protobuf:"varint,2,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	ReceiverId int64                  `protobuf:"varint,3,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	// repeated EmailLanguage contents = 4;
	Contents      map[string]*EmailLanguage `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	SendTime      int64                     `protobuf:"varint,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ReadTime      int64                     `protobuf:"varint,6,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
	Status        EmailStatus               `protobuf:"varint,7,opt,name=status,proto3,enum=MainServer.EmailStatus" json:"status,omitempty"`
	Attachments   *EmailAttachment          `protobuf:"bytes,8,opt,name=attachments,proto3" json:"attachments,omitempty"`
	EmailType     EmainType                 `protobuf:"varint,9,opt,name=email_type,json=emailType,proto3,enum=MainServer.EmainType" json:"email_type,omitempty"`
	Extra         *EmailExtra               `protobuf:"bytes,10,opt,name=extra,proto3" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailInfo) Reset() {
	*x = EmailInfo{}
	mi := &file_MainServer_Email_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailInfo) ProtoMessage() {}

func (x *EmailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailInfo.ProtoReflect.Descriptor instead.
func (*EmailInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{0}
}

func (x *EmailInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EmailInfo) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *EmailInfo) GetReceiverId() int64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *EmailInfo) GetContents() map[string]*EmailLanguage {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *EmailInfo) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *EmailInfo) GetReadTime() int64 {
	if x != nil {
		return x.ReadTime
	}
	return 0
}

func (x *EmailInfo) GetStatus() EmailStatus {
	if x != nil {
		return x.Status
	}
	return EmailStatus_EMAIL_STATUS_UNREAD
}

func (x *EmailInfo) GetAttachments() *EmailAttachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *EmailInfo) GetEmailType() EmainType {
	if x != nil {
		return x.EmailType
	}
	return EmainType_EmainType_System
}

func (x *EmailInfo) GetExtra() *EmailExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

type EmailExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailExtra) Reset() {
	*x = EmailExtra{}
	mi := &file_MainServer_Email_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailExtra) ProtoMessage() {}

func (x *EmailExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailExtra.ProtoReflect.Descriptor instead.
func (*EmailExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{1}
}

type EmailLanguage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Language      EmailLanguageKey       `protobuf:"varint,3,opt,name=language,proto3,enum=MainServer.EmailLanguageKey" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailLanguage) Reset() {
	*x = EmailLanguage{}
	mi := &file_MainServer_Email_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailLanguage) ProtoMessage() {}

func (x *EmailLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailLanguage.ProtoReflect.Descriptor instead.
func (*EmailLanguage) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{2}
}

func (x *EmailLanguage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EmailLanguage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EmailLanguage) GetLanguage() EmailLanguageKey {
	if x != nil {
		return x.Language
	}
	return EmailLanguageKey_EmailLanguageKey_EN
}

type EmailAttachment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pokemons      []*Poke                `protobuf:"bytes,1,rep,name=pokemons,proto3" json:"pokemons,omitempty"`
	Items         []*AddItemParam        `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`           // 道具 参数定义在Inventory
	Clothes       []*TrainerCloth        `protobuf:"bytes,3,rep,name=clothes,proto3" json:"clothes,omitempty"`       //服饰
	Equipments    []*Equipment           `protobuf:"bytes,4,rep,name=equipments,proto3" json:"equipments,omitempty"` //装备
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailAttachment) Reset() {
	*x = EmailAttachment{}
	mi := &file_MainServer_Email_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailAttachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailAttachment) ProtoMessage() {}

func (x *EmailAttachment) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailAttachment.ProtoReflect.Descriptor instead.
func (*EmailAttachment) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{3}
}

func (x *EmailAttachment) GetPokemons() []*Poke {
	if x != nil {
		return x.Pokemons
	}
	return nil
}

func (x *EmailAttachment) GetItems() []*AddItemParam {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *EmailAttachment) GetClothes() []*TrainerCloth {
	if x != nil {
		return x.Clothes
	}
	return nil
}

func (x *EmailAttachment) GetEquipments() []*Equipment {
	if x != nil {
		return x.Equipments
	}
	return nil
}

type EmailOpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EmailIds      []int64                `protobuf:"varint,1,rep,packed,name=email_ids,json=emailIds,proto3" json:"email_ids,omitempty"`
	Op            EmailOp                `protobuf:"varint,2,opt,name=op,proto3,enum=MainServer.EmailOp" json:"op,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailOpRequest) Reset() {
	*x = EmailOpRequest{}
	mi := &file_MainServer_Email_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailOpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailOpRequest) ProtoMessage() {}

func (x *EmailOpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailOpRequest.ProtoReflect.Descriptor instead.
func (*EmailOpRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{4}
}

func (x *EmailOpRequest) GetEmailIds() []int64 {
	if x != nil {
		return x.EmailIds
	}
	return nil
}

func (x *EmailOpRequest) GetOp() EmailOp {
	if x != nil {
		return x.Op
	}
	return EmailOp_EMAIL_OP_READ
}

type EmailListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emails        []*EmailInfo           `protobuf:"bytes,1,rep,name=emails,proto3" json:"emails,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailListResponse) Reset() {
	*x = EmailListResponse{}
	mi := &file_MainServer_Email_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailListResponse) ProtoMessage() {}

func (x *EmailListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailListResponse.ProtoReflect.Descriptor instead.
func (*EmailListResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{5}
}

func (x *EmailListResponse) GetEmails() []*EmailInfo {
	if x != nil {
		return x.Emails
	}
	return nil
}

// 邮件列表请求
type EmailListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                                    // 页码
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`            // 每页数量
	IsFilterAll   bool                   `protobuf:"varint,3,opt,name=is_filter_all,json=isFilterAll,proto3" json:"is_filter_all,omitempty"` // 是否过滤全部
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailListRequest) Reset() {
	*x = EmailListRequest{}
	mi := &file_MainServer_Email_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailListRequest) ProtoMessage() {}

func (x *EmailListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailListRequest.ProtoReflect.Descriptor instead.
func (*EmailListRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{6}
}

func (x *EmailListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *EmailListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *EmailListRequest) GetIsFilterAll() bool {
	if x != nil {
		return x.IsFilterAll
	}
	return false
}

// 邮件通知
type EmailNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         *EmailInfo             `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"` // 邮件信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailNotification) Reset() {
	*x = EmailNotification{}
	mi := &file_MainServer_Email_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailNotification) ProtoMessage() {}

func (x *EmailNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailNotification.ProtoReflect.Descriptor instead.
func (*EmailNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{7}
}

func (x *EmailNotification) GetEmail() *EmailInfo {
	if x != nil {
		return x.Email
	}
	return nil
}

var File_MainServer_Email_proto protoreflect.FileDescriptor

const file_MainServer_Email_proto_rawDesc = "" +
	"\n" +
	"\x16MainServer/Email.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\x1a\x1aMainServer/Inventory.proto\x1a\x1dMainServer/TrainerCloth.proto\x1a\x1aMainServer/Equipment.proto\"\x80\x04\n" +
	"\tEmailInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tsender_id\x18\x02 \x01(\x03R\bsenderId\x12\x1f\n" +
	"\vreceiver_id\x18\x03 \x01(\x03R\n" +
	"receiverId\x12?\n" +
	"\bcontents\x18\x04 \x03(\v2#.MainServer.EmailInfo.ContentsEntryR\bcontents\x12\x1b\n" +
	"\tsend_time\x18\x05 \x01(\x03R\bsendTime\x12\x1b\n" +
	"\tread_time\x18\x06 \x01(\x03R\breadTime\x12/\n" +
	"\x06status\x18\a \x01(\x0e2\x17.MainServer.EmailStatusR\x06status\x12=\n" +
	"\vattachments\x18\b \x01(\v2\x1b.MainServer.EmailAttachmentR\vattachments\x124\n" +
	"\n" +
	"email_type\x18\t \x01(\x0e2\x15.MainServer.EmainTypeR\temailType\x12,\n" +
	"\x05extra\x18\n" +
	" \x01(\v2\x16.MainServer.EmailExtraR\x05extra\x1aV\n" +
	"\rContentsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12/\n" +
	"\x05value\x18\x02 \x01(\v2\x19.MainServer.EmailLanguageR\x05value:\x028\x01\"\f\n" +
	"\n" +
	"EmailExtra\"y\n" +
	"\rEmailLanguage\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x128\n" +
	"\blanguage\x18\x03 \x01(\x0e2\x1c.MainServer.EmailLanguageKeyR\blanguage\"\xda\x01\n" +
	"\x0fEmailAttachment\x12,\n" +
	"\bpokemons\x18\x01 \x03(\v2\x10.MainServer.PokeR\bpokemons\x12.\n" +
	"\x05items\x18\x02 \x03(\v2\x18.MainServer.AddItemParamR\x05items\x122\n" +
	"\aclothes\x18\x03 \x03(\v2\x18.MainServer.TrainerClothR\aclothes\x125\n" +
	"\n" +
	"equipments\x18\x04 \x03(\v2\x15.MainServer.EquipmentR\n" +
	"equipments\"R\n" +
	"\x0eEmailOpRequest\x12\x1b\n" +
	"\temail_ids\x18\x01 \x03(\x03R\bemailIds\x12#\n" +
	"\x02op\x18\x02 \x01(\x0e2\x13.MainServer.EmailOpR\x02op\"B\n" +
	"\x11EmailListResponse\x12-\n" +
	"\x06emails\x18\x01 \x03(\v2\x15.MainServer.EmailInfoR\x06emails\"g\n" +
	"\x10EmailListRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\"\n" +
	"\ris_filter_all\x18\x03 \x01(\bR\visFilterAll\"@\n" +
	"\x11EmailNotification\x12+\n" +
	"\x05email\x18\x01 \x01(\v2\x15.MainServer.EmailInfoR\x05email*8\n" +
	"\tEmainType\x12\x14\n" +
	"\x10EmainType_System\x10\x00\x12\x15\n" +
	"\x11EmainType_Deposit\x10\x01*}\n" +
	"\vEmailStatus\x12\x17\n" +
	"\x13EMAIL_STATUS_UNREAD\x10\x00\x12\x15\n" +
	"\x11EMAIL_STATUS_READ\x10\x01\x12\x18\n" +
	"\x14EMAIL_STATUS_DELETED\x10\x02\x12$\n" +
	" EMAIL_STATUS_RECEIVED_ATTACHMENT\x10\x03*]\n" +
	"\x10EmailLanguageKey\x12\x17\n" +
	"\x13EmailLanguageKey_EN\x10\x00\x12\x17\n" +
	"\x13EmailLanguageKey_ZH\x10\x01\x12\x17\n" +
	"\x13EmailLanguageKey_JP\x10\x02*R\n" +
	"\aEmailOp\x12\x11\n" +
	"\rEMAIL_OP_READ\x10\x00\x12\x13\n" +
	"\x0fEMAIL_OP_DELETE\x10\x01\x12\x1f\n" +
	"\x1bEMAIL_OP_RECEIVE_ATTACHMENT\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Email_proto_rawDescOnce sync.Once
	file_MainServer_Email_proto_rawDescData []byte
)

func file_MainServer_Email_proto_rawDescGZIP() []byte {
	file_MainServer_Email_proto_rawDescOnce.Do(func() {
		file_MainServer_Email_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Email_proto_rawDesc), len(file_MainServer_Email_proto_rawDesc)))
	})
	return file_MainServer_Email_proto_rawDescData
}

var file_MainServer_Email_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_MainServer_Email_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_MainServer_Email_proto_goTypes = []any{
	(EmainType)(0),            // 0: MainServer.EmainType
	(EmailStatus)(0),          // 1: MainServer.EmailStatus
	(EmailLanguageKey)(0),     // 2: MainServer.EmailLanguageKey
	(EmailOp)(0),              // 3: MainServer.EmailOp
	(*EmailInfo)(nil),         // 4: MainServer.EmailInfo
	(*EmailExtra)(nil),        // 5: MainServer.EmailExtra
	(*EmailLanguage)(nil),     // 6: MainServer.EmailLanguage
	(*EmailAttachment)(nil),   // 7: MainServer.EmailAttachment
	(*EmailOpRequest)(nil),    // 8: MainServer.EmailOpRequest
	(*EmailListResponse)(nil), // 9: MainServer.EmailListResponse
	(*EmailListRequest)(nil),  // 10: MainServer.EmailListRequest
	(*EmailNotification)(nil), // 11: MainServer.EmailNotification
	nil,                       // 12: MainServer.EmailInfo.ContentsEntry
	(*Poke)(nil),              // 13: MainServer.Poke
	(*AddItemParam)(nil),      // 14: MainServer.AddItemParam
	(*TrainerCloth)(nil),      // 15: MainServer.TrainerCloth
	(*Equipment)(nil),         // 16: MainServer.Equipment
}
var file_MainServer_Email_proto_depIdxs = []int32{
	12, // 0: MainServer.EmailInfo.contents:type_name -> MainServer.EmailInfo.ContentsEntry
	1,  // 1: MainServer.EmailInfo.status:type_name -> MainServer.EmailStatus
	7,  // 2: MainServer.EmailInfo.attachments:type_name -> MainServer.EmailAttachment
	0,  // 3: MainServer.EmailInfo.email_type:type_name -> MainServer.EmainType
	5,  // 4: MainServer.EmailInfo.extra:type_name -> MainServer.EmailExtra
	2,  // 5: MainServer.EmailLanguage.language:type_name -> MainServer.EmailLanguageKey
	13, // 6: MainServer.EmailAttachment.pokemons:type_name -> MainServer.Poke
	14, // 7: MainServer.EmailAttachment.items:type_name -> MainServer.AddItemParam
	15, // 8: MainServer.EmailAttachment.clothes:type_name -> MainServer.TrainerCloth
	16, // 9: MainServer.EmailAttachment.equipments:type_name -> MainServer.Equipment
	3,  // 10: MainServer.EmailOpRequest.op:type_name -> MainServer.EmailOp
	4,  // 11: MainServer.EmailListResponse.emails:type_name -> MainServer.EmailInfo
	4,  // 12: MainServer.EmailNotification.email:type_name -> MainServer.EmailInfo
	6,  // 13: MainServer.EmailInfo.ContentsEntry.value:type_name -> MainServer.EmailLanguage
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_MainServer_Email_proto_init() }
func file_MainServer_Email_proto_init() {
	if File_MainServer_Email_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	file_MainServer_Equipment_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Email_proto_rawDesc), len(file_MainServer_Email_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Email_proto_goTypes,
		DependencyIndexes: file_MainServer_Email_proto_depIdxs,
		EnumInfos:         file_MainServer_Email_proto_enumTypes,
		MessageInfos:      file_MainServer_Email_proto_msgTypes,
	}.Build()
	File_MainServer_Email_proto = out.File
	file_MainServer_Email_proto_goTypes = nil
	file_MainServer_Email_proto_depIdxs = nil
}
