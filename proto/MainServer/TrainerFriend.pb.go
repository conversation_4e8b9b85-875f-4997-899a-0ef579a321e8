// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerFriend.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerFriend struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// string Username { get; }
	DisplaName    string `protobuf:"bytes,1,opt,name=displa_name,json=displaName,proto3" json:"displa_name,omitempty"`
	Username      string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	AvatarUrl     string `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Id            string `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	Online        bool   `protobuf:"varint,5,opt,name=online,proto3" json:"online,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerFriend) Reset() {
	*x = TrainerFriend{}
	mi := &file_MainServer_TrainerFriend_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerFriend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerFriend) ProtoMessage() {}

func (x *TrainerFriend) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerFriend_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerFriend.ProtoReflect.Descriptor instead.
func (*TrainerFriend) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerFriend_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerFriend) GetDisplaName() string {
	if x != nil {
		return x.DisplaName
	}
	return ""
}

func (x *TrainerFriend) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TrainerFriend) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *TrainerFriend) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TrainerFriend) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

var File_MainServer_TrainerFriend_proto protoreflect.FileDescriptor

const file_MainServer_TrainerFriend_proto_rawDesc = "" +
	"\n" +
	"\x1eMainServer/TrainerFriend.proto\x12\n" +
	"MainServer\"\x93\x01\n" +
	"\rTrainerFriend\x12\x1f\n" +
	"\vdispla_name\x18\x01 \x01(\tR\n" +
	"displaName\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x03 \x01(\tR\tavatarUrl\x12\x0e\n" +
	"\x02id\x18\x04 \x01(\tR\x02id\x12\x16\n" +
	"\x06online\x18\x05 \x01(\bR\x06onlineB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerFriend_proto_rawDescOnce sync.Once
	file_MainServer_TrainerFriend_proto_rawDescData []byte
)

func file_MainServer_TrainerFriend_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerFriend_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerFriend_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerFriend_proto_rawDesc), len(file_MainServer_TrainerFriend_proto_rawDesc)))
	})
	return file_MainServer_TrainerFriend_proto_rawDescData
}

var file_MainServer_TrainerFriend_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_TrainerFriend_proto_goTypes = []any{
	(*TrainerFriend)(nil), // 0: MainServer.TrainerFriend
}
var file_MainServer_TrainerFriend_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerFriend_proto_init() }
func file_MainServer_TrainerFriend_proto_init() {
	if File_MainServer_TrainerFriend_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerFriend_proto_rawDesc), len(file_MainServer_TrainerFriend_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerFriend_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerFriend_proto_depIdxs,
		MessageInfos:      file_MainServer_TrainerFriend_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerFriend_proto = out.File
	file_MainServer_TrainerFriend_proto_goTypes = nil
	file_MainServer_TrainerFriend_proto_depIdxs = nil
}
