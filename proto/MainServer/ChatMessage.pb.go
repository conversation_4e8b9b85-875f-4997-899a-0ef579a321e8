// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ChatMessage.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 枚举：消息类型
type ChatMessageType int32

const (
	ChatMessageType_TEXT  ChatMessageType = 0
	ChatMessageType_IMAGE ChatMessageType = 1
	ChatMessageType_VIDEO ChatMessageType = 2
)

// Enum value maps for ChatMessageType.
var (
	ChatMessageType_name = map[int32]string{
		0: "TEXT",
		1: "IMAGE",
		2: "VIDEO",
	}
	ChatMessageType_value = map[string]int32{
		"TEXT":  0,
		"IMAGE": 1,
		"VIDEO": 2,
	}
)

func (x ChatMessageType) Enum() *ChatMessageType {
	p := new(ChatMessageType)
	*p = x
	return p
}

func (x ChatMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ChatMessage_proto_enumTypes[0].Descriptor()
}

func (ChatMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_ChatMessage_proto_enumTypes[0]
}

func (x ChatMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatMessageType.Descriptor instead.
func (ChatMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

// 消息作者定义
type ChatMessageAuthor struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon          string                 `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Tid           int64                  `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"` // 注意：isSelf 是派生字段，不应在 protobuf 中定义，因为它是运行时计算得出的。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageAuthor) Reset() {
	*x = ChatMessageAuthor{}
	mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageAuthor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageAuthor) ProtoMessage() {}

func (x *ChatMessageAuthor) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageAuthor.ProtoReflect.Descriptor instead.
func (*ChatMessageAuthor) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

func (x *ChatMessageAuthor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatMessageAuthor) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ChatMessageAuthor) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

// 聊天消息定义
type ChatMessageModel struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Text          string                  `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Time          string                  `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	TimeTs        int64                   `protobuf:"varint,3,opt,name=timeTs,proto3" json:"timeTs,omitempty"`
	Type          ChatMessageType         `protobuf:"varint,4,opt,name=type,proto3,enum=MainServer.ChatMessageType" json:"type,omitempty"` // 默认值为 TEXT，可在服务端/客户端处理
	Author        *ChatMessageAuthor      `protobuf:"bytes,5,opt,name=author,proto3" json:"author,omitempty"`
	ChannelInfo   *ChatMessageChannelInfo `protobuf:"bytes,6,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageModel) Reset() {
	*x = ChatMessageModel{}
	mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageModel) ProtoMessage() {}

func (x *ChatMessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageModel.ProtoReflect.Descriptor instead.
func (*ChatMessageModel) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{1}
}

func (x *ChatMessageModel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ChatMessageModel) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *ChatMessageModel) GetTimeTs() int64 {
	if x != nil {
		return x.TimeTs
	}
	return 0
}

func (x *ChatMessageModel) GetType() ChatMessageType {
	if x != nil {
		return x.Type
	}
	return ChatMessageType_TEXT
}

func (x *ChatMessageModel) GetAuthor() *ChatMessageAuthor {
	if x != nil {
		return x.Author
	}
	return nil
}

func (x *ChatMessageModel) GetChannelInfo() *ChatMessageChannelInfo {
	if x != nil {
		return x.ChannelInfo
	}
	return nil
}

type ChatMessageChannelInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IsParty       bool                   `protobuf:"varint,2,opt,name=is_party,json=isParty,proto3" json:"is_party,omitempty"`
	SenderUid     string                 `protobuf:"bytes,3,opt,name=sender_uid,json=senderUid,proto3" json:"sender_uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageChannelInfo) Reset() {
	*x = ChatMessageChannelInfo{}
	mi := &file_MainServer_ChatMessage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageChannelInfo) ProtoMessage() {}

func (x *ChatMessageChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageChannelInfo.ProtoReflect.Descriptor instead.
func (*ChatMessageChannelInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{2}
}

func (x *ChatMessageChannelInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ChatMessageChannelInfo) GetIsParty() bool {
	if x != nil {
		return x.IsParty
	}
	return false
}

func (x *ChatMessageChannelInfo) GetSenderUid() string {
	if x != nil {
		return x.SenderUid
	}
	return ""
}

var File_MainServer_ChatMessage_proto protoreflect.FileDescriptor

const file_MainServer_ChatMessage_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/ChatMessage.proto\x12\n" +
	"MainServer\"M\n" +
	"\x11ChatMessageAuthor\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04icon\x18\x02 \x01(\tR\x04icon\x12\x10\n" +
	"\x03tid\x18\x03 \x01(\x03R\x03tid\"\x81\x02\n" +
	"\x10ChatMessageModel\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x12\n" +
	"\x04time\x18\x02 \x01(\tR\x04time\x12\x16\n" +
	"\x06timeTs\x18\x03 \x01(\x03R\x06timeTs\x12/\n" +
	"\x04type\x18\x04 \x01(\x0e2\x1b.MainServer.ChatMessageTypeR\x04type\x125\n" +
	"\x06author\x18\x05 \x01(\v2\x1d.MainServer.ChatMessageAuthorR\x06author\x12E\n" +
	"\fchannel_info\x18\x06 \x01(\v2\".MainServer.ChatMessageChannelInfoR\vchannelInfo\"b\n" +
	"\x16ChatMessageChannelInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x19\n" +
	"\bis_party\x18\x02 \x01(\bR\aisParty\x12\x1d\n" +
	"\n" +
	"sender_uid\x18\x03 \x01(\tR\tsenderUid*1\n" +
	"\x0fChatMessageType\x12\b\n" +
	"\x04TEXT\x10\x00\x12\t\n" +
	"\x05IMAGE\x10\x01\x12\t\n" +
	"\x05VIDEO\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ChatMessage_proto_rawDescOnce sync.Once
	file_MainServer_ChatMessage_proto_rawDescData []byte
)

func file_MainServer_ChatMessage_proto_rawDescGZIP() []byte {
	file_MainServer_ChatMessage_proto_rawDescOnce.Do(func() {
		file_MainServer_ChatMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ChatMessage_proto_rawDesc), len(file_MainServer_ChatMessage_proto_rawDesc)))
	})
	return file_MainServer_ChatMessage_proto_rawDescData
}

var file_MainServer_ChatMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ChatMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_MainServer_ChatMessage_proto_goTypes = []any{
	(ChatMessageType)(0),           // 0: MainServer.ChatMessageType
	(*ChatMessageAuthor)(nil),      // 1: MainServer.ChatMessageAuthor
	(*ChatMessageModel)(nil),       // 2: MainServer.ChatMessageModel
	(*ChatMessageChannelInfo)(nil), // 3: MainServer.ChatMessageChannelInfo
}
var file_MainServer_ChatMessage_proto_depIdxs = []int32{
	0, // 0: MainServer.ChatMessageModel.type:type_name -> MainServer.ChatMessageType
	1, // 1: MainServer.ChatMessageModel.author:type_name -> MainServer.ChatMessageAuthor
	3, // 2: MainServer.ChatMessageModel.channel_info:type_name -> MainServer.ChatMessageChannelInfo
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_ChatMessage_proto_init() }
func file_MainServer_ChatMessage_proto_init() {
	if File_MainServer_ChatMessage_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ChatMessage_proto_rawDesc), len(file_MainServer_ChatMessage_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ChatMessage_proto_goTypes,
		DependencyIndexes: file_MainServer_ChatMessage_proto_depIdxs,
		EnumInfos:         file_MainServer_ChatMessage_proto_enumTypes,
		MessageInfos:      file_MainServer_ChatMessage_proto_msgTypes,
	}.Build()
	File_MainServer_ChatMessage_proto = out.File
	file_MainServer_ChatMessage_proto_goTypes = nil
	file_MainServer_ChatMessage_proto_depIdxs = nil
}
