// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Exchange.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExchangeItemType int32

const (
	ExchangeItemType_Exchange_Item_Type_Poke  ExchangeItemType = 0
	ExchangeItemType_Exchange_Item_Type_Item  ExchangeItemType = 1
	ExchangeItemType_Exchange_Item_Type_Equip ExchangeItemType = 2
)

// Enum value maps for ExchangeItemType.
var (
	ExchangeItemType_name = map[int32]string{
		0: "Exchange_Item_Type_Poke",
		1: "Exchange_Item_Type_Item",
		2: "Exchange_Item_Type_Equip",
	}
	ExchangeItemType_value = map[string]int32{
		"Exchange_Item_Type_Poke":  0,
		"Exchange_Item_Type_Item":  1,
		"Exchange_Item_Type_Equip": 2,
	}
)

func (x ExchangeItemType) Enum() *ExchangeItemType {
	p := new(ExchangeItemType)
	*p = x
	return p
}

func (x ExchangeItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangeItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Exchange_proto_enumTypes[0].Descriptor()
}

func (ExchangeItemType) Type() protoreflect.EnumType {
	return &file_MainServer_Exchange_proto_enumTypes[0]
}

func (x ExchangeItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangeItemType.Descriptor instead.
func (ExchangeItemType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Exchange_proto_rawDescGZIP(), []int{0}
}

type ExchangeRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  int64                  `protobuf:"varint,3,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	ItemId        int64                  `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName      string                 `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemType      ExchangeItemType       `protobuf:"varint,6,opt,name=item_type,json=itemType,proto3,enum=MainServer.ExchangeItemType" json:"item_type,omitempty"`
	Extra         *ExchangeExtra         `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeRecord) Reset() {
	*x = ExchangeRecord{}
	mi := &file_MainServer_Exchange_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRecord) ProtoMessage() {}

func (x *ExchangeRecord) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Exchange_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRecord.ProtoReflect.Descriptor instead.
func (*ExchangeRecord) Descriptor() ([]byte, []int) {
	return file_MainServer_Exchange_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangeRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExchangeRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ExchangeRecord) GetTargetUserId() int64 {
	if x != nil {
		return x.TargetUserId
	}
	return 0
}

func (x *ExchangeRecord) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *ExchangeRecord) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *ExchangeRecord) GetItemType() ExchangeItemType {
	if x != nil {
		return x.ItemType
	}
	return ExchangeItemType_Exchange_Item_Type_Poke
}

func (x *ExchangeRecord) GetExtra() *ExchangeExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *ExchangeRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExchangeRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type ExchangeExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeExtra) Reset() {
	*x = ExchangeExtra{}
	mi := &file_MainServer_Exchange_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeExtra) ProtoMessage() {}

func (x *ExchangeExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Exchange_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeExtra.ProtoReflect.Descriptor instead.
func (*ExchangeExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Exchange_proto_rawDescGZIP(), []int{1}
}

var File_MainServer_Exchange_proto protoreflect.FileDescriptor

const file_MainServer_Exchange_proto_rawDesc = "" +
	"\n" +
	"\x19MainServer/Exchange.proto\x12\n" +
	"MainServer\"\xbf\x02\n" +
	"\x0eExchangeRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x03 \x01(\x03R\ftargetUserId\x12\x17\n" +
	"\aitem_id\x18\x04 \x01(\x03R\x06itemId\x12\x1b\n" +
	"\titem_name\x18\x05 \x01(\tR\bitemName\x129\n" +
	"\titem_type\x18\x06 \x01(\x0e2\x1c.MainServer.ExchangeItemTypeR\bitemType\x12/\n" +
	"\x05extra\x18\a \x01(\v2\x19.MainServer.ExchangeExtraR\x05extra\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\t \x01(\x03R\tupdatedAt\"\x0f\n" +
	"\rExchangeExtra*j\n" +
	"\x10ExchangeItemType\x12\x1b\n" +
	"\x17Exchange_Item_Type_Poke\x10\x00\x12\x1b\n" +
	"\x17Exchange_Item_Type_Item\x10\x01\x12\x1c\n" +
	"\x18Exchange_Item_Type_Equip\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Exchange_proto_rawDescOnce sync.Once
	file_MainServer_Exchange_proto_rawDescData []byte
)

func file_MainServer_Exchange_proto_rawDescGZIP() []byte {
	file_MainServer_Exchange_proto_rawDescOnce.Do(func() {
		file_MainServer_Exchange_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Exchange_proto_rawDesc), len(file_MainServer_Exchange_proto_rawDesc)))
	})
	return file_MainServer_Exchange_proto_rawDescData
}

var file_MainServer_Exchange_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Exchange_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_Exchange_proto_goTypes = []any{
	(ExchangeItemType)(0),  // 0: MainServer.ExchangeItemType
	(*ExchangeRecord)(nil), // 1: MainServer.ExchangeRecord
	(*ExchangeExtra)(nil),  // 2: MainServer.ExchangeExtra
}
var file_MainServer_Exchange_proto_depIdxs = []int32{
	0, // 0: MainServer.ExchangeRecord.item_type:type_name -> MainServer.ExchangeItemType
	2, // 1: MainServer.ExchangeRecord.extra:type_name -> MainServer.ExchangeExtra
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_Exchange_proto_init() }
func file_MainServer_Exchange_proto_init() {
	if File_MainServer_Exchange_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Exchange_proto_rawDesc), len(file_MainServer_Exchange_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Exchange_proto_goTypes,
		DependencyIndexes: file_MainServer_Exchange_proto_depIdxs,
		EnumInfos:         file_MainServer_Exchange_proto_enumTypes,
		MessageInfos:      file_MainServer_Exchange_proto_msgTypes,
	}.Build()
	File_MainServer_Exchange_proto = out.File
	file_MainServer_Exchange_proto_goTypes = nil
	file_MainServer_Exchange_proto_depIdxs = nil
}
