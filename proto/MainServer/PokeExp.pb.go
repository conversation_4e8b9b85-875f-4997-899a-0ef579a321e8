// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeExp.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeExpInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Exp           int32                  `protobuf:"varint,1,opt,name=exp,proto3" json:"exp,omitempty"`
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	PokeId        int64                  `protobuf:"varint,3,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	MoveIds       []string               `protobuf:"bytes,4,rep,name=move_ids,json=moveIds,proto3" json:"move_ids,omitempty"`
	OldLevel      int32                  `protobuf:"varint,5,opt,name=old_level,json=oldLevel,proto3" json:"old_level,omitempty"`
	OldExp        int64                  `protobuf:"varint,6,opt,name=old_exp,json=oldExp,proto3" json:"old_exp,omitempty"`
	PokeName      string                 `protobuf:"bytes,7,opt,name=poke_name,json=pokeName,proto3" json:"poke_name,omitempty"`
	Ts            int64                  `protobuf:"varint,8,opt,name=ts,proto3" json:"ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeExpInfo) Reset() {
	*x = PokeExpInfo{}
	mi := &file_MainServer_PokeExp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeExpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeExpInfo) ProtoMessage() {}

func (x *PokeExpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeExp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeExpInfo.ProtoReflect.Descriptor instead.
func (*PokeExpInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeExp_proto_rawDescGZIP(), []int{0}
}

func (x *PokeExpInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PokeExpInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PokeExpInfo) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *PokeExpInfo) GetMoveIds() []string {
	if x != nil {
		return x.MoveIds
	}
	return nil
}

func (x *PokeExpInfo) GetOldLevel() int32 {
	if x != nil {
		return x.OldLevel
	}
	return 0
}

func (x *PokeExpInfo) GetOldExp() int64 {
	if x != nil {
		return x.OldExp
	}
	return 0
}

func (x *PokeExpInfo) GetPokeName() string {
	if x != nil {
		return x.PokeName
	}
	return ""
}

func (x *PokeExpInfo) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type PokeExpInfos struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Infos         []*PokeExpInfo         `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeExpInfos) Reset() {
	*x = PokeExpInfos{}
	mi := &file_MainServer_PokeExp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeExpInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeExpInfos) ProtoMessage() {}

func (x *PokeExpInfos) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeExp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeExpInfos.ProtoReflect.Descriptor instead.
func (*PokeExpInfos) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeExp_proto_rawDescGZIP(), []int{1}
}

func (x *PokeExpInfos) GetInfos() []*PokeExpInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

var File_MainServer_PokeExp_proto protoreflect.FileDescriptor

const file_MainServer_PokeExp_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/PokeExp.proto\x12\n" +
	"MainServer\"\xcc\x01\n" +
	"\vPokeExpInfo\x12\x10\n" +
	"\x03exp\x18\x01 \x01(\x05R\x03exp\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12\x17\n" +
	"\apoke_id\x18\x03 \x01(\x03R\x06pokeId\x12\x19\n" +
	"\bmove_ids\x18\x04 \x03(\tR\amoveIds\x12\x1b\n" +
	"\told_level\x18\x05 \x01(\x05R\boldLevel\x12\x17\n" +
	"\aold_exp\x18\x06 \x01(\x03R\x06oldExp\x12\x1b\n" +
	"\tpoke_name\x18\a \x01(\tR\bpokeName\x12\x0e\n" +
	"\x02ts\x18\b \x01(\x03R\x02ts\"=\n" +
	"\fPokeExpInfos\x12-\n" +
	"\x05infos\x18\x01 \x03(\v2\x17.MainServer.PokeExpInfoR\x05infosB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeExp_proto_rawDescOnce sync.Once
	file_MainServer_PokeExp_proto_rawDescData []byte
)

func file_MainServer_PokeExp_proto_rawDescGZIP() []byte {
	file_MainServer_PokeExp_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeExp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeExp_proto_rawDesc), len(file_MainServer_PokeExp_proto_rawDesc)))
	})
	return file_MainServer_PokeExp_proto_rawDescData
}

var file_MainServer_PokeExp_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_PokeExp_proto_goTypes = []any{
	(*PokeExpInfo)(nil),  // 0: MainServer.PokeExpInfo
	(*PokeExpInfos)(nil), // 1: MainServer.PokeExpInfos
}
var file_MainServer_PokeExp_proto_depIdxs = []int32{
	0, // 0: MainServer.PokeExpInfos.infos:type_name -> MainServer.PokeExpInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_PokeExp_proto_init() }
func file_MainServer_PokeExp_proto_init() {
	if File_MainServer_PokeExp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeExp_proto_rawDesc), len(file_MainServer_PokeExp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeExp_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeExp_proto_depIdxs,
		MessageInfos:      file_MainServer_PokeExp_proto_msgTypes,
	}.Build()
	File_MainServer_PokeExp_proto = out.File
	file_MainServer_PokeExp_proto_goTypes = nil
	file_MainServer_PokeExp_proto_depIdxs = nil
}
