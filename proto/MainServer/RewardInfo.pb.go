// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/RewardInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RewardChannelType int32

const (
	RewardChannelType_REWARD_CHANNEL_TYPE_NONE     RewardChannelType = 0 // 无
	RewardChannelType_REWARD_CHANNEL_TYPE_QUEST    RewardChannelType = 1 // 任务
	RewardChannelType_REWARD_CHANNEL_TYPE_DUNGEON  RewardChannelType = 2 // 地下城
	RewardChannelType_REWARD_CHANNEL_TYPE_BATTLE   RewardChannelType = 3 // 战斗
	RewardChannelType_REWARD_CHANNEL_TYPE_SHOP     RewardChannelType = 4 // 商店
	RewardChannelType_REWARD_CHANNEL_TYPE_OTHER    RewardChannelType = 5 // 其他
	RewardChannelType_REWARD_CHANNEL_TYPE_EXCHANGE RewardChannelType = 6 //交易行
)

// Enum value maps for RewardChannelType.
var (
	RewardChannelType_name = map[int32]string{
		0: "REWARD_CHANNEL_TYPE_NONE",
		1: "REWARD_CHANNEL_TYPE_QUEST",
		2: "REWARD_CHANNEL_TYPE_DUNGEON",
		3: "REWARD_CHANNEL_TYPE_BATTLE",
		4: "REWARD_CHANNEL_TYPE_SHOP",
		5: "REWARD_CHANNEL_TYPE_OTHER",
		6: "REWARD_CHANNEL_TYPE_EXCHANGE",
	}
	RewardChannelType_value = map[string]int32{
		"REWARD_CHANNEL_TYPE_NONE":     0,
		"REWARD_CHANNEL_TYPE_QUEST":    1,
		"REWARD_CHANNEL_TYPE_DUNGEON":  2,
		"REWARD_CHANNEL_TYPE_BATTLE":   3,
		"REWARD_CHANNEL_TYPE_SHOP":     4,
		"REWARD_CHANNEL_TYPE_OTHER":    5,
		"REWARD_CHANNEL_TYPE_EXCHANGE": 6,
	}
)

func (x RewardChannelType) Enum() *RewardChannelType {
	p := new(RewardChannelType)
	*p = x
	return p
}

func (x RewardChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_RewardInfo_proto_enumTypes[0].Descriptor()
}

func (RewardChannelType) Type() protoreflect.EnumType {
	return &file_MainServer_RewardInfo_proto_enumTypes[0]
}

func (x RewardChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardChannelType.Descriptor instead.
func (RewardChannelType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{0}
}

type RewardType int32

const (
	RewardType_RewardType_None           RewardType = 0
	RewardType_RewardType_Pokemon        RewardType = 1  //宝可梦
	RewardType_RewardType_Item           RewardType = 2  //道具
	RewardType_RewardType_Cloth          RewardType = 3  //服装
	RewardType_RewardType_Money          RewardType = 4  //金钱
	RewardType_RewardType_Exp            RewardType = 5  //经验值
	RewardType_RewardType_Star           RewardType = 6  //星星
	RewardType_RewardType_Title          RewardType = 7  //称号
	RewardType_RewardType_Achievement    RewardType = 8  //成就
	RewardType_RewardType_BattleLevel    RewardType = 9  //战斗等级
	RewardType_RewardType_Config_Pokemon RewardType = 10 //配置的宝可梦
	RewardType_RewardType_Sp_Badge       RewardType = 11 //徽章
	RewardType_RewardType_Sp_Champion    RewardType = 12 //冠军
	RewardType_RewardType_Equip          RewardType = 13 //装备
)

// Enum value maps for RewardType.
var (
	RewardType_name = map[int32]string{
		0:  "RewardType_None",
		1:  "RewardType_Pokemon",
		2:  "RewardType_Item",
		3:  "RewardType_Cloth",
		4:  "RewardType_Money",
		5:  "RewardType_Exp",
		6:  "RewardType_Star",
		7:  "RewardType_Title",
		8:  "RewardType_Achievement",
		9:  "RewardType_BattleLevel",
		10: "RewardType_Config_Pokemon",
		11: "RewardType_Sp_Badge",
		12: "RewardType_Sp_Champion",
		13: "RewardType_Equip",
	}
	RewardType_value = map[string]int32{
		"RewardType_None":           0,
		"RewardType_Pokemon":        1,
		"RewardType_Item":           2,
		"RewardType_Cloth":          3,
		"RewardType_Money":          4,
		"RewardType_Exp":            5,
		"RewardType_Star":           6,
		"RewardType_Title":          7,
		"RewardType_Achievement":    8,
		"RewardType_BattleLevel":    9,
		"RewardType_Config_Pokemon": 10,
		"RewardType_Sp_Badge":       11,
		"RewardType_Sp_Champion":    12,
		"RewardType_Equip":          13,
	}
)

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}

func (x RewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_RewardInfo_proto_enumTypes[1].Descriptor()
}

func (RewardType) Type() protoreflect.EnumType {
	return &file_MainServer_RewardInfo_proto_enumTypes[1]
}

func (x RewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardType.Descriptor instead.
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{1}
}

// import "MainServer/QuestType.proto"
// 奖励结果
type RewardResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RewardId      string                 `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	BaseMoney     int32                  `protobuf:"varint,2,opt,name=base_money,json=baseMoney,proto3" json:"base_money,omitempty"`
	Rewards       []*GeneratedReward     `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
	SpRewards     []*GeneratedReward     `protobuf:"bytes,4,rep,name=sp_rewards,json=spRewards,proto3" json:"sp_rewards,omitempty"`                                          //特殊的奖励（比如徽章之类的，特殊道具
	ChannelType   RewardChannelType      `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3,enum=MainServer.RewardChannelType" json:"channel_type,omitempty"` // 奖励渠道类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RewardResult) Reset() {
	*x = RewardResult{}
	mi := &file_MainServer_RewardInfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardResult) ProtoMessage() {}

func (x *RewardResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_RewardInfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardResult.ProtoReflect.Descriptor instead.
func (*RewardResult) Descriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{0}
}

func (x *RewardResult) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardResult) GetBaseMoney() int32 {
	if x != nil {
		return x.BaseMoney
	}
	return 0
}

func (x *RewardResult) GetRewards() []*GeneratedReward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *RewardResult) GetSpRewards() []*GeneratedReward {
	if x != nil {
		return x.SpRewards
	}
	return nil
}

func (x *RewardResult) GetChannelType() RewardChannelType {
	if x != nil {
		return x.ChannelType
	}
	return RewardChannelType_REWARD_CHANNEL_TYPE_NONE
}

// 生成的奖励
type GeneratedReward struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RewardType     RewardType             `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=MainServer.RewardType" json:"reward_type,omitempty"`
	RewardValueStr string                 `protobuf:"bytes,2,opt,name=reward_value_str,json=rewardValueStr,proto3" json:"reward_value_str,omitempty"`
	Count          int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	RewardValueInt int32                  `protobuf:"varint,4,opt,name=reward_value_int,json=rewardValueInt,proto3" json:"reward_value_int,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GeneratedReward) Reset() {
	*x = GeneratedReward{}
	mi := &file_MainServer_RewardInfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneratedReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratedReward) ProtoMessage() {}

func (x *GeneratedReward) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_RewardInfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratedReward.ProtoReflect.Descriptor instead.
func (*GeneratedReward) Descriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{1}
}

func (x *GeneratedReward) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_RewardType_None
}

func (x *GeneratedReward) GetRewardValueStr() string {
	if x != nil {
		return x.RewardValueStr
	}
	return ""
}

func (x *GeneratedReward) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GeneratedReward) GetRewardValueInt() int32 {
	if x != nil {
		return x.RewardValueInt
	}
	return 0
}

type RewardInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	RewardId           string                 `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`                                    // 奖励id 可以去配置表中读取
	RewardBaseMinMoney int32                  `protobuf:"varint,2,opt,name=reward_base_min_money,json=rewardBaseMinMoney,proto3" json:"reward_base_min_money,omitempty"` // 最低基础奖励金额
	RewardBaseMaxMoney int32                  `protobuf:"varint,3,opt,name=reward_base_max_money,json=rewardBaseMaxMoney,proto3" json:"reward_base_max_money,omitempty"` // 最高基础奖励金额
	AllRewards         []*RewardValue         `protobuf:"bytes,4,rep,name=all_rewards,json=allRewards,proto3" json:"all_rewards,omitempty"`                              // all组奖励
	OneRewards         []*RewardValue         `protobuf:"bytes,5,rep,name=one_rewards,json=oneRewards,proto3" json:"one_rewards,omitempty"`                              // one组奖励
	TwoRewards         []*RewardValue         `protobuf:"bytes,6,rep,name=two_rewards,json=twoRewards,proto3" json:"two_rewards,omitempty"`                              // two组奖励
	ThreeRewards       []*RewardValue         `protobuf:"bytes,7,rep,name=three_rewards,json=threeRewards,proto3" json:"three_rewards,omitempty"`                        // three组奖励
	SpAllRewards       []*RewardValue         `protobuf:"bytes,8,rep,name=sp_all_rewards,json=spAllRewards,proto3" json:"sp_all_rewards,omitempty"`                      //特别的all组奖励(比如徽章之类的只发一次)
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	mi := &file_MainServer_RewardInfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_RewardInfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{2}
}

func (x *RewardInfo) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardInfo) GetRewardBaseMinMoney() int32 {
	if x != nil {
		return x.RewardBaseMinMoney
	}
	return 0
}

func (x *RewardInfo) GetRewardBaseMaxMoney() int32 {
	if x != nil {
		return x.RewardBaseMaxMoney
	}
	return 0
}

func (x *RewardInfo) GetAllRewards() []*RewardValue {
	if x != nil {
		return x.AllRewards
	}
	return nil
}

func (x *RewardInfo) GetOneRewards() []*RewardValue {
	if x != nil {
		return x.OneRewards
	}
	return nil
}

func (x *RewardInfo) GetTwoRewards() []*RewardValue {
	if x != nil {
		return x.TwoRewards
	}
	return nil
}

func (x *RewardInfo) GetThreeRewards() []*RewardValue {
	if x != nil {
		return x.ThreeRewards
	}
	return nil
}

func (x *RewardInfo) GetSpAllRewards() []*RewardValue {
	if x != nil {
		return x.SpAllRewards
	}
	return nil
}

//	message RewardValues {
//	    int32 count = 1; //0代表
//	    repeated RewardValue reward_values = 2;
//	}
type RewardValue struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RewardType     RewardType             `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=MainServer.RewardType" json:"reward_type,omitempty"` // 奖励类型
	RewardValueStr string                 `protobuf:"bytes,2,opt,name=reward_value_str,json=rewardValueStr,proto3" json:"reward_value_str,omitempty"`
	RewardMinCount int32                  `protobuf:"varint,3,opt,name=reward_min_count,json=rewardMinCount,proto3" json:"reward_min_count,omitempty"` // 最低奖励数量
	RewardMaxCount int32                  `protobuf:"varint,4,opt,name=reward_max_count,json=rewardMaxCount,proto3" json:"reward_max_count,omitempty"` // 最高奖励数量
	DropRate       int32                  `protobuf:"varint,5,opt,name=drop_rate,json=dropRate,proto3" json:"drop_rate,omitempty"`                     // 掉落率 最高100
	RewardValueInt int32                  `protobuf:"varint,6,opt,name=reward_value_int,json=rewardValueInt,proto3" json:"reward_value_int,omitempty"` // 奖励值为int类型，比如徽章是枚举
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RewardValue) Reset() {
	*x = RewardValue{}
	mi := &file_MainServer_RewardInfo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardValue) ProtoMessage() {}

func (x *RewardValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_RewardInfo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardValue.ProtoReflect.Descriptor instead.
func (*RewardValue) Descriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{3}
}

func (x *RewardValue) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_RewardType_None
}

func (x *RewardValue) GetRewardValueStr() string {
	if x != nil {
		return x.RewardValueStr
	}
	return ""
}

func (x *RewardValue) GetRewardMinCount() int32 {
	if x != nil {
		return x.RewardMinCount
	}
	return 0
}

func (x *RewardValue) GetRewardMaxCount() int32 {
	if x != nil {
		return x.RewardMaxCount
	}
	return 0
}

func (x *RewardValue) GetDropRate() int32 {
	if x != nil {
		return x.DropRate
	}
	return 0
}

func (x *RewardValue) GetRewardValueInt() int32 {
	if x != nil {
		return x.RewardValueInt
	}
	return 0
}

type RewardDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChannelType   RewardChannelType      `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=MainServer.RewardChannelType" json:"channel_type,omitempty"` // 奖励渠道类型
	Inventories   []*Inventory           `protobuf:"bytes,2,rep,name=inventories,proto3" json:"inventories,omitempty"`                                                       // 道具
	Pokes         []*Poke                `protobuf:"bytes,3,rep,name=pokes,proto3" json:"pokes,omitempty"`                                                                   // 宠物
	Clothes       []*TrainerCloth        `protobuf:"bytes,4,rep,name=clothes,proto3" json:"clothes,omitempty"`                                                               // 服饰
	Equipments    []*Equipment           `protobuf:"bytes,5,rep,name=equipments,proto3" json:"equipments,omitempty"`                                                         // 装备
	Money         int32                  `protobuf:"varint,6,opt,name=money,proto3" json:"money,omitempty"`                                                                  // 金钱
	Exp           int32                  `protobuf:"varint,7,opt,name=exp,proto3" json:"exp,omitempty"`                                                                      // 经验值
	Badges        []*TrainerBadge        `protobuf:"bytes,8,rep,name=badges,proto3" json:"badges,omitempty"`                                                                 // 徽章
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RewardDetail) Reset() {
	*x = RewardDetail{}
	mi := &file_MainServer_RewardInfo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardDetail) ProtoMessage() {}

func (x *RewardDetail) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_RewardInfo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardDetail.ProtoReflect.Descriptor instead.
func (*RewardDetail) Descriptor() ([]byte, []int) {
	return file_MainServer_RewardInfo_proto_rawDescGZIP(), []int{4}
}

func (x *RewardDetail) GetChannelType() RewardChannelType {
	if x != nil {
		return x.ChannelType
	}
	return RewardChannelType_REWARD_CHANNEL_TYPE_NONE
}

func (x *RewardDetail) GetInventories() []*Inventory {
	if x != nil {
		return x.Inventories
	}
	return nil
}

func (x *RewardDetail) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *RewardDetail) GetClothes() []*TrainerCloth {
	if x != nil {
		return x.Clothes
	}
	return nil
}

func (x *RewardDetail) GetEquipments() []*Equipment {
	if x != nil {
		return x.Equipments
	}
	return nil
}

func (x *RewardDetail) GetMoney() int32 {
	if x != nil {
		return x.Money
	}
	return 0
}

func (x *RewardDetail) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *RewardDetail) GetBadges() []*TrainerBadge {
	if x != nil {
		return x.Badges
	}
	return nil
}

var File_MainServer_RewardInfo_proto protoreflect.FileDescriptor

const file_MainServer_RewardInfo_proto_rawDesc = "" +
	"\n" +
	"\x1bMainServer/RewardInfo.proto\x12\n" +
	"MainServer\x1a\x1aMainServer/Inventory.proto\x1a\x15MainServer/Poke.proto\x1a\x1dMainServer/TrainerCloth.proto\x1a\x1aMainServer/Equipment.proto\x1a\x1dMainServer/TrainerBadge.proto\"\xff\x01\n" +
	"\fRewardResult\x12\x1b\n" +
	"\treward_id\x18\x01 \x01(\tR\brewardId\x12\x1d\n" +
	"\n" +
	"base_money\x18\x02 \x01(\x05R\tbaseMoney\x125\n" +
	"\arewards\x18\x03 \x03(\v2\x1b.MainServer.GeneratedRewardR\arewards\x12:\n" +
	"\n" +
	"sp_rewards\x18\x04 \x03(\v2\x1b.MainServer.GeneratedRewardR\tspRewards\x12@\n" +
	"\fchannel_type\x18\x05 \x01(\x0e2\x1d.MainServer.RewardChannelTypeR\vchannelType\"\xb4\x01\n" +
	"\x0fGeneratedReward\x127\n" +
	"\vreward_type\x18\x01 \x01(\x0e2\x16.MainServer.RewardTypeR\n" +
	"rewardType\x12(\n" +
	"\x10reward_value_str\x18\x02 \x01(\tR\x0erewardValueStr\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12(\n" +
	"\x10reward_value_int\x18\x04 \x01(\x05R\x0erewardValueInt\"\xba\x03\n" +
	"\n" +
	"RewardInfo\x12\x1b\n" +
	"\treward_id\x18\x01 \x01(\tR\brewardId\x121\n" +
	"\x15reward_base_min_money\x18\x02 \x01(\x05R\x12rewardBaseMinMoney\x121\n" +
	"\x15reward_base_max_money\x18\x03 \x01(\x05R\x12rewardBaseMaxMoney\x128\n" +
	"\vall_rewards\x18\x04 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"allRewards\x128\n" +
	"\vone_rewards\x18\x05 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"oneRewards\x128\n" +
	"\vtwo_rewards\x18\x06 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"twoRewards\x12<\n" +
	"\rthree_rewards\x18\a \x03(\v2\x17.MainServer.RewardValueR\fthreeRewards\x12=\n" +
	"\x0esp_all_rewards\x18\b \x03(\v2\x17.MainServer.RewardValueR\fspAllRewards\"\x8b\x02\n" +
	"\vRewardValue\x127\n" +
	"\vreward_type\x18\x01 \x01(\x0e2\x16.MainServer.RewardTypeR\n" +
	"rewardType\x12(\n" +
	"\x10reward_value_str\x18\x02 \x01(\tR\x0erewardValueStr\x12(\n" +
	"\x10reward_min_count\x18\x03 \x01(\x05R\x0erewardMinCount\x12(\n" +
	"\x10reward_max_count\x18\x04 \x01(\x05R\x0erewardMaxCount\x12\x1b\n" +
	"\tdrop_rate\x18\x05 \x01(\x05R\bdropRate\x12(\n" +
	"\x10reward_value_int\x18\x06 \x01(\x05R\x0erewardValueInt\"\xf6\x02\n" +
	"\fRewardDetail\x12@\n" +
	"\fchannel_type\x18\x01 \x01(\x0e2\x1d.MainServer.RewardChannelTypeR\vchannelType\x127\n" +
	"\vinventories\x18\x02 \x03(\v2\x15.MainServer.InventoryR\vinventories\x12&\n" +
	"\x05pokes\x18\x03 \x03(\v2\x10.MainServer.PokeR\x05pokes\x122\n" +
	"\aclothes\x18\x04 \x03(\v2\x18.MainServer.TrainerClothR\aclothes\x125\n" +
	"\n" +
	"equipments\x18\x05 \x03(\v2\x15.MainServer.EquipmentR\n" +
	"equipments\x12\x14\n" +
	"\x05money\x18\x06 \x01(\x05R\x05money\x12\x10\n" +
	"\x03exp\x18\a \x01(\x05R\x03exp\x120\n" +
	"\x06badges\x18\b \x03(\v2\x18.MainServer.TrainerBadgeR\x06badges*\xf0\x01\n" +
	"\x11RewardChannelType\x12\x1c\n" +
	"\x18REWARD_CHANNEL_TYPE_NONE\x10\x00\x12\x1d\n" +
	"\x19REWARD_CHANNEL_TYPE_QUEST\x10\x01\x12\x1f\n" +
	"\x1bREWARD_CHANNEL_TYPE_DUNGEON\x10\x02\x12\x1e\n" +
	"\x1aREWARD_CHANNEL_TYPE_BATTLE\x10\x03\x12\x1c\n" +
	"\x18REWARD_CHANNEL_TYPE_SHOP\x10\x04\x12\x1d\n" +
	"\x19REWARD_CHANNEL_TYPE_OTHER\x10\x05\x12 \n" +
	"\x1cREWARD_CHANNEL_TYPE_EXCHANGE\x10\x06*\xdb\x02\n" +
	"\n" +
	"RewardType\x12\x13\n" +
	"\x0fRewardType_None\x10\x00\x12\x16\n" +
	"\x12RewardType_Pokemon\x10\x01\x12\x13\n" +
	"\x0fRewardType_Item\x10\x02\x12\x14\n" +
	"\x10RewardType_Cloth\x10\x03\x12\x14\n" +
	"\x10RewardType_Money\x10\x04\x12\x12\n" +
	"\x0eRewardType_Exp\x10\x05\x12\x13\n" +
	"\x0fRewardType_Star\x10\x06\x12\x14\n" +
	"\x10RewardType_Title\x10\a\x12\x1a\n" +
	"\x16RewardType_Achievement\x10\b\x12\x1a\n" +
	"\x16RewardType_BattleLevel\x10\t\x12\x1d\n" +
	"\x19RewardType_Config_Pokemon\x10\n" +
	"\x12\x17\n" +
	"\x13RewardType_Sp_Badge\x10\v\x12\x1a\n" +
	"\x16RewardType_Sp_Champion\x10\f\x12\x14\n" +
	"\x10RewardType_Equip\x10\rB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_RewardInfo_proto_rawDescOnce sync.Once
	file_MainServer_RewardInfo_proto_rawDescData []byte
)

func file_MainServer_RewardInfo_proto_rawDescGZIP() []byte {
	file_MainServer_RewardInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_RewardInfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_RewardInfo_proto_rawDesc), len(file_MainServer_RewardInfo_proto_rawDesc)))
	})
	return file_MainServer_RewardInfo_proto_rawDescData
}

var file_MainServer_RewardInfo_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_RewardInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_MainServer_RewardInfo_proto_goTypes = []any{
	(RewardChannelType)(0),  // 0: MainServer.RewardChannelType
	(RewardType)(0),         // 1: MainServer.RewardType
	(*RewardResult)(nil),    // 2: MainServer.RewardResult
	(*GeneratedReward)(nil), // 3: MainServer.GeneratedReward
	(*RewardInfo)(nil),      // 4: MainServer.RewardInfo
	(*RewardValue)(nil),     // 5: MainServer.RewardValue
	(*RewardDetail)(nil),    // 6: MainServer.RewardDetail
	(*Inventory)(nil),       // 7: MainServer.Inventory
	(*Poke)(nil),            // 8: MainServer.Poke
	(*TrainerCloth)(nil),    // 9: MainServer.TrainerCloth
	(*Equipment)(nil),       // 10: MainServer.Equipment
	(*TrainerBadge)(nil),    // 11: MainServer.TrainerBadge
}
var file_MainServer_RewardInfo_proto_depIdxs = []int32{
	3,  // 0: MainServer.RewardResult.rewards:type_name -> MainServer.GeneratedReward
	3,  // 1: MainServer.RewardResult.sp_rewards:type_name -> MainServer.GeneratedReward
	0,  // 2: MainServer.RewardResult.channel_type:type_name -> MainServer.RewardChannelType
	1,  // 3: MainServer.GeneratedReward.reward_type:type_name -> MainServer.RewardType
	5,  // 4: MainServer.RewardInfo.all_rewards:type_name -> MainServer.RewardValue
	5,  // 5: MainServer.RewardInfo.one_rewards:type_name -> MainServer.RewardValue
	5,  // 6: MainServer.RewardInfo.two_rewards:type_name -> MainServer.RewardValue
	5,  // 7: MainServer.RewardInfo.three_rewards:type_name -> MainServer.RewardValue
	5,  // 8: MainServer.RewardInfo.sp_all_rewards:type_name -> MainServer.RewardValue
	1,  // 9: MainServer.RewardValue.reward_type:type_name -> MainServer.RewardType
	0,  // 10: MainServer.RewardDetail.channel_type:type_name -> MainServer.RewardChannelType
	7,  // 11: MainServer.RewardDetail.inventories:type_name -> MainServer.Inventory
	8,  // 12: MainServer.RewardDetail.pokes:type_name -> MainServer.Poke
	9,  // 13: MainServer.RewardDetail.clothes:type_name -> MainServer.TrainerCloth
	10, // 14: MainServer.RewardDetail.equipments:type_name -> MainServer.Equipment
	11, // 15: MainServer.RewardDetail.badges:type_name -> MainServer.TrainerBadge
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_MainServer_RewardInfo_proto_init() }
func file_MainServer_RewardInfo_proto_init() {
	if File_MainServer_RewardInfo_proto != nil {
		return
	}
	file_MainServer_Inventory_proto_init()
	file_MainServer_Poke_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	file_MainServer_Equipment_proto_init()
	file_MainServer_TrainerBadge_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_RewardInfo_proto_rawDesc), len(file_MainServer_RewardInfo_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_RewardInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_RewardInfo_proto_depIdxs,
		EnumInfos:         file_MainServer_RewardInfo_proto_enumTypes,
		MessageInfos:      file_MainServer_RewardInfo_proto_msgTypes,
	}.Build()
	File_MainServer_RewardInfo_proto = out.File
	file_MainServer_RewardInfo_proto_goTypes = nil
	file_MainServer_RewardInfo_proto_depIdxs = nil
}
