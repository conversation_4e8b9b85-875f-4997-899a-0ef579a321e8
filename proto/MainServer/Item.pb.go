// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Item.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ItemFilterSort int32

const (
	ItemFilterSort_price_i ItemFilterSort = 0 //默认用价格排序
)

// Enum value maps for ItemFilterSort.
var (
	ItemFilterSort_name = map[int32]string{
		0: "price_i",
	}
	ItemFilterSort_value = map[string]int32{
		"price_i": 0,
	}
)

func (x ItemFilterSort) Enum() *ItemFilterSort {
	p := new(ItemFilterSort)
	*p = x
	return p
}

func (x ItemFilterSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemFilterSort) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Item_proto_enumTypes[0].Descriptor()
}

func (ItemFilterSort) Type() protoreflect.EnumType {
	return &file_MainServer_Item_proto_enumTypes[0]
}

func (x ItemFilterSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemFilterSort.Descriptor instead.
func (ItemFilterSort) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

type BuySiteType int32

const (
	BuySiteType_BuySiteTypeNormal     BuySiteType = 0
	BuySiteType_BuySiteTypeTeamStore  BuySiteType = 1
	BuySiteType_BuySiteTypeTradeStore BuySiteType = 2
)

// Enum value maps for BuySiteType.
var (
	BuySiteType_name = map[int32]string{
		0: "BuySiteTypeNormal",
		1: "BuySiteTypeTeamStore",
		2: "BuySiteTypeTradeStore",
	}
	BuySiteType_value = map[string]int32{
		"BuySiteTypeNormal":     0,
		"BuySiteTypeTeamStore":  1,
		"BuySiteTypeTradeStore": 2,
	}
)

func (x BuySiteType) Enum() *BuySiteType {
	p := new(BuySiteType)
	*p = x
	return p
}

func (x BuySiteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BuySiteType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Item_proto_enumTypes[1].Descriptor()
}

func (BuySiteType) Type() protoreflect.EnumType {
	return &file_MainServer_Item_proto_enumTypes[1]
}

func (x BuySiteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BuySiteType.Descriptor instead.
func (BuySiteType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{1}
}

type Item struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid   int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	// int32 item_type = 3;
	Name          string     `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Count         int32      `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	Price         float64    `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	SaleCount     int32      `protobuf:"varint,7,opt,name=sale_count,json=saleCount,proto3" json:"sale_count,omitempty"`
	Extra         *ItemExtra `protobuf:"bytes,8,opt,name=extra,proto3" json:"extra,omitempty"`                         // 额外信息
	CreateTs      int64      `protobuf:"varint,9,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`  // 创建时间戳
	UpdateTs      int64      `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Item) Reset() {
	*x = Item{}
	mi := &file_MainServer_Item_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

func (x *Item) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Item) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Item) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Item) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Item) GetSaleCount() int32 {
	if x != nil {
		return x.SaleCount
	}
	return 0
}

func (x *Item) GetExtra() *ItemExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Item) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Item) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type ItemExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemExtra) Reset() {
	*x = ItemExtra{}
	mi := &file_MainServer_Item_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemExtra) ProtoMessage() {}

func (x *ItemExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemExtra.ProtoReflect.Descriptor instead.
func (*ItemExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{1}
}

// 在前端进行名称筛选 再传到后面
type ItemFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Names         []string               `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	Sort          ItemFilterSort         `protobuf:"varint,2,opt,name=sort,proto3,enum=MainServer.ItemFilterSort" json:"sort,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MinPrice      float64                `protobuf:"fixed64,5,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice      float64                `protobuf:"fixed64,6,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,7,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Sale          bool                   `protobuf:"varint,8,opt,name=sale,proto3" json:"sale,omitempty"` //sale_count > 0
	Owner         bool                   `protobuf:"varint,13,opt,name=owner,proto3" json:"owner,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemFilter) Reset() {
	*x = ItemFilter{}
	mi := &file_MainServer_Item_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemFilter) ProtoMessage() {}

func (x *ItemFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemFilter.ProtoReflect.Descriptor instead.
func (*ItemFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{2}
}

func (x *ItemFilter) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *ItemFilter) GetSort() ItemFilterSort {
	if x != nil {
		return x.Sort
	}
	return ItemFilterSort_price_i
}

func (x *ItemFilter) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ItemFilter) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ItemFilter) GetMinPrice() float64 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *ItemFilter) GetMaxPrice() float64 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *ItemFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *ItemFilter) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *ItemFilter) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

type UseItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemId        int64                  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName      string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TargetPokeId  int64                  `protobuf:"varint,4,opt,name=targetPokeId,proto3" json:"targetPokeId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemInfo) Reset() {
	*x = UseItemInfo{}
	mi := &file_MainServer_Item_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemInfo) ProtoMessage() {}

func (x *UseItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemInfo.ProtoReflect.Descriptor instead.
func (*UseItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{3}
}

func (x *UseItemInfo) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *UseItemInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseItemInfo) GetTargetPokeId() int64 {
	if x != nil {
		return x.TargetPokeId
	}
	return 0
}

type UseSummonItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemId        int64                  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName      string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	LockedPokeIds []string               `protobuf:"bytes,4,rep,name=locked_poke_ids,json=lockedPokeIds,proto3" json:"locked_poke_ids,omitempty"` //要锁定的召唤poke
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseSummonItemInfo) Reset() {
	*x = UseSummonItemInfo{}
	mi := &file_MainServer_Item_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseSummonItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseSummonItemInfo) ProtoMessage() {}

func (x *UseSummonItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseSummonItemInfo.ProtoReflect.Descriptor instead.
func (*UseSummonItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{4}
}

func (x *UseSummonItemInfo) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *UseSummonItemInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseSummonItemInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseSummonItemInfo) GetLockedPokeIds() []string {
	if x != nil {
		return x.LockedPokeIds
	}
	return nil
}

type UseSummonItemInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	AppearedPokes []*Poke                `protobuf:"bytes,3,rep,name=appeared_pokes,json=appearedPokes,proto3" json:"appeared_pokes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseSummonItemInfoResponse) Reset() {
	*x = UseSummonItemInfoResponse{}
	mi := &file_MainServer_Item_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseSummonItemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseSummonItemInfoResponse) ProtoMessage() {}

func (x *UseSummonItemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseSummonItemInfoResponse.ProtoReflect.Descriptor instead.
func (*UseSummonItemInfoResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{5}
}

func (x *UseSummonItemInfoResponse) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseSummonItemInfoResponse) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseSummonItemInfoResponse) GetAppearedPokes() []*Poke {
	if x != nil {
		return x.AppearedPokes
	}
	return nil
}

type UseItemInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TargetPoke    *Poke                  `protobuf:"bytes,3,opt,name=targetPoke,proto3" json:"targetPoke,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemInfoResponse) Reset() {
	*x = UseItemInfoResponse{}
	mi := &file_MainServer_Item_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemInfoResponse) ProtoMessage() {}

func (x *UseItemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemInfoResponse.ProtoReflect.Descriptor instead.
func (*UseItemInfoResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{6}
}

func (x *UseItemInfoResponse) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemInfoResponse) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseItemInfoResponse) GetTargetPoke() *Poke {
	if x != nil {
		return x.TargetPoke
	}
	return nil
}

type UseTrainerItemInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TargetTrainer *Trainer               `protobuf:"bytes,3,opt,name=targetTrainer,proto3" json:"targetTrainer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseTrainerItemInfoResponse) Reset() {
	*x = UseTrainerItemInfoResponse{}
	mi := &file_MainServer_Item_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseTrainerItemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseTrainerItemInfoResponse) ProtoMessage() {}

func (x *UseTrainerItemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseTrainerItemInfoResponse.ProtoReflect.Descriptor instead.
func (*UseTrainerItemInfoResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{7}
}

func (x *UseTrainerItemInfoResponse) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseTrainerItemInfoResponse) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseTrainerItemInfoResponse) GetTargetTrainer() *Trainer {
	if x != nil {
		return x.TargetTrainer
	}
	return nil
}

type LocalItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Cost          int32                  `protobuf:"varint,2,opt,name=cost,proto3" json:"cost,omitempty"`
	InventoryType InventoryType          `protobuf:"varint,3,opt,name=inventory_type,json=inventoryType,proto3,enum=MainServer.InventoryType" json:"inventory_type,omitempty"`
	Duration      int32                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Teams         []TrainerTeam          `protobuf:"varint,5,rep,packed,name=teams,proto3,enum=MainServer.TrainerTeam" json:"teams,omitempty"`
	TeamCost      int32                  `protobuf:"varint,6,opt,name=team_cost,json=teamCost,proto3" json:"team_cost,omitempty"`
	Sort          int32                  `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"` //排序权重
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalItem) Reset() {
	*x = LocalItem{}
	mi := &file_MainServer_Item_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalItem) ProtoMessage() {}

func (x *LocalItem) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalItem.ProtoReflect.Descriptor instead.
func (*LocalItem) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{8}
}

func (x *LocalItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LocalItem) GetCost() int32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *LocalItem) GetInventoryType() InventoryType {
	if x != nil {
		return x.InventoryType
	}
	return InventoryType_inventory_unknown
}

func (x *LocalItem) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *LocalItem) GetTeams() []TrainerTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

func (x *LocalItem) GetTeamCost() int32 {
	if x != nil {
		return x.TeamCost
	}
	return 0
}

func (x *LocalItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type LocalItemInfos struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Items         map[string]*LocalItem        `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TypeItemList  map[int32]*LocalTypeItemList `protobuf:"bytes,2,rep,name=type_item_list,json=typeItemList,proto3" json:"type_item_list,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalItemInfos) Reset() {
	*x = LocalItemInfos{}
	mi := &file_MainServer_Item_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalItemInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalItemInfos) ProtoMessage() {}

func (x *LocalItemInfos) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalItemInfos.ProtoReflect.Descriptor instead.
func (*LocalItemInfos) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{9}
}

func (x *LocalItemInfos) GetItems() map[string]*LocalItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *LocalItemInfos) GetTypeItemList() map[int32]*LocalTypeItemList {
	if x != nil {
		return x.TypeItemList
	}
	return nil
}

type LocalTypeItemList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         map[string]*LocalItem  `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalTypeItemList) Reset() {
	*x = LocalTypeItemList{}
	mi := &file_MainServer_Item_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalTypeItemList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalTypeItemList) ProtoMessage() {}

func (x *LocalTypeItemList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalTypeItemList.ProtoReflect.Descriptor instead.
func (*LocalTypeItemList) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{10}
}

func (x *LocalTypeItemList) GetItems() map[string]*LocalItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type BadgeInfos struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// "kanto": [
	//
	//	{
	//	  "nameEn": "Boulder Badge",
	//	  "nameZh": "灰色徽章",
	//	  "nameId": "boulderbadge"
	//	},
	Badges        map[string]*BadgeInfo `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BadgeInfos) Reset() {
	*x = BadgeInfos{}
	mi := &file_MainServer_Item_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BadgeInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BadgeInfos) ProtoMessage() {}

func (x *BadgeInfos) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BadgeInfos.ProtoReflect.Descriptor instead.
func (*BadgeInfos) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{11}
}

func (x *BadgeInfos) GetBadges() map[string]*BadgeInfo {
	if x != nil {
		return x.Badges
	}
	return nil
}

type BadgeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameEn        string                 `protobuf:"bytes,1,opt,name=nameEn,proto3" json:"nameEn,omitempty"`
	NameZh        string                 `protobuf:"bytes,2,opt,name=nameZh,proto3" json:"nameZh,omitempty"`
	NameId        string                 `protobuf:"bytes,3,opt,name=nameId,proto3" json:"nameId,omitempty"`
	Region        string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BadgeInfo) Reset() {
	*x = BadgeInfo{}
	mi := &file_MainServer_Item_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BadgeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BadgeInfo) ProtoMessage() {}

func (x *BadgeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BadgeInfo.ProtoReflect.Descriptor instead.
func (*BadgeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{12}
}

func (x *BadgeInfo) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *BadgeInfo) GetNameZh() string {
	if x != nil {
		return x.NameZh
	}
	return ""
}

func (x *BadgeInfo) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *BadgeInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type RpcBuyItemRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	InventoryType InventoryType          `protobuf:"varint,2,opt,name=inventory_type,json=inventoryType,proto3,enum=MainServer.InventoryType" json:"inventory_type,omitempty"`
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	BuySiteType   BuySiteType            `protobuf:"varint,4,opt,name=buy_site_type,json=buySiteType,proto3,enum=MainServer.BuySiteType" json:"buy_site_type,omitempty"`
	TeamType      TrainerTeam            `protobuf:"varint,5,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"`
	ItemId        int64                  `protobuf:"varint,6,opt,name=itemId,proto3" json:"itemId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcBuyItemRequest) Reset() {
	*x = RpcBuyItemRequest{}
	mi := &file_MainServer_Item_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcBuyItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcBuyItemRequest) ProtoMessage() {}

func (x *RpcBuyItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcBuyItemRequest.ProtoReflect.Descriptor instead.
func (*RpcBuyItemRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{13}
}

func (x *RpcBuyItemRequest) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *RpcBuyItemRequest) GetInventoryType() InventoryType {
	if x != nil {
		return x.InventoryType
	}
	return InventoryType_inventory_unknown
}

func (x *RpcBuyItemRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *RpcBuyItemRequest) GetBuySiteType() BuySiteType {
	if x != nil {
		return x.BuySiteType
	}
	return BuySiteType_BuySiteTypeNormal
}

func (x *RpcBuyItemRequest) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *RpcBuyItemRequest) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

var File_MainServer_Item_proto protoreflect.FileDescriptor

const file_MainServer_Item_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Item.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\x1a\x1aMainServer/Inventory.proto\x1a\x1cMainServer/TrainerTeam.proto\x1a\x18MainServer/Trainer.proto\"\xee\x01\n" +
	"\x04Item\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x14\n" +
	"\x05count\x18\x05 \x01(\x05R\x05count\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x01R\x05price\x12\x1d\n" +
	"\n" +
	"sale_count\x18\a \x01(\x05R\tsaleCount\x12+\n" +
	"\x05extra\x18\b \x01(\v2\x15.MainServer.ItemExtraR\x05extra\x12\x1b\n" +
	"\tcreate_ts\x18\t \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\n" +
	" \x01(\x03R\bupdateTs\"\v\n" +
	"\tItemExtra\"\x84\x02\n" +
	"\n" +
	"ItemFilter\x12\x14\n" +
	"\x05names\x18\x01 \x03(\tR\x05names\x12.\n" +
	"\x04sort\x18\x02 \x01(\x0e2\x1a.MainServer.ItemFilterSortR\x04sort\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1b\n" +
	"\tmin_price\x18\x05 \x01(\x01R\bminPrice\x12\x1b\n" +
	"\tmax_price\x18\x06 \x01(\x01R\bmaxPrice\x12\x1b\n" +
	"\tupdate_ts\x18\a \x01(\x03R\bupdateTs\x12\x12\n" +
	"\x04sale\x18\b \x01(\bR\x04sale\x12\x14\n" +
	"\x05owner\x18\r \x01(\bR\x05owner\"\x83\x01\n" +
	"\vUseItemInfo\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\x03R\x06itemId\x12\x1b\n" +
	"\titem_name\x18\x02 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\"\n" +
	"\ftargetPokeId\x18\x04 \x01(\x03R\ftargetPokeId\"\x8d\x01\n" +
	"\x11UseSummonItemInfo\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\x03R\x06itemId\x12\x1b\n" +
	"\titem_name\x18\x02 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12&\n" +
	"\x0flocked_poke_ids\x18\x04 \x03(\tR\rlockedPokeIds\"\x8d\x01\n" +
	"\x19UseSummonItemInfoResponse\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x127\n" +
	"\x0eappeared_pokes\x18\x03 \x03(\v2\x10.MainServer.PokeR\rappearedPokes\"\x80\x01\n" +
	"\x13UseItemInfoResponse\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x120\n" +
	"\n" +
	"targetPoke\x18\x03 \x01(\v2\x10.MainServer.PokeR\n" +
	"targetPoke\"\x90\x01\n" +
	"\x1aUseTrainerItemInfoResponse\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x129\n" +
	"\rtargetTrainer\x18\x03 \x01(\v2\x13.MainServer.TrainerR\rtargetTrainer\"\xf1\x01\n" +
	"\tLocalItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04cost\x18\x02 \x01(\x05R\x04cost\x12@\n" +
	"\x0einventory_type\x18\x03 \x01(\x0e2\x19.MainServer.InventoryTypeR\rinventoryType\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x05R\bduration\x12-\n" +
	"\x05teams\x18\x05 \x03(\x0e2\x17.MainServer.TrainerTeamR\x05teams\x12\x1b\n" +
	"\tteam_cost\x18\x06 \x01(\x05R\bteamCost\x12\x12\n" +
	"\x04sort\x18\a \x01(\x05R\x04sort\"\xd2\x02\n" +
	"\x0eLocalItemInfos\x12;\n" +
	"\x05items\x18\x01 \x03(\v2%.MainServer.LocalItemInfos.ItemsEntryR\x05items\x12R\n" +
	"\x0etype_item_list\x18\x02 \x03(\v2,.MainServer.LocalItemInfos.TypeItemListEntryR\ftypeItemList\x1aO\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12+\n" +
	"\x05value\x18\x02 \x01(\v2\x15.MainServer.LocalItemR\x05value:\x028\x01\x1a^\n" +
	"\x11TypeItemListEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.MainServer.LocalTypeItemListR\x05value:\x028\x01\"\xa4\x01\n" +
	"\x11LocalTypeItemList\x12>\n" +
	"\x05items\x18\x01 \x03(\v2(.MainServer.LocalTypeItemList.ItemsEntryR\x05items\x1aO\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12+\n" +
	"\x05value\x18\x02 \x01(\v2\x15.MainServer.LocalItemR\x05value:\x028\x01\"\x9a\x01\n" +
	"\n" +
	"BadgeInfos\x12:\n" +
	"\x06badges\x18\x01 \x03(\v2\".MainServer.BadgeInfos.BadgesEntryR\x06badges\x1aP\n" +
	"\vBadgesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12+\n" +
	"\x05value\x18\x02 \x01(\v2\x15.MainServer.BadgeInfoR\x05value:\x028\x01\"k\n" +
	"\tBadgeInfo\x12\x16\n" +
	"\x06nameEn\x18\x01 \x01(\tR\x06nameEn\x12\x16\n" +
	"\x06nameZh\x18\x02 \x01(\tR\x06nameZh\x12\x16\n" +
	"\x06nameId\x18\x03 \x01(\tR\x06nameId\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\"\x93\x02\n" +
	"\x11RpcBuyItemRequest\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12@\n" +
	"\x0einventory_type\x18\x02 \x01(\x0e2\x19.MainServer.InventoryTypeR\rinventoryType\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12;\n" +
	"\rbuy_site_type\x18\x04 \x01(\x0e2\x17.MainServer.BuySiteTypeR\vbuySiteType\x124\n" +
	"\tteam_type\x18\x05 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x12\x16\n" +
	"\x06itemId\x18\x06 \x01(\x03R\x06itemId*\x1d\n" +
	"\x0eItemFilterSort\x12\v\n" +
	"\aprice_i\x10\x00*Y\n" +
	"\vBuySiteType\x12\x15\n" +
	"\x11BuySiteTypeNormal\x10\x00\x12\x18\n" +
	"\x14BuySiteTypeTeamStore\x10\x01\x12\x19\n" +
	"\x15BuySiteTypeTradeStore\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Item_proto_rawDescOnce sync.Once
	file_MainServer_Item_proto_rawDescData []byte
)

func file_MainServer_Item_proto_rawDescGZIP() []byte {
	file_MainServer_Item_proto_rawDescOnce.Do(func() {
		file_MainServer_Item_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Item_proto_rawDesc), len(file_MainServer_Item_proto_rawDesc)))
	})
	return file_MainServer_Item_proto_rawDescData
}

var file_MainServer_Item_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_Item_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_MainServer_Item_proto_goTypes = []any{
	(ItemFilterSort)(0),                // 0: MainServer.ItemFilterSort
	(BuySiteType)(0),                   // 1: MainServer.BuySiteType
	(*Item)(nil),                       // 2: MainServer.Item
	(*ItemExtra)(nil),                  // 3: MainServer.ItemExtra
	(*ItemFilter)(nil),                 // 4: MainServer.ItemFilter
	(*UseItemInfo)(nil),                // 5: MainServer.UseItemInfo
	(*UseSummonItemInfo)(nil),          // 6: MainServer.UseSummonItemInfo
	(*UseSummonItemInfoResponse)(nil),  // 7: MainServer.UseSummonItemInfoResponse
	(*UseItemInfoResponse)(nil),        // 8: MainServer.UseItemInfoResponse
	(*UseTrainerItemInfoResponse)(nil), // 9: MainServer.UseTrainerItemInfoResponse
	(*LocalItem)(nil),                  // 10: MainServer.LocalItem
	(*LocalItemInfos)(nil),             // 11: MainServer.LocalItemInfos
	(*LocalTypeItemList)(nil),          // 12: MainServer.LocalTypeItemList
	(*BadgeInfos)(nil),                 // 13: MainServer.BadgeInfos
	(*BadgeInfo)(nil),                  // 14: MainServer.BadgeInfo
	(*RpcBuyItemRequest)(nil),          // 15: MainServer.RpcBuyItemRequest
	nil,                                // 16: MainServer.LocalItemInfos.ItemsEntry
	nil,                                // 17: MainServer.LocalItemInfos.TypeItemListEntry
	nil,                                // 18: MainServer.LocalTypeItemList.ItemsEntry
	nil,                                // 19: MainServer.BadgeInfos.BadgesEntry
	(*Poke)(nil),                       // 20: MainServer.Poke
	(*Trainer)(nil),                    // 21: MainServer.Trainer
	(InventoryType)(0),                 // 22: MainServer.InventoryType
	(TrainerTeam)(0),                   // 23: MainServer.TrainerTeam
}
var file_MainServer_Item_proto_depIdxs = []int32{
	3,  // 0: MainServer.Item.extra:type_name -> MainServer.ItemExtra
	0,  // 1: MainServer.ItemFilter.sort:type_name -> MainServer.ItemFilterSort
	20, // 2: MainServer.UseSummonItemInfoResponse.appeared_pokes:type_name -> MainServer.Poke
	20, // 3: MainServer.UseItemInfoResponse.targetPoke:type_name -> MainServer.Poke
	21, // 4: MainServer.UseTrainerItemInfoResponse.targetTrainer:type_name -> MainServer.Trainer
	22, // 5: MainServer.LocalItem.inventory_type:type_name -> MainServer.InventoryType
	23, // 6: MainServer.LocalItem.teams:type_name -> MainServer.TrainerTeam
	16, // 7: MainServer.LocalItemInfos.items:type_name -> MainServer.LocalItemInfos.ItemsEntry
	17, // 8: MainServer.LocalItemInfos.type_item_list:type_name -> MainServer.LocalItemInfos.TypeItemListEntry
	18, // 9: MainServer.LocalTypeItemList.items:type_name -> MainServer.LocalTypeItemList.ItemsEntry
	19, // 10: MainServer.BadgeInfos.badges:type_name -> MainServer.BadgeInfos.BadgesEntry
	22, // 11: MainServer.RpcBuyItemRequest.inventory_type:type_name -> MainServer.InventoryType
	1,  // 12: MainServer.RpcBuyItemRequest.buy_site_type:type_name -> MainServer.BuySiteType
	23, // 13: MainServer.RpcBuyItemRequest.team_type:type_name -> MainServer.TrainerTeam
	10, // 14: MainServer.LocalItemInfos.ItemsEntry.value:type_name -> MainServer.LocalItem
	12, // 15: MainServer.LocalItemInfos.TypeItemListEntry.value:type_name -> MainServer.LocalTypeItemList
	10, // 16: MainServer.LocalTypeItemList.ItemsEntry.value:type_name -> MainServer.LocalItem
	14, // 17: MainServer.BadgeInfos.BadgesEntry.value:type_name -> MainServer.BadgeInfo
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_MainServer_Item_proto_init() }
func file_MainServer_Item_proto_init() {
	if File_MainServer_Item_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_Trainer_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Item_proto_rawDesc), len(file_MainServer_Item_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Item_proto_goTypes,
		DependencyIndexes: file_MainServer_Item_proto_depIdxs,
		EnumInfos:         file_MainServer_Item_proto_enumTypes,
		MessageInfos:      file_MainServer_Item_proto_msgTypes,
	}.Build()
	File_MainServer_Item_proto = out.File
	file_MainServer_Item_proto_goTypes = nil
	file_MainServer_Item_proto_depIdxs = nil
}
