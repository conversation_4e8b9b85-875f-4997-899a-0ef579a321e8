// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTeam int32

const (
	TrainerTeam_TRAINER_TEAM_NONE     TrainerTeam = 0
	TrainerTeam_TRAINER_TEAM_ALL      TrainerTeam = 1  //全部team
	TrainerTeam_TRAINER_TEAM_Rocket   TrainerTeam = 11 //火箭队
	TrainerTeam_TRAINER_TEAM_Magma    TrainerTeam = 12 //熔岩队
	TrainerTeam_TRAINER_TEAM_Aqua     TrainerTeam = 13 //海洋队
	TrainerTeam_TRAINER_TEAM_Galactic TrainerTeam = 14 //银河队
	TrainerTeam_TRAINER_TEAM_Plasma   TrainerTeam = 15 //等离子队
	TrainerTeam_TRAINER_TEAM_Flare    TrainerTeam = 16 //闪焰队
	TrainerTeam_TRAINER_TEAM_Skull    TrainerTeam = 17 //骷髅队
	TrainerTeam_TRAINER_TEAM_Yell     TrainerTeam = 18 //呐喊队
	TrainerTeam_TRAINER_TEAM_Star     TrainerTeam = 19 //天星队
)

// Enum value maps for TrainerTeam.
var (
	TrainerTeam_name = map[int32]string{
		0:  "TRAINER_TEAM_NONE",
		1:  "TRAINER_TEAM_ALL",
		11: "TRAINER_TEAM_Rocket",
		12: "TRAINER_TEAM_Magma",
		13: "TRAINER_TEAM_Aqua",
		14: "TRAINER_TEAM_Galactic",
		15: "TRAINER_TEAM_Plasma",
		16: "TRAINER_TEAM_Flare",
		17: "TRAINER_TEAM_Skull",
		18: "TRAINER_TEAM_Yell",
		19: "TRAINER_TEAM_Star",
	}
	TrainerTeam_value = map[string]int32{
		"TRAINER_TEAM_NONE":     0,
		"TRAINER_TEAM_ALL":      1,
		"TRAINER_TEAM_Rocket":   11,
		"TRAINER_TEAM_Magma":    12,
		"TRAINER_TEAM_Aqua":     13,
		"TRAINER_TEAM_Galactic": 14,
		"TRAINER_TEAM_Plasma":   15,
		"TRAINER_TEAM_Flare":    16,
		"TRAINER_TEAM_Skull":    17,
		"TRAINER_TEAM_Yell":     18,
		"TRAINER_TEAM_Star":     19,
	}
)

func (x TrainerTeam) Enum() *TrainerTeam {
	p := new(TrainerTeam)
	*p = x
	return p
}

func (x TrainerTeam) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTeam) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeam_proto_enumTypes[0].Descriptor()
}

func (TrainerTeam) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeam_proto_enumTypes[0]
}

func (x TrainerTeam) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTeam.Descriptor instead.
func (TrainerTeam) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

type SummonFluteLevelExp int32

const (
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_0 SummonFluteLevelExp = 0
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_1 SummonFluteLevelExp = 1000   //1.2 可能出现两只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_2 SummonFluteLevelExp = 2000   //1.5 可能出现两只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_3 SummonFluteLevelExp = 4000   //2.0 可能出现两只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_4 SummonFluteLevelExp = 8000   //2.3 可能出现3只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_5 SummonFluteLevelExp = 16000  //2.8 可能出现3只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_6 SummonFluteLevelExp = 32000  //3.1 可能出现4只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_7 SummonFluteLevelExp = 64000  //3.5 必定出现4只
	SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_8 SummonFluteLevelExp = 128000 //4.0 必定出现5只
)

// Enum value maps for SummonFluteLevelExp.
var (
	SummonFluteLevelExp_name = map[int32]string{
		0:      "SUMMON_FLUTE_LEVEL_EXP_0",
		1000:   "SUMMON_FLUTE_LEVEL_EXP_1",
		2000:   "SUMMON_FLUTE_LEVEL_EXP_2",
		4000:   "SUMMON_FLUTE_LEVEL_EXP_3",
		8000:   "SUMMON_FLUTE_LEVEL_EXP_4",
		16000:  "SUMMON_FLUTE_LEVEL_EXP_5",
		32000:  "SUMMON_FLUTE_LEVEL_EXP_6",
		64000:  "SUMMON_FLUTE_LEVEL_EXP_7",
		128000: "SUMMON_FLUTE_LEVEL_EXP_8",
	}
	SummonFluteLevelExp_value = map[string]int32{
		"SUMMON_FLUTE_LEVEL_EXP_0": 0,
		"SUMMON_FLUTE_LEVEL_EXP_1": 1000,
		"SUMMON_FLUTE_LEVEL_EXP_2": 2000,
		"SUMMON_FLUTE_LEVEL_EXP_3": 4000,
		"SUMMON_FLUTE_LEVEL_EXP_4": 8000,
		"SUMMON_FLUTE_LEVEL_EXP_5": 16000,
		"SUMMON_FLUTE_LEVEL_EXP_6": 32000,
		"SUMMON_FLUTE_LEVEL_EXP_7": 64000,
		"SUMMON_FLUTE_LEVEL_EXP_8": 128000,
	}
)

func (x SummonFluteLevelExp) Enum() *SummonFluteLevelExp {
	p := new(SummonFluteLevelExp)
	*p = x
	return p
}

func (x SummonFluteLevelExp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SummonFluteLevelExp) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeam_proto_enumTypes[1].Descriptor()
}

func (SummonFluteLevelExp) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeam_proto_enumTypes[1]
}

func (x SummonFluteLevelExp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SummonFluteLevelExp.Descriptor instead.
func (SummonFluteLevelExp) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{1}
}

type TrainerOnTeamInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contribution  int64                  `protobuf:"varint,1,opt,name=contribution,proto3" json:"contribution,omitempty"` //阵营贡献
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Exp           int32                  `protobuf:"varint,3,opt,name=exp,proto3" json:"exp,omitempty"`
	SummonFlute   *SummonFluteInfo       `protobuf:"bytes,4,opt,name=summon_flute,json=summonFlute,proto3" json:"summon_flute,omitempty"` // map<int32, TrainerTeamInfoDetail> teamInfo = 1;
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerOnTeamInfo) Reset() {
	*x = TrainerOnTeamInfo{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerOnTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerOnTeamInfo) ProtoMessage() {}

func (x *TrainerOnTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerOnTeamInfo.ProtoReflect.Descriptor instead.
func (*TrainerOnTeamInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerOnTeamInfo) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *TrainerOnTeamInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *TrainerOnTeamInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *TrainerOnTeamInfo) GetSummonFlute() *SummonFluteInfo {
	if x != nil {
		return x.SummonFlute
	}
	return nil
}

type SummonFluteInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CountLevel    int32                  `protobuf:"varint,1,opt,name=count_level,json=countLevel,proto3" json:"count_level,omitempty"`
	LockLevel     int32                  `protobuf:"varint,2,opt,name=lock_level,json=lockLevel,proto3" json:"lock_level,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SummonFluteInfo) Reset() {
	*x = SummonFluteInfo{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SummonFluteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummonFluteInfo) ProtoMessage() {}

func (x *SummonFluteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummonFluteInfo.ProtoReflect.Descriptor instead.
func (*SummonFluteInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{1}
}

func (x *SummonFluteInfo) GetCountLevel() int32 {
	if x != nil {
		return x.CountLevel
	}
	return 0
}

func (x *SummonFluteInfo) GetLockLevel() int32 {
	if x != nil {
		return x.LockLevel
	}
	return 0
}

type TrainerTeamInfoDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeamType      TrainerTeam            `protobuf:"varint,1,opt,name=teamType,proto3,enum=MainServer.TrainerTeam" json:"teamType,omitempty"`
	Contribution  int64                  `protobuf:"varint,2,opt,name=contribution,proto3" json:"contribution,omitempty"` //阵营贡献
	Level         int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	Exp           int32                  `protobuf:"varint,4,opt,name=exp,proto3" json:"exp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerTeamInfoDetail) Reset() {
	*x = TrainerTeamInfoDetail{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTeamInfoDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTeamInfoDetail) ProtoMessage() {}

func (x *TrainerTeamInfoDetail) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTeamInfoDetail.ProtoReflect.Descriptor instead.
func (*TrainerTeamInfoDetail) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{2}
}

func (x *TrainerTeamInfoDetail) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *TrainerTeamInfoDetail) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *TrainerTeamInfoDetail) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *TrainerTeamInfoDetail) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

type RpcAddTrainerTeamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerTeam            `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTeam" json:"type,omitempty"` //要加入的组织
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAddTrainerTeamRequest) Reset() {
	*x = RpcAddTrainerTeamRequest{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAddTrainerTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerTeamRequest) ProtoMessage() {}

func (x *RpcAddTrainerTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerTeamRequest.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerTeamRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{3}
}

func (x *RpcAddTrainerTeamRequest) GetType() TrainerTeam {
	if x != nil {
		return x.Type
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

type RpcUpgradeTeamSummonFluteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsCount       bool                   `protobuf:"varint,1,opt,name=is_count,json=isCount,proto3" json:"is_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUpgradeTeamSummonFluteRequest) Reset() {
	*x = RpcUpgradeTeamSummonFluteRequest{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUpgradeTeamSummonFluteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpgradeTeamSummonFluteRequest) ProtoMessage() {}

func (x *RpcUpgradeTeamSummonFluteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpgradeTeamSummonFluteRequest.ProtoReflect.Descriptor instead.
func (*RpcUpgradeTeamSummonFluteRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{4}
}

func (x *RpcUpgradeTeamSummonFluteRequest) GetIsCount() bool {
	if x != nil {
		return x.IsCount
	}
	return false
}

var File_MainServer_TrainerTeam_proto protoreflect.FileDescriptor

const file_MainServer_TrainerTeam_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/TrainerTeam.proto\x12\n" +
	"MainServer\"\x9f\x01\n" +
	"\x11TrainerOnTeamInfo\x12\"\n" +
	"\fcontribution\x18\x01 \x01(\x03R\fcontribution\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12\x10\n" +
	"\x03exp\x18\x03 \x01(\x05R\x03exp\x12>\n" +
	"\fsummon_flute\x18\x04 \x01(\v2\x1b.MainServer.SummonFluteInfoR\vsummonFlute\"Q\n" +
	"\x0fSummonFluteInfo\x12\x1f\n" +
	"\vcount_level\x18\x01 \x01(\x05R\n" +
	"countLevel\x12\x1d\n" +
	"\n" +
	"lock_level\x18\x02 \x01(\x05R\tlockLevel\"\x98\x01\n" +
	"\x15TrainerTeamInfoDetail\x123\n" +
	"\bteamType\x18\x01 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x12\"\n" +
	"\fcontribution\x18\x02 \x01(\x03R\fcontribution\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12\x10\n" +
	"\x03exp\x18\x04 \x01(\x05R\x03exp\"G\n" +
	"\x18RpcAddTrainerTeamRequest\x12+\n" +
	"\x04type\x18\x01 \x01(\x0e2\x17.MainServer.TrainerTeamR\x04type\"=\n" +
	" RpcUpgradeTeamSummonFluteRequest\x12\x19\n" +
	"\bis_count\x18\x01 \x01(\bR\aisCount*\x94\x02\n" +
	"\vTrainerTeam\x12\x15\n" +
	"\x11TRAINER_TEAM_NONE\x10\x00\x12\x14\n" +
	"\x10TRAINER_TEAM_ALL\x10\x01\x12\x17\n" +
	"\x13TRAINER_TEAM_Rocket\x10\v\x12\x16\n" +
	"\x12TRAINER_TEAM_Magma\x10\f\x12\x15\n" +
	"\x11TRAINER_TEAM_Aqua\x10\r\x12\x19\n" +
	"\x15TRAINER_TEAM_Galactic\x10\x0e\x12\x17\n" +
	"\x13TRAINER_TEAM_Plasma\x10\x0f\x12\x16\n" +
	"\x12TRAINER_TEAM_Flare\x10\x10\x12\x16\n" +
	"\x12TRAINER_TEAM_Skull\x10\x11\x12\x15\n" +
	"\x11TRAINER_TEAM_Yell\x10\x12\x12\x15\n" +
	"\x11TRAINER_TEAM_Star\x10\x13*\xae\x02\n" +
	"\x13SummonFluteLevelExp\x12\x1c\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_0\x10\x00\x12\x1d\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_1\x10\xe8\a\x12\x1d\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_2\x10\xd0\x0f\x12\x1d\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_3\x10\xa0\x1f\x12\x1d\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_4\x10\xc0>\x12\x1d\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_5\x10\x80}\x12\x1e\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_6\x10\x80\xfa\x01\x12\x1e\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_7\x10\x80\xf4\x03\x12\x1e\n" +
	"\x18SUMMON_FLUTE_LEVEL_EXP_8\x10\x80\xe8\aB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerTeam_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTeam_proto_rawDescData []byte
)

func file_MainServer_TrainerTeam_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTeam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeam_proto_rawDesc), len(file_MainServer_TrainerTeam_proto_rawDesc)))
	})
	return file_MainServer_TrainerTeam_proto_rawDescData
}

var file_MainServer_TrainerTeam_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_TrainerTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_MainServer_TrainerTeam_proto_goTypes = []any{
	(TrainerTeam)(0),                         // 0: MainServer.TrainerTeam
	(SummonFluteLevelExp)(0),                 // 1: MainServer.SummonFluteLevelExp
	(*TrainerOnTeamInfo)(nil),                // 2: MainServer.TrainerOnTeamInfo
	(*SummonFluteInfo)(nil),                  // 3: MainServer.SummonFluteInfo
	(*TrainerTeamInfoDetail)(nil),            // 4: MainServer.TrainerTeamInfoDetail
	(*RpcAddTrainerTeamRequest)(nil),         // 5: MainServer.RpcAddTrainerTeamRequest
	(*RpcUpgradeTeamSummonFluteRequest)(nil), // 6: MainServer.RpcUpgradeTeamSummonFluteRequest
}
var file_MainServer_TrainerTeam_proto_depIdxs = []int32{
	3, // 0: MainServer.TrainerOnTeamInfo.summon_flute:type_name -> MainServer.SummonFluteInfo
	0, // 1: MainServer.TrainerTeamInfoDetail.teamType:type_name -> MainServer.TrainerTeam
	0, // 2: MainServer.RpcAddTrainerTeamRequest.type:type_name -> MainServer.TrainerTeam
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTeam_proto_init() }
func file_MainServer_TrainerTeam_proto_init() {
	if File_MainServer_TrainerTeam_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeam_proto_rawDesc), len(file_MainServer_TrainerTeam_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTeam_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTeam_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTeam_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTeam_proto = out.File
	file_MainServer_TrainerTeam_proto_goTypes = nil
	file_MainServer_TrainerTeam_proto_depIdxs = nil
}
