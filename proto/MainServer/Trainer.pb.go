// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Trainer.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerSpecialRight int32

const (
	TrainerSpecialRight_TrainerNone    TrainerSpecialRight = 0
	TrainerSpecialRight_TrainerVip     TrainerSpecialRight = 1
	TrainerSpecialRight_TrainerPremium TrainerSpecialRight = 2
)

// Enum value maps for TrainerSpecialRight.
var (
	TrainerSpecialRight_name = map[int32]string{
		0: "TrainerNone",
		1: "TrainerVip",
		2: "TrainerPremium",
	}
	TrainerSpecialRight_value = map[string]int32{
		"TrainerNone":    0,
		"TrainerVip":     1,
		"TrainerPremium": 2,
	}
)

func (x TrainerSpecialRight) Enum() *TrainerSpecialRight {
	p := new(TrainerSpecialRight)
	*p = x
	return p
}

func (x TrainerSpecialRight) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerSpecialRight) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Trainer_proto_enumTypes[0].Descriptor()
}

func (TrainerSpecialRight) Type() protoreflect.EnumType {
	return &file_MainServer_Trainer_proto_enumTypes[0]
}

func (x TrainerSpecialRight) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerSpecialRight.Descriptor instead.
func (TrainerSpecialRight) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{0}
}

type TrainerActionType int32

const (
	TrainerActionType_idle   TrainerActionType = 0
	TrainerActionType_battle TrainerActionType = 1
)

// Enum value maps for TrainerActionType.
var (
	TrainerActionType_name = map[int32]string{
		0: "idle",
		1: "battle",
	}
	TrainerActionType_value = map[string]int32{
		"idle":   0,
		"battle": 1,
	}
)

func (x TrainerActionType) Enum() *TrainerActionType {
	p := new(TrainerActionType)
	*p = x
	return p
}

func (x TrainerActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Trainer_proto_enumTypes[1].Descriptor()
}

func (TrainerActionType) Type() protoreflect.EnumType {
	return &file_MainServer_Trainer_proto_enumTypes[1]
}

func (x TrainerActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerActionType.Descriptor instead.
func (TrainerActionType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{1}
}

type TrainerRideType int32

const (
	TrainerRideType_Ride_None       TrainerRideType = 0
	TrainerRideType_Ride_NormalBike TrainerRideType = 1
)

// Enum value maps for TrainerRideType.
var (
	TrainerRideType_name = map[int32]string{
		0: "Ride_None",
		1: "Ride_NormalBike",
	}
	TrainerRideType_value = map[string]int32{
		"Ride_None":       0,
		"Ride_NormalBike": 1,
	}
)

func (x TrainerRideType) Enum() *TrainerRideType {
	p := new(TrainerRideType)
	*p = x
	return p
}

func (x TrainerRideType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerRideType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Trainer_proto_enumTypes[2].Descriptor()
}

func (TrainerRideType) Type() protoreflect.EnumType {
	return &file_MainServer_Trainer_proto_enumTypes[2]
}

func (x TrainerRideType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerRideType.Descriptor instead.
func (TrainerRideType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{2}
}

type Trainer struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid        string                 `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Name       string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Gender     Gender                 `protobuf:"varint,4,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`
	ActionInfo *TrainerActionInfo     `protobuf:"bytes,5,opt,name=action_info,json=actionInfo,proto3" json:"action_info,omitempty"` // regin_id|[x,y,z]
	PokeIds    []string               `protobuf:"bytes,6,rep,name=poke_ids,json=pokeIds,proto3" json:"poke_ids,omitempty"`
	// TrainerActionType action = 7;
	Items         map[string]*TrainerItemInfo `protobuf:"bytes,8,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //使用的道具 //key为道具id //后面的info
	Badges        *TrainerBadges              `protobuf:"bytes,9,opt,name=badges,proto3" json:"badges,omitempty"`                                                                         //微章
	Team          TrainerTeam                 `protobuf:"varint,10,opt,name=team,proto3,enum=MainServer.TrainerTeam" json:"team,omitempty"`                                               //所属阵营
	GroupId       string                      `protobuf:"bytes,11,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`                                                       //玩家团
	Cloth         *TrainerCloth               `protobuf:"bytes,12,opt,name=cloth,proto3" json:"cloth,omitempty"`
	Coin          int64                       `protobuf:"varint,13,opt,name=coin,proto3" json:"coin,omitempty"`                                                                         //硬币
	TeamInfo      *TrainerOnTeamInfo          `protobuf:"bytes,14,opt,name=team_info,json=teamInfo,proto3" json:"team_info,omitempty"`                                                  //阵营贡献
	SpecialCoin   int64                       `protobuf:"varint,15,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`                                        //特殊硬币 //比如用充值卡兑换的
	FollowPoke    *TrainerFollowPoke          `protobuf:"bytes,16,opt,name=follow_poke,json=followPoke,proto3" json:"follow_poke,omitempty"`                                            //跟随的宝可梦
	CreateTs      int64                       `protobuf:"varint,17,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                                 // 创建时间戳
	UpdateTs      int64                       `protobuf:"varint,18,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                                 // 更新时间戳
	OnlineTime    int64                       `protobuf:"varint,19,opt,name=online_time,json=onlineTime,proto3" json:"online_time,omitempty"`                                           // 在线时间
	SpecialRight  TrainerSpecialRight         `protobuf:"varint,20,opt,name=special_right,json=specialRight,proto3,enum=MainServer.TrainerSpecialRight" json:"special_right,omitempty"` //特殊权限
	BoxStatus     *TrainerBoxStatus           `protobuf:"bytes,21,opt,name=box_status,json=boxStatus,proto3" json:"box_status,omitempty"`                                               //盒子状态
	StrictInfo    *TrainerStrictInfo          `protobuf:"bytes,22,opt,name=strict_info,json=strictInfo,proto3" json:"strict_info,omitempty"`                                            //限制信息
	Decoration    *TrainerDecoration          `protobuf:"bytes,23,opt,name=decoration,proto3" json:"decoration,omitempty"`                                                              //训练师装饰
	SessionInfo   *TrainerSessionInfo         `protobuf:"bytes,24,opt,name=sessionInfo,proto3" json:"sessionInfo,omitempty"`                                                            //不存入数据库的数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Trainer) Reset() {
	*x = Trainer{}
	mi := &file_MainServer_Trainer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Trainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trainer) ProtoMessage() {}

func (x *Trainer) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trainer.ProtoReflect.Descriptor instead.
func (*Trainer) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{0}
}

func (x *Trainer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Trainer) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Trainer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Trainer) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *Trainer) GetActionInfo() *TrainerActionInfo {
	if x != nil {
		return x.ActionInfo
	}
	return nil
}

func (x *Trainer) GetPokeIds() []string {
	if x != nil {
		return x.PokeIds
	}
	return nil
}

func (x *Trainer) GetItems() map[string]*TrainerItemInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Trainer) GetBadges() *TrainerBadges {
	if x != nil {
		return x.Badges
	}
	return nil
}

func (x *Trainer) GetTeam() TrainerTeam {
	if x != nil {
		return x.Team
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *Trainer) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Trainer) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

func (x *Trainer) GetCoin() int64 {
	if x != nil {
		return x.Coin
	}
	return 0
}

func (x *Trainer) GetTeamInfo() *TrainerOnTeamInfo {
	if x != nil {
		return x.TeamInfo
	}
	return nil
}

func (x *Trainer) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *Trainer) GetFollowPoke() *TrainerFollowPoke {
	if x != nil {
		return x.FollowPoke
	}
	return nil
}

func (x *Trainer) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Trainer) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *Trainer) GetOnlineTime() int64 {
	if x != nil {
		return x.OnlineTime
	}
	return 0
}

func (x *Trainer) GetSpecialRight() TrainerSpecialRight {
	if x != nil {
		return x.SpecialRight
	}
	return TrainerSpecialRight_TrainerNone
}

func (x *Trainer) GetBoxStatus() *TrainerBoxStatus {
	if x != nil {
		return x.BoxStatus
	}
	return nil
}

func (x *Trainer) GetStrictInfo() *TrainerStrictInfo {
	if x != nil {
		return x.StrictInfo
	}
	return nil
}

func (x *Trainer) GetDecoration() *TrainerDecoration {
	if x != nil {
		return x.Decoration
	}
	return nil
}

func (x *Trainer) GetSessionInfo() *TrainerSessionInfo {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

type TrainerActionInfo struct {
	state        protoimpl.MessageState      `protogen:"open.v1"`
	Loc          *TrainerLoc                 `protobuf:"bytes,1,opt,name=loc,proto3" json:"loc,omitempty"`
	LastMainLand *TrainerLastMainLandLocInfo `protobuf:"bytes,2,opt,name=last_main_land,json=lastMainLand,proto3" json:"last_main_land,omitempty"`
	// string lastPcName = 2;
	Action        TrainerActionType `protobuf:"varint,3,opt,name=action,proto3,enum=MainServer.TrainerActionType" json:"action,omitempty"`
	IsInstance    bool              `protobuf:"varint,4,opt,name=is_instance,json=isInstance,proto3" json:"is_instance,omitempty"` //是否在副本
	InstanceId    string            `protobuf:"bytes,5,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`  //副本id
	UpdateTs      int64             `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerActionInfo) Reset() {
	*x = TrainerActionInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerActionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerActionInfo) ProtoMessage() {}

func (x *TrainerActionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerActionInfo.ProtoReflect.Descriptor instead.
func (*TrainerActionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerActionInfo) GetLoc() *TrainerLoc {
	if x != nil {
		return x.Loc
	}
	return nil
}

func (x *TrainerActionInfo) GetLastMainLand() *TrainerLastMainLandLocInfo {
	if x != nil {
		return x.LastMainLand
	}
	return nil
}

func (x *TrainerActionInfo) GetAction() TrainerActionType {
	if x != nil {
		return x.Action
	}
	return TrainerActionType_idle
}

func (x *TrainerActionInfo) GetIsInstance() bool {
	if x != nil {
		return x.IsInstance
	}
	return false
}

func (x *TrainerActionInfo) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *TrainerActionInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type TrainerLastMainLandLocInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Loc           *TrainerLoc            `protobuf:"bytes,1,opt,name=loc,proto3" json:"loc,omitempty"`
	LastPcName    string                 `protobuf:"bytes,2,opt,name=lastPcName,proto3" json:"lastPcName,omitempty"` //pc 的传送点名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerLastMainLandLocInfo) Reset() {
	*x = TrainerLastMainLandLocInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerLastMainLandLocInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLastMainLandLocInfo) ProtoMessage() {}

func (x *TrainerLastMainLandLocInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLastMainLandLocInfo.ProtoReflect.Descriptor instead.
func (*TrainerLastMainLandLocInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{2}
}

func (x *TrainerLastMainLandLocInfo) GetLoc() *TrainerLoc {
	if x != nil {
		return x.Loc
	}
	return nil
}

func (x *TrainerLastMainLandLocInfo) GetLastPcName() string {
	if x != nil {
		return x.LastPcName
	}
	return ""
}

type TrainerDecoration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// repeated TrainerTitleInfo titles = 1; //称号
	EquipmentTitle    *TrainerEquipmentInfo `protobuf:"bytes,1,opt,name=equipment_title,json=equipmentTitle,proto3" json:"equipment_title,omitempty"`
	EquipmentCard     *TrainerEquipmentInfo `protobuf:"bytes,2,opt,name=equipment_card,json=equipmentCard,proto3" json:"equipment_card,omitempty"`
	EquipmentRide     *TrainerEquipmentInfo `protobuf:"bytes,3,opt,name=equipment_ride,json=equipmentRide,proto3" json:"equipment_ride,omitempty"`
	EquipmentPokeball *TrainerEquipmentInfo `protobuf:"bytes,4,opt,name=equipment_pokeball,json=equipmentPokeball,proto3" json:"equipment_pokeball,omitempty"`
	EquipmentBadge    *TrainerEquipmentInfo `protobuf:"bytes,5,opt,name=equipment_badge,json=equipmentBadge,proto3" json:"equipment_badge,omitempty"`
	EquipmentAmulet   *TrainerEquipmentInfo `protobuf:"bytes,6,opt,name=equipment_amulet,json=equipmentAmulet,proto3" json:"equipment_amulet,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TrainerDecoration) Reset() {
	*x = TrainerDecoration{}
	mi := &file_MainServer_Trainer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerDecoration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerDecoration) ProtoMessage() {}

func (x *TrainerDecoration) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerDecoration.ProtoReflect.Descriptor instead.
func (*TrainerDecoration) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{3}
}

func (x *TrainerDecoration) GetEquipmentTitle() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentTitle
	}
	return nil
}

func (x *TrainerDecoration) GetEquipmentCard() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentCard
	}
	return nil
}

func (x *TrainerDecoration) GetEquipmentRide() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentRide
	}
	return nil
}

func (x *TrainerDecoration) GetEquipmentPokeball() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentPokeball
	}
	return nil
}

func (x *TrainerDecoration) GetEquipmentBadge() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentBadge
	}
	return nil
}

func (x *TrainerDecoration) GetEquipmentAmulet() *TrainerEquipmentInfo {
	if x != nil {
		return x.EquipmentAmulet
	}
	return nil
}

type TrainerEquipmentInfo struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EquipmentName string                  `protobuf:"bytes,2,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`
	EquipmentType EquipmentType           `protobuf:"varint,3,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"`
	FortifyCount  int32                   `protobuf:"varint,4,opt,name=fortifyCount,proto3" json:"fortifyCount,omitempty"`              //加强数 （+1）
	EffectInfo    *TrainerEquipmentEffect `protobuf:"bytes,5,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"` //效果信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerEquipmentInfo) Reset() {
	*x = TrainerEquipmentInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerEquipmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerEquipmentInfo) ProtoMessage() {}

func (x *TrainerEquipmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerEquipmentInfo.ProtoReflect.Descriptor instead.
func (*TrainerEquipmentInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{4}
}

func (x *TrainerEquipmentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerEquipmentInfo) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *TrainerEquipmentInfo) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

func (x *TrainerEquipmentInfo) GetFortifyCount() int32 {
	if x != nil {
		return x.FortifyCount
	}
	return 0
}

func (x *TrainerEquipmentInfo) GetEffectInfo() *TrainerEquipmentEffect {
	if x != nil {
		return x.EffectInfo
	}
	return nil
}

type TrainerItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UseTs         int64                  `protobuf:"varint,1,opt,name=useTs,proto3" json:"useTs,omitempty"`
	ExpireTs      int64                  `protobuf:"varint,2,opt,name=expireTs,proto3" json:"expireTs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerItemInfo) Reset() {
	*x = TrainerItemInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerItemInfo) ProtoMessage() {}

func (x *TrainerItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerItemInfo.ProtoReflect.Descriptor instead.
func (*TrainerItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{5}
}

func (x *TrainerItemInfo) GetUseTs() int64 {
	if x != nil {
		return x.UseTs
	}
	return 0
}

func (x *TrainerItemInfo) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

type TrainerBoxStatus struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ActiveBoxes        int32                  `protobuf:"varint,1,opt,name=active_boxes,json=activeBoxes,proto3" json:"active_boxes,omitempty"`
	SpecialActiveBoxes int32                  `protobuf:"varint,2,opt,name=special_active_boxes,json=specialActiveBoxes,proto3" json:"special_active_boxes,omitempty"`
	InitVersion        int32                  `protobuf:"varint,3,opt,name=init_version,json=initVersion,proto3" json:"init_version,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TrainerBoxStatus) Reset() {
	*x = TrainerBoxStatus{}
	mi := &file_MainServer_Trainer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBoxStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBoxStatus) ProtoMessage() {}

func (x *TrainerBoxStatus) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBoxStatus.ProtoReflect.Descriptor instead.
func (*TrainerBoxStatus) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{6}
}

func (x *TrainerBoxStatus) GetActiveBoxes() int32 {
	if x != nil {
		return x.ActiveBoxes
	}
	return 0
}

func (x *TrainerBoxStatus) GetSpecialActiveBoxes() int32 {
	if x != nil {
		return x.SpecialActiveBoxes
	}
	return 0
}

func (x *TrainerBoxStatus) GetInitVersion() int32 {
	if x != nil {
		return x.InitVersion
	}
	return 0
}

type TrainerSessionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BattlePokes   []*Poke                `protobuf:"bytes,1,rep,name=battlePokes,proto3" json:"battlePokes,omitempty"`                          //战斗的poke
	SessionEndTs  int64                  `protobuf:"varint,2,opt,name=session_end_ts,json=sessionEndTs,proto3" json:"session_end_ts,omitempty"` //离线时间 //不存入数据库
	LocInfo       *TrainerLocInfo        `protobuf:"bytes,3,opt,name=locInfo,proto3" json:"locInfo,omitempty"`                                  //上一个位置信息，//不存入数据库 //位置信息
	MatchId       string                 `protobuf:"bytes,4,opt,name=matchId,proto3" json:"matchId,omitempty"`
	PartyId       string                 `protobuf:"bytes,5,opt,name=partyId,proto3" json:"partyId,omitempty"`                                  //派对id
	LastOnlineTs  int64                  `protobuf:"varint,6,opt,name=last_online_ts,json=lastOnlineTs,proto3" json:"last_online_ts,omitempty"` //最后在线时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerSessionInfo) Reset() {
	*x = TrainerSessionInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerSessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerSessionInfo) ProtoMessage() {}

func (x *TrainerSessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerSessionInfo.ProtoReflect.Descriptor instead.
func (*TrainerSessionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{7}
}

func (x *TrainerSessionInfo) GetBattlePokes() []*Poke {
	if x != nil {
		return x.BattlePokes
	}
	return nil
}

func (x *TrainerSessionInfo) GetSessionEndTs() int64 {
	if x != nil {
		return x.SessionEndTs
	}
	return 0
}

func (x *TrainerSessionInfo) GetLocInfo() *TrainerLocInfo {
	if x != nil {
		return x.LocInfo
	}
	return nil
}

func (x *TrainerSessionInfo) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *TrainerSessionInfo) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *TrainerSessionInfo) GetLastOnlineTs() int64 {
	if x != nil {
		return x.LastOnlineTs
	}
	return 0
}

type TrainerFollowPoke struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Pokes         []*TrainerFollowPokeInfo `protobuf:"bytes,1,rep,name=pokes,proto3" json:"pokes,omitempty"`
	Ride          TrainerRideType          `protobuf:"varint,2,opt,name=ride,proto3,enum=MainServer.TrainerRideType" json:"ride,omitempty"` //乘骑
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerFollowPoke) Reset() {
	*x = TrainerFollowPoke{}
	mi := &file_MainServer_Trainer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerFollowPoke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerFollowPoke) ProtoMessage() {}

func (x *TrainerFollowPoke) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerFollowPoke.ProtoReflect.Descriptor instead.
func (*TrainerFollowPoke) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{8}
}

func (x *TrainerFollowPoke) GetPokes() []*TrainerFollowPokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *TrainerFollowPoke) GetRide() TrainerRideType {
	if x != nil {
		return x.Ride
	}
	return TrainerRideType_Ride_None
}

type TrainerFollowPokeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Shiny         int32                  `protobuf:"varint,3,opt,name=shiny,proto3" json:"shiny,omitempty"`
	Gender        Gender                 `protobuf:"varint,4,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerFollowPokeInfo) Reset() {
	*x = TrainerFollowPokeInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerFollowPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerFollowPokeInfo) ProtoMessage() {}

func (x *TrainerFollowPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerFollowPokeInfo.ProtoReflect.Descriptor instead.
func (*TrainerFollowPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{9}
}

func (x *TrainerFollowPokeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerFollowPokeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrainerFollowPokeInfo) GetShiny() int32 {
	if x != nil {
		return x.Shiny
	}
	return 0
}

func (x *TrainerFollowPokeInfo) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

type TrainerLocInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Loc   *TrainerLoc            `protobuf:"bytes,1,opt,name=loc,proto3" json:"loc,omitempty"`
	// repeated TrainerLoc locLine = 2;
	// float speed = 3;
	PcName        string `protobuf:"bytes,4,opt,name=pc_name,json=pcName,proto3" json:"pc_name,omitempty"`
	LastUpdateTs  int64  `protobuf:"varint,5,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"` // AoiUploadLoc aoiUploadLoc = 6; //上一个位置信息，//不存入数据库 //位置信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerLocInfo) Reset() {
	*x = TrainerLocInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerLocInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLocInfo) ProtoMessage() {}

func (x *TrainerLocInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLocInfo.ProtoReflect.Descriptor instead.
func (*TrainerLocInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{10}
}

func (x *TrainerLocInfo) GetLoc() *TrainerLoc {
	if x != nil {
		return x.Loc
	}
	return nil
}

func (x *TrainerLocInfo) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *TrainerLocInfo) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

type PartyInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Trainers         map[int64]*Trainer     `protobuf:"bytes,1,rep,name=trainers,proto3" json:"trainers,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	PartyId          string                 `protobuf:"bytes,2,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	Leader           *Trainer               `protobuf:"bytes,3,opt,name=leader,proto3" json:"leader,omitempty"`
	CreateTs         int64                  `protobuf:"varint,4,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	UpdateTs         int64                  `protobuf:"varint,5,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	LockBattleChoice bool                   `protobuf:"varint,6,opt,name=lock_battle_choice,json=lockBattleChoice,proto3" json:"lock_battle_choice,omitempty"`
	TidMax_Index     int32                  `protobuf:"varint,7,opt,name=tid_max_Index,json=tidMaxIndex,proto3" json:"tid_max_Index,omitempty"`
	TrainersIndexMap map[int64]int32        `protobuf:"bytes,8,rep,name=trainers_index_map,json=trainersIndexMap,proto3" json:"trainers_index_map,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PartyInfo) Reset() {
	*x = PartyInfo{}
	mi := &file_MainServer_Trainer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PartyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyInfo) ProtoMessage() {}

func (x *PartyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyInfo.ProtoReflect.Descriptor instead.
func (*PartyInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{11}
}

func (x *PartyInfo) GetTrainers() map[int64]*Trainer {
	if x != nil {
		return x.Trainers
	}
	return nil
}

func (x *PartyInfo) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyInfo) GetLeader() *Trainer {
	if x != nil {
		return x.Leader
	}
	return nil
}

func (x *PartyInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PartyInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PartyInfo) GetLockBattleChoice() bool {
	if x != nil {
		return x.LockBattleChoice
	}
	return false
}

func (x *PartyInfo) GetTidMax_Index() int32 {
	if x != nil {
		return x.TidMax_Index
	}
	return 0
}

func (x *PartyInfo) GetTrainersIndexMap() map[int64]int32 {
	if x != nil {
		return x.TrainersIndexMap
	}
	return nil
}

var File_MainServer_Trainer_proto protoreflect.FileDescriptor

const file_MainServer_Trainer_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/Trainer.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\x1a\x1dMainServer/TrainerCloth.proto\x1a\x1cMainServer/TrainerTeam.proto\x1a\x1dMainServer/TrainerBadge.proto\x1a\x1eMainServer/TrainerStrict.proto\x1a\x18MainServer/LocInfo.proto\x1a\x1aMainServer/Equipment.proto\"\xd0\b\n" +
	"\aTrainer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\tR\x03uid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12*\n" +
	"\x06gender\x18\x04 \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12>\n" +
	"\vaction_info\x18\x05 \x01(\v2\x1d.MainServer.TrainerActionInfoR\n" +
	"actionInfo\x12\x19\n" +
	"\bpoke_ids\x18\x06 \x03(\tR\apokeIds\x124\n" +
	"\x05items\x18\b \x03(\v2\x1e.MainServer.Trainer.ItemsEntryR\x05items\x121\n" +
	"\x06badges\x18\t \x01(\v2\x19.MainServer.TrainerBadgesR\x06badges\x12+\n" +
	"\x04team\x18\n" +
	" \x01(\x0e2\x17.MainServer.TrainerTeamR\x04team\x12\x19\n" +
	"\bgroup_id\x18\v \x01(\tR\agroupId\x12.\n" +
	"\x05cloth\x18\f \x01(\v2\x18.MainServer.TrainerClothR\x05cloth\x12\x12\n" +
	"\x04coin\x18\r \x01(\x03R\x04coin\x12:\n" +
	"\tteam_info\x18\x0e \x01(\v2\x1d.MainServer.TrainerOnTeamInfoR\bteamInfo\x12!\n" +
	"\fspecial_coin\x18\x0f \x01(\x03R\vspecialCoin\x12>\n" +
	"\vfollow_poke\x18\x10 \x01(\v2\x1d.MainServer.TrainerFollowPokeR\n" +
	"followPoke\x12\x1b\n" +
	"\tcreate_ts\x18\x11 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x12 \x01(\x03R\bupdateTs\x12\x1f\n" +
	"\vonline_time\x18\x13 \x01(\x03R\n" +
	"onlineTime\x12D\n" +
	"\rspecial_right\x18\x14 \x01(\x0e2\x1f.MainServer.TrainerSpecialRightR\fspecialRight\x12;\n" +
	"\n" +
	"box_status\x18\x15 \x01(\v2\x1c.MainServer.TrainerBoxStatusR\tboxStatus\x12>\n" +
	"\vstrict_info\x18\x16 \x01(\v2\x1d.MainServer.TrainerStrictInfoR\n" +
	"strictInfo\x12=\n" +
	"\n" +
	"decoration\x18\x17 \x01(\v2\x1d.MainServer.TrainerDecorationR\n" +
	"decoration\x12@\n" +
	"\vsessionInfo\x18\x18 \x01(\v2\x1e.MainServer.TrainerSessionInfoR\vsessionInfo\x1aU\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x121\n" +
	"\x05value\x18\x02 \x01(\v2\x1b.MainServer.TrainerItemInfoR\x05value:\x028\x01\"\xa1\x02\n" +
	"\x11TrainerActionInfo\x12(\n" +
	"\x03loc\x18\x01 \x01(\v2\x16.MainServer.TrainerLocR\x03loc\x12L\n" +
	"\x0elast_main_land\x18\x02 \x01(\v2&.MainServer.TrainerLastMainLandLocInfoR\flastMainLand\x125\n" +
	"\x06action\x18\x03 \x01(\x0e2\x1d.MainServer.TrainerActionTypeR\x06action\x12\x1f\n" +
	"\vis_instance\x18\x04 \x01(\bR\n" +
	"isInstance\x12\x1f\n" +
	"\vinstance_id\x18\x05 \x01(\tR\n" +
	"instanceId\x12\x1b\n" +
	"\tupdate_ts\x18\x06 \x01(\x03R\bupdateTs\"f\n" +
	"\x1aTrainerLastMainLandLocInfo\x12(\n" +
	"\x03loc\x18\x01 \x01(\v2\x16.MainServer.TrainerLocR\x03loc\x12\x1e\n" +
	"\n" +
	"lastPcName\x18\x02 \x01(\tR\n" +
	"lastPcName\"\xd9\x03\n" +
	"\x11TrainerDecoration\x12I\n" +
	"\x0fequipment_title\x18\x01 \x01(\v2 .MainServer.TrainerEquipmentInfoR\x0eequipmentTitle\x12G\n" +
	"\x0eequipment_card\x18\x02 \x01(\v2 .MainServer.TrainerEquipmentInfoR\requipmentCard\x12G\n" +
	"\x0eequipment_ride\x18\x03 \x01(\v2 .MainServer.TrainerEquipmentInfoR\requipmentRide\x12O\n" +
	"\x12equipment_pokeball\x18\x04 \x01(\v2 .MainServer.TrainerEquipmentInfoR\x11equipmentPokeball\x12I\n" +
	"\x0fequipment_badge\x18\x05 \x01(\v2 .MainServer.TrainerEquipmentInfoR\x0eequipmentBadge\x12K\n" +
	"\x10equipment_amulet\x18\x06 \x01(\v2 .MainServer.TrainerEquipmentInfoR\x0fequipmentAmulet\"\xf8\x01\n" +
	"\x14TrainerEquipmentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12%\n" +
	"\x0eequipment_name\x18\x02 \x01(\tR\requipmentName\x12@\n" +
	"\x0eequipment_type\x18\x03 \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\x12\"\n" +
	"\ffortifyCount\x18\x04 \x01(\x05R\ffortifyCount\x12C\n" +
	"\veffect_info\x18\x05 \x01(\v2\".MainServer.TrainerEquipmentEffectR\n" +
	"effectInfo\"C\n" +
	"\x0fTrainerItemInfo\x12\x14\n" +
	"\x05useTs\x18\x01 \x01(\x03R\x05useTs\x12\x1a\n" +
	"\bexpireTs\x18\x02 \x01(\x03R\bexpireTs\"\x8a\x01\n" +
	"\x10TrainerBoxStatus\x12!\n" +
	"\factive_boxes\x18\x01 \x01(\x05R\vactiveBoxes\x120\n" +
	"\x14special_active_boxes\x18\x02 \x01(\x05R\x12specialActiveBoxes\x12!\n" +
	"\finit_version\x18\x03 \x01(\x05R\vinitVersion\"\xfe\x01\n" +
	"\x12TrainerSessionInfo\x122\n" +
	"\vbattlePokes\x18\x01 \x03(\v2\x10.MainServer.PokeR\vbattlePokes\x12$\n" +
	"\x0esession_end_ts\x18\x02 \x01(\x03R\fsessionEndTs\x124\n" +
	"\alocInfo\x18\x03 \x01(\v2\x1a.MainServer.TrainerLocInfoR\alocInfo\x12\x18\n" +
	"\amatchId\x18\x04 \x01(\tR\amatchId\x12\x18\n" +
	"\apartyId\x18\x05 \x01(\tR\apartyId\x12$\n" +
	"\x0elast_online_ts\x18\x06 \x01(\x03R\flastOnlineTs\"}\n" +
	"\x11TrainerFollowPoke\x127\n" +
	"\x05pokes\x18\x01 \x03(\v2!.MainServer.TrainerFollowPokeInfoR\x05pokes\x12/\n" +
	"\x04ride\x18\x02 \x01(\x0e2\x1b.MainServer.TrainerRideTypeR\x04ride\"}\n" +
	"\x15TrainerFollowPokeInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05shiny\x18\x03 \x01(\x05R\x05shiny\x12*\n" +
	"\x06gender\x18\x04 \x01(\x0e2\x12.MainServer.GenderR\x06gender\"y\n" +
	"\x0eTrainerLocInfo\x12(\n" +
	"\x03loc\x18\x01 \x01(\v2\x16.MainServer.TrainerLocR\x03loc\x12\x17\n" +
	"\apc_name\x18\x04 \x01(\tR\x06pcName\x12$\n" +
	"\x0elast_update_ts\x18\x05 \x01(\x03R\flastUpdateTs\"\x92\x04\n" +
	"\tPartyInfo\x12?\n" +
	"\btrainers\x18\x01 \x03(\v2#.MainServer.PartyInfo.TrainersEntryR\btrainers\x12\x19\n" +
	"\bparty_id\x18\x02 \x01(\tR\apartyId\x12+\n" +
	"\x06leader\x18\x03 \x01(\v2\x13.MainServer.TrainerR\x06leader\x12\x1b\n" +
	"\tcreate_ts\x18\x04 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x05 \x01(\x03R\bupdateTs\x12,\n" +
	"\x12lock_battle_choice\x18\x06 \x01(\bR\x10lockBattleChoice\x12\"\n" +
	"\rtid_max_Index\x18\a \x01(\x05R\vtidMaxIndex\x12Y\n" +
	"\x12trainers_index_map\x18\b \x03(\v2+.MainServer.PartyInfo.TrainersIndexMapEntryR\x10trainersIndexMap\x1aP\n" +
	"\rTrainersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12)\n" +
	"\x05value\x18\x02 \x01(\v2\x13.MainServer.TrainerR\x05value:\x028\x01\x1aC\n" +
	"\x15TrainersIndexMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01*J\n" +
	"\x13TrainerSpecialRight\x12\x0f\n" +
	"\vTrainerNone\x10\x00\x12\x0e\n" +
	"\n" +
	"TrainerVip\x10\x01\x12\x12\n" +
	"\x0eTrainerPremium\x10\x02*)\n" +
	"\x11TrainerActionType\x12\b\n" +
	"\x04idle\x10\x00\x12\n" +
	"\n" +
	"\x06battle\x10\x01*5\n" +
	"\x0fTrainerRideType\x12\r\n" +
	"\tRide_None\x10\x00\x12\x13\n" +
	"\x0fRide_NormalBike\x10\x01B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Trainer_proto_rawDescOnce sync.Once
	file_MainServer_Trainer_proto_rawDescData []byte
)

func file_MainServer_Trainer_proto_rawDescGZIP() []byte {
	file_MainServer_Trainer_proto_rawDescOnce.Do(func() {
		file_MainServer_Trainer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Trainer_proto_rawDesc), len(file_MainServer_Trainer_proto_rawDesc)))
	})
	return file_MainServer_Trainer_proto_rawDescData
}

var file_MainServer_Trainer_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_MainServer_Trainer_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_MainServer_Trainer_proto_goTypes = []any{
	(TrainerSpecialRight)(0),           // 0: MainServer.TrainerSpecialRight
	(TrainerActionType)(0),             // 1: MainServer.TrainerActionType
	(TrainerRideType)(0),               // 2: MainServer.TrainerRideType
	(*Trainer)(nil),                    // 3: MainServer.Trainer
	(*TrainerActionInfo)(nil),          // 4: MainServer.TrainerActionInfo
	(*TrainerLastMainLandLocInfo)(nil), // 5: MainServer.TrainerLastMainLandLocInfo
	(*TrainerDecoration)(nil),          // 6: MainServer.TrainerDecoration
	(*TrainerEquipmentInfo)(nil),       // 7: MainServer.TrainerEquipmentInfo
	(*TrainerItemInfo)(nil),            // 8: MainServer.TrainerItemInfo
	(*TrainerBoxStatus)(nil),           // 9: MainServer.TrainerBoxStatus
	(*TrainerSessionInfo)(nil),         // 10: MainServer.TrainerSessionInfo
	(*TrainerFollowPoke)(nil),          // 11: MainServer.TrainerFollowPoke
	(*TrainerFollowPokeInfo)(nil),      // 12: MainServer.TrainerFollowPokeInfo
	(*TrainerLocInfo)(nil),             // 13: MainServer.TrainerLocInfo
	(*PartyInfo)(nil),                  // 14: MainServer.PartyInfo
	nil,                                // 15: MainServer.Trainer.ItemsEntry
	nil,                                // 16: MainServer.PartyInfo.TrainersEntry
	nil,                                // 17: MainServer.PartyInfo.TrainersIndexMapEntry
	(Gender)(0),                        // 18: MainServer.Gender
	(*TrainerBadges)(nil),              // 19: MainServer.TrainerBadges
	(TrainerTeam)(0),                   // 20: MainServer.TrainerTeam
	(*TrainerCloth)(nil),               // 21: MainServer.TrainerCloth
	(*TrainerOnTeamInfo)(nil),          // 22: MainServer.TrainerOnTeamInfo
	(*TrainerStrictInfo)(nil),          // 23: MainServer.TrainerStrictInfo
	(*TrainerLoc)(nil),                 // 24: MainServer.TrainerLoc
	(EquipmentType)(0),                 // 25: MainServer.EquipmentType
	(*TrainerEquipmentEffect)(nil),     // 26: MainServer.TrainerEquipmentEffect
	(*Poke)(nil),                       // 27: MainServer.Poke
}
var file_MainServer_Trainer_proto_depIdxs = []int32{
	18, // 0: MainServer.Trainer.gender:type_name -> MainServer.Gender
	4,  // 1: MainServer.Trainer.action_info:type_name -> MainServer.TrainerActionInfo
	15, // 2: MainServer.Trainer.items:type_name -> MainServer.Trainer.ItemsEntry
	19, // 3: MainServer.Trainer.badges:type_name -> MainServer.TrainerBadges
	20, // 4: MainServer.Trainer.team:type_name -> MainServer.TrainerTeam
	21, // 5: MainServer.Trainer.cloth:type_name -> MainServer.TrainerCloth
	22, // 6: MainServer.Trainer.team_info:type_name -> MainServer.TrainerOnTeamInfo
	11, // 7: MainServer.Trainer.follow_poke:type_name -> MainServer.TrainerFollowPoke
	0,  // 8: MainServer.Trainer.special_right:type_name -> MainServer.TrainerSpecialRight
	9,  // 9: MainServer.Trainer.box_status:type_name -> MainServer.TrainerBoxStatus
	23, // 10: MainServer.Trainer.strict_info:type_name -> MainServer.TrainerStrictInfo
	6,  // 11: MainServer.Trainer.decoration:type_name -> MainServer.TrainerDecoration
	10, // 12: MainServer.Trainer.sessionInfo:type_name -> MainServer.TrainerSessionInfo
	24, // 13: MainServer.TrainerActionInfo.loc:type_name -> MainServer.TrainerLoc
	5,  // 14: MainServer.TrainerActionInfo.last_main_land:type_name -> MainServer.TrainerLastMainLandLocInfo
	1,  // 15: MainServer.TrainerActionInfo.action:type_name -> MainServer.TrainerActionType
	24, // 16: MainServer.TrainerLastMainLandLocInfo.loc:type_name -> MainServer.TrainerLoc
	7,  // 17: MainServer.TrainerDecoration.equipment_title:type_name -> MainServer.TrainerEquipmentInfo
	7,  // 18: MainServer.TrainerDecoration.equipment_card:type_name -> MainServer.TrainerEquipmentInfo
	7,  // 19: MainServer.TrainerDecoration.equipment_ride:type_name -> MainServer.TrainerEquipmentInfo
	7,  // 20: MainServer.TrainerDecoration.equipment_pokeball:type_name -> MainServer.TrainerEquipmentInfo
	7,  // 21: MainServer.TrainerDecoration.equipment_badge:type_name -> MainServer.TrainerEquipmentInfo
	7,  // 22: MainServer.TrainerDecoration.equipment_amulet:type_name -> MainServer.TrainerEquipmentInfo
	25, // 23: MainServer.TrainerEquipmentInfo.equipment_type:type_name -> MainServer.EquipmentType
	26, // 24: MainServer.TrainerEquipmentInfo.effect_info:type_name -> MainServer.TrainerEquipmentEffect
	27, // 25: MainServer.TrainerSessionInfo.battlePokes:type_name -> MainServer.Poke
	13, // 26: MainServer.TrainerSessionInfo.locInfo:type_name -> MainServer.TrainerLocInfo
	12, // 27: MainServer.TrainerFollowPoke.pokes:type_name -> MainServer.TrainerFollowPokeInfo
	2,  // 28: MainServer.TrainerFollowPoke.ride:type_name -> MainServer.TrainerRideType
	18, // 29: MainServer.TrainerFollowPokeInfo.gender:type_name -> MainServer.Gender
	24, // 30: MainServer.TrainerLocInfo.loc:type_name -> MainServer.TrainerLoc
	16, // 31: MainServer.PartyInfo.trainers:type_name -> MainServer.PartyInfo.TrainersEntry
	3,  // 32: MainServer.PartyInfo.leader:type_name -> MainServer.Trainer
	17, // 33: MainServer.PartyInfo.trainers_index_map:type_name -> MainServer.PartyInfo.TrainersIndexMapEntry
	8,  // 34: MainServer.Trainer.ItemsEntry.value:type_name -> MainServer.TrainerItemInfo
	3,  // 35: MainServer.PartyInfo.TrainersEntry.value:type_name -> MainServer.Trainer
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_MainServer_Trainer_proto_init() }
func file_MainServer_Trainer_proto_init() {
	if File_MainServer_Trainer_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_TrainerBadge_proto_init()
	file_MainServer_TrainerStrict_proto_init()
	file_MainServer_LocInfo_proto_init()
	file_MainServer_Equipment_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Trainer_proto_rawDesc), len(file_MainServer_Trainer_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Trainer_proto_goTypes,
		DependencyIndexes: file_MainServer_Trainer_proto_depIdxs,
		EnumInfos:         file_MainServer_Trainer_proto_enumTypes,
		MessageInfos:      file_MainServer_Trainer_proto_msgTypes,
	}.Build()
	File_MainServer_Trainer_proto = out.File
	file_MainServer_Trainer_proto_goTypes = nil
	file_MainServer_Trainer_proto_depIdxs = nil
}
