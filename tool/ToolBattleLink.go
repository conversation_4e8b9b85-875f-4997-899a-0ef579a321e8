package tool

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

var battleLink ToolBattleLink

func InitBattleLink(toolBattleLink ToolBattleLink) {
	battleLink = toolBattleLink
}
func GetBattleLink() ToolBattleLink {
	return battleLink
}

type ToolBattleLink interface {
	TryBattlePrepare(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, trainerInfo *MainServer.Trainer, prepareInfo *MainServer.BattlePrepare, moreConfigInfo *MainServer.BattleMoreConfigInfo) (string, error)
}
