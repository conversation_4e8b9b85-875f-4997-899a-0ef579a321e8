package tool

import (
	"go-nakama-poke/proto/MainServer"
	"sync"
)

// 定义 PartyInfo 结构体
//
//	type PartyInfo struct {
//		// Tids             []int64
//		Trainers         map[int64]*MainServer.Trainer
//		PartyId          string
//		Leader           *MainServer.Trainer
//		CreateTs         int64
//		UpdateTs         int64
//		LockBattleChoice bool
//		// HasSync          bool //当有人离线后需要更新
//		// BattleID         string
//		// LeaveTrainers    []*MainServer.Trainer
//		// JoinTrainers     []*MainServer.Trainer
//		// PartAPokes       []*MainServer.Poke
//		// PartBPokes       []*MainServer.Poke
//		// PlayerIndex int
//	}
type BattleInfo struct {
	PartyInfos           map[string]*MainServer.PartyInfo
	BattleID             string
	MatchLabel           *MainServer.BattleMatchLabel
	BattleServerId       string
	BattleServerMessages chan *MainServer.BattleServerMessage
	CreateTrainer        *MainServer.Trainer
	BattlePrepare        *MainServer.BattlePrepare
	// BattleSummonPokes    []*MainServer.Poke
	BattlePokes      map[int64]map[int64]*MainServer.Poke // key pokeId
	CreateTs         int64
	UpdateTs         int64
	LockBattleChoice bool
	BattleReward     *MainServer.RewardInfo
	MoreConfigInfo   *MainServer.BattleMoreConfigInfo
}

// 定义线程安全的 battleMap
type SafeBattleInfoMap struct {
	data map[string]*BattleInfo
	lock sync.RWMutex
}
type SafePartyInfoMap struct {
	//用uid作为key
	data map[int64]*MainServer.PartyInfo
	lock sync.RWMutex
}

// 创建一个新的 SafeBattleMap
func NewSafeBattleInfoMap() *SafeBattleInfoMap {
	return &SafeBattleInfoMap{
		data: make(map[string]*BattleInfo),
	}
}

// 设置值 //用matchId
func (bm *SafeBattleInfoMap) Set(key string, info *BattleInfo) {
	bm.lock.Lock()
	defer bm.lock.Unlock()
	bm.data[key] = info
}

// 获取值
func (bm *SafeBattleInfoMap) Get(key string) (*BattleInfo, bool) {
	bm.lock.RLock()
	defer bm.lock.RUnlock()
	info, exists := bm.data[key]
	return info, exists
}

// 移除值
func (bm *SafeBattleInfoMap) Remove(key string) {
	bm.lock.Lock()
	defer bm.lock.Unlock()
	delete(bm.data, key)
}

func NewSafePartyInfoMap() *SafePartyInfoMap {
	return &SafePartyInfoMap{
		data: make(map[int64]*MainServer.PartyInfo),
	}
}

// 设置值
func (bm *SafePartyInfoMap) Set(key int64, info *MainServer.PartyInfo) {
	bm.lock.Lock()
	defer bm.lock.Unlock()
	bm.data[key] = info
}

// 获取值 //用uid作为key
func (bm *SafePartyInfoMap) Get(key int64) (*MainServer.PartyInfo, bool) {
	bm.lock.RLock()
	defer bm.lock.RUnlock()
	info, exists := bm.data[key]
	return info, exists
}

// 移除值
func (bm *SafePartyInfoMap) Remove(key int64) {
	bm.lock.Lock()
	defer bm.lock.Unlock()
	delete(bm.data, key)
}

func (bm *SafePartyInfoMap) RemovePartyInfo(key int64) {
	bm.lock.Lock()
	defer bm.lock.Unlock()
	info, exists := bm.data[key]
	if !exists {
		return
	}
	delete(bm.data, key)
	for _, v := range info.Trainers {
		delete(bm.data, v.Id)
	}
}

// 通过 key 获取到的 partyInfo 中的 party 成员，遍历所有 party，合并结果
// func (bm *SafeBattleInfoMap) GetInfoByAllParty(key string) []PartyInfo {
// 	bm.lock.RLock()
// 	defer bm.lock.RUnlock()

// 	// 查找 key 对应的 partyInfo
// 	baseInfo, exists := bm.data[key]
// 	if !exists {
// 		return nil
// 	}

// 	// 创建结果数组
// 	result := []PartyInfo{*baseInfo}

// 	// 遍历所有 party 成员，找到匹配的 partyInfo
// 	for _, member := range baseInfo.Tids {
// 		for k, info := range bm.data {
// 			if k != key && ContainsInt(info.Tids, member) {
// 				result = append(result, *info)
// 			}
// 		}
// 	}

// 	return result
// }

// 互相包含检查
// func (bm *SafeBattleInfoMap) ArePartiesMutuallyContaining(key string) bool {
// 	bm.lock.RLock()
// 	defer bm.lock.RUnlock()

// 	// 获取 baseInfo
// 	baseInfo, exists := bm.data[key]
// 	if !exists {
// 		return false // 如果 key 不存在，直接返回 false
// 	}

// 	// 获取 baseInfo 的所有成员
// 	baseParty := baseInfo.Tids

// 	// 遍历 baseParty 中每个成员的 key
// 	for _, memberKey := range baseParty {
// 		// 获取当前 memberKey 对应的 partyInfo
// 		memberInfo, memberExists := bm.data[memberKey]
// 		if !memberExists {
// 			return false // 如果某个成员不存在，直接返回 false
// 		}

// 		// 检查 memberInfo.Party 是否完全包含 baseParty 和 key 本身
// 		if !areSlicesMutuallyContaining(baseParty, memberInfo.Uids, key) {
// 			return false
// 		}
// 	}

// 	// 如果所有成员的 party 都符合条件，返回 true
// 	return true
// }

// 判断两个切片是否互相包含，且包括一个额外的 key
// func areSlicesMutuallyContaining(base []string, member []string, key string) bool {
// 	// 确保 key 在 member 中
// 	if !Contains(member, key) {
// 		return false
// 	}

// 	// 确保 member 完全包含 base
// 	for _, b := range base {
// 		if !Contains(member, b) {
// 			return false
// 		}
// 	}

// 	// 确保 base 完全包含 member（除了 key 本身）
// 	for _, m := range member {
// 		if m != key && !Contains(base, m) {
// 			return false
// 		}
// 	}

// 	return true
// }

// 辅助函数：判断一个字符串数组是否包含指定元素
func Contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func ContainsInt(slice []int64, item int64) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// 全局 SafeBattleMap 实例
var (
	globalBattleMap *SafeBattleInfoMap
	globalPartyMap  *SafePartyInfoMap
	once            sync.Once
)

// 初始化全局变量（线程安全，确保只初始化一次）
func InitGlobalBattleMap() {
	once.Do(func() {
		globalBattleMap = NewSafeBattleInfoMap()
		globalPartyMap = NewSafePartyInfoMap()
	})
}

// 获取全局 SafeBattleMap 的实例
func GetGlobalBattleMap() *SafeBattleInfoMap {
	return globalBattleMap
}

func GetGlobalPartyMap() *SafePartyInfoMap {
	return globalPartyMap
}
