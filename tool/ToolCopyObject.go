package tool

import (
	"go-nakama-poke/proto/MainServer"

	"google.golang.org/protobuf/proto"
)

func SafePoke(poke *MainServer.Poke) *MainServer.Poke {
	if poke == nil {
		return nil
	}
	if poke.Egg {
		poke.Evs = &MainServer.PokeStat{}
		poke.Ivs = &MainServer.PokeStat{}
		poke.Moves = []*MainServer.PokeSimpleMove{}
		poke.HonorInfo = &MainServer.HonorInfo{}
		poke.Nature = MainServer.Nature_NATURE_UNSPECIFIED
		poke.Ability = ""
		poke.Gender = MainServer.Gender_GenderNull
		poke.Shiny = 0
	}
	return poke
}
func CopySafeTrainer(trainer *MainServer.Trainer) *MainServer.Trainer {
	if trainer == nil {
		return nil
	}
	// 深拷贝 trainer 对象
	desensitizedTrainer := &MainServer.Trainer{
		Id:     trainer.Id,
		Uid:    trainer.Uid,
		Name:   trainer.Name,
		Gender: trainer.Gender,
		ActionInfo: &MainServer.TrainerActionInfo{
			Loc:    trainer.ActionInfo.Loc,
			Action: trainer.ActionInfo.Action,
		},
		PokeIds:      make([]string, 0),                            // 初始化切片
		Items:        make(map[string]*MainServer.TrainerItemInfo), // 初始化映射
		Badges:       &MainServer.TrainerBadges{},                  // 初始化切片
		Team:         trainer.Team,
		GroupId:      trainer.GroupId,
		Cloth:        trainer.Cloth,                   // 假设 TrainerCloth 是简单结构体
		Coin:         0,                               // 重置为 0
		TeamInfo:     &MainServer.TrainerOnTeamInfo{}, // 重置为 0
		SpecialCoin:  0,                               // 重置为 0
		FollowPoke:   trainer.FollowPoke,              // 假设 TrainerFollowPoke 是简单结构体
		CreateTs:     0,
		UpdateTs:     0,
		SpecialRight: MainServer.TrainerSpecialRight_TrainerNone,
		BoxStatus:    &MainServer.TrainerBoxStatus{},
		SessionInfo: &MainServer.TrainerSessionInfo{
			SessionEndTs: trainer.SessionInfo.SessionEndTs,
			LocInfo:      proto.Clone(trainer.SessionInfo.LocInfo).(*MainServer.TrainerLocInfo),
			MatchId:      trainer.SessionInfo.MatchId,
			// BattlePokes:  make([]*MainServer.Poke, 0),
		},
	}

	// 深拷贝切片字段
	// copy(desensitizedTrainer.PokeIds, trainer.PokeIds)
	// 深拷贝映射字段
	// for key, value := range trainer.Items {
	// 	desensitizedTrainer.Items[key] = value
	// }
	return desensitizedTrainer
}
func CopySafeBattleTrainer(trainer *MainServer.Trainer) *MainServer.Trainer {
	desensitizedTrainer := CopySafeTrainer(trainer)
	desensitizedTrainer.FollowPoke = nil
	desensitizedTrainer.ActionInfo = nil
	desensitizedTrainer.SessionInfo.LocInfo = nil
	// 深拷贝切片字段
	// copy(desensitizedTrainer.PokeIds, trainer.PokeIds)
	// 深拷贝映射字段
	// for key, value := range trainer.Items {
	// 	desensitizedTrainer.Items[key] = value
	// }
	return desensitizedTrainer
}
