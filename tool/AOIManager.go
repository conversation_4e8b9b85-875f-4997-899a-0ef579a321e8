package tool

import (
	"context"
	"fmt"
	"go-nakama-poke/config"
	"go-nakama-poke/proto/MainServer"
	"math"
	"sync"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// -------------------- 公共类型 --------------------

const DefaultChunkSize = 32

// Position 精度使用 float32（内部处理），protobuf 可使用 float32 传输以节省带宽
// type Position struct {
// 	X float32 `json:"x"`
// 	Y float32 `json:"y"`
// 	Z float32 `json:"z"`
// }

// PlayerInfo 对外暴露/广播用
// type PlayerInfo struct {
// 	ID int64    `json:"id"`
// 	P  Position `json:"pos"`
// 	// 可以扩展额外字段（方向、动作、额外元数据），逐步添加
// }

// -------------------- 内部实现 --------------------

// 玩家内部结构
type player struct {
	id int64
	// pos    Position
	chunkX int
	chunkZ int
	state  *MainServer.TrainerLoc
}

// 每个地图单独的 AOI
type mapAOI struct {
	chunkSize int

	// chunkKey -> map[playerID]*player
	chunks map[string]map[int64]*player
	// playerID -> *player
	players map[int64]*player

	lock sync.RWMutex
}

func newMapAOI(chunkSize int) *mapAOI {
	return &mapAOI{
		chunkSize: chunkSize,
		chunks:    make(map[string]map[int64]*player),
		players:   make(map[int64]*player),
	}
}

func (m *mapAOI) worldToChunk(x float32) int {
	return int(math.Floor(float64(x / float32(m.chunkSize))))
}

func chunkKey(cx, cz int) string {
	return fmt.Sprintf("%d_%d", cx, cz)
}

// add or update (within same map)
func (m *mapAOI) addOrUpdatePlayer(id int64, pos *MainServer.TrainerLoc) (isNew bool, movedChunk bool) {
	m.lock.Lock()
	defer m.lock.Unlock()

	cx := m.worldToChunk(pos.X)
	cz := m.worldToChunk(pos.Z)
	key := chunkKey(cx, cz)

	if p, ok := m.players[id]; ok {
		oldKey := chunkKey(p.chunkX, p.chunkZ)
		p.state = pos
		if oldKey != key {
			// move between chunks inside same map
			if bucket, ok2 := m.chunks[oldKey]; ok2 {
				delete(bucket, id)
				if len(bucket) == 0 {
					delete(m.chunks, oldKey)
				}
			}
			if _, ok3 := m.chunks[key]; !ok3 {
				m.chunks[key] = make(map[int64]*player)
			}
			m.chunks[key][id] = p
			p.chunkX = cx
			p.chunkZ = cz
			return false, true
		}
		p.chunkX = cx
		p.chunkZ = cz
		return false, false
	}

	// new player
	p := &player{id: id, state: pos, chunkX: cx, chunkZ: cz}
	m.players[id] = p
	if _, ok := m.chunks[key]; !ok {
		m.chunks[key] = make(map[int64]*player)
	}
	m.chunks[key][id] = p
	return true, false
}

func (m *mapAOI) removePlayer(id int64) {
	m.lock.Lock()
	defer m.lock.Unlock()
	if p, ok := m.players[id]; ok {
		k := chunkKey(p.chunkX, p.chunkZ)
		if bucket, ok2 := m.chunks[k]; ok2 {
			delete(bucket, id)
			if len(bucket) == 0 {
				delete(m.chunks, k)
			}
		}
		delete(m.players, id)
	}
}

// 获取指定 player 周围 9 个块的玩家（相同 map 内）
func (m *mapAOI) getNearbyPlayers(id int64, excludeSelf bool, maxResults int) []*MainServer.TrainerLoc {
	m.lock.RLock()
	defer m.lock.RUnlock()

	p, ok := m.players[id]
	if !ok {
		return nil
	}
	cx := p.chunkX
	cz := p.chunkZ

	results := make([]*MainServer.TrainerLoc, 0, 32)
	added := 0

	for dx := -1; dx <= 1; dx++ {
		for dz := -1; dz <= 1; dz++ {
			k := chunkKey(cx+dx, cz+dz)
			if bucket, ok := m.chunks[k]; ok {
				for pid, pp := range bucket {
					if excludeSelf && pid == id {
						continue
					}
					//这段时间内没有更新就不同步了
					// if time.Now().UnixMilli()-pp.state.TsMs > config.TrainerUpdateInterval {
					// 	continue
					// }
					results = append(results, pp.state)
					added++
					if maxResults > 0 && added >= maxResults {
						return results
					}
				}
			}
		}
	}
	return results
}

// func (m *mapAOI) getNearbyPlayers(id int64, excludeSelf bool, maxResults int) []*MainServer.TrainerLoc {
// 	m.lock.RLock()
// 	p, ok := m.players[id]
// 	if !ok {
// 		m.lock.RUnlock()
// 		return nil
// 	}
// 	cx := p.chunkX
// 	cz := p.chunkZ
// 	m.lock.RUnlock()

// 	results := make([]*MainServer.TrainerLoc, 0, 32)
// 	added := 0

// 	m.lock.RLock()
// 	defer m.lock.RUnlock()
// 	for dx := -1; dx <= 1; dx++ {
// 		for dz := -1; dz <= 1; dz++ {
// 			k := chunkKey(cx+dx, cz+dz)
// 			if bucket, ok := m.chunks[k]; ok {
// 				for pid, pp := range bucket {
// 					if excludeSelf && pid == id {
// 						continue
// 					}
// 					results = append(results, pp.state)
// 					added++
// 					if maxResults > 0 && added >= maxResults {
// 						return results
// 					}
// 				}
// 			}
// 		}
// 	}
// 	return results
// }

// 根据任意位置获取 9 块范围内玩家（用于广播给某个位置）
func (m *mapAOI) getNearbyForPosition(pos *MainServer.TrainerLoc, excludeID int64, maxResults int) []*MainServer.TrainerLoc {
	cx := m.worldToChunk(pos.X)
	cz := m.worldToChunk(pos.Z)

	results := make([]*MainServer.TrainerLoc, 0, 32)
	added := 0

	m.lock.RLock()
	defer m.lock.RUnlock()
	for dx := -1; dx <= 1; dx++ {
		for dz := -1; dz <= 1; dz++ {
			k := chunkKey(cx+dx, cz+dz)
			if bucket, ok := m.chunks[k]; ok {
				for pid, pp := range bucket {
					if excludeID >= 0 && pid == excludeID {
						continue
					}
					results = append(results, pp.state)
					added++
					if maxResults > 0 && added >= maxResults {
						return results
					}
				}
			}
		}
	}
	return results
}

func (m *mapAOI) getTotalPlayers() int {
	m.lock.RLock()
	defer m.lock.RUnlock()
	return len(m.players)
}

// -------------------- AOIManager（对外） --------------------
type StreamInfo struct {
	mode        uint8
	label       string
	hidden      bool
	persistence bool
}

type AOIManager struct {
	defaultChunkSize int
	streamInfo       StreamInfo
	// mapId -> *mapAOI
	maps map[MainServer.MainLandType]*mapAOI

	// 记录玩家在哪个 map 中，便于跨地图移动
	playerToMap map[int64]MainServer.MainLandType
	// presences   []runtime.Presence
	// maps 和 playerToMap 的并发保护
	lock sync.RWMutex
}

var GlobalAOIManager *AOIManager

func NewAOIManager(chunkSize int) *AOIManager {
	if chunkSize <= 0 {
		chunkSize = DefaultChunkSize
	}
	streamInfo := StreamInfo{
		mode:        102,
		label:       "aoi",
		hidden:      true,
		persistence: false,
	}
	GlobalAOIManager = &AOIManager{
		defaultChunkSize: chunkSize,
		maps:             make(map[MainServer.MainLandType]*mapAOI),
		playerToMap:      make(map[int64]MainServer.MainLandType),
		streamInfo:       streamInfo,
	}
	return GlobalAOIManager
}
func StartBroadcastLoop(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule) {
	ticker := time.NewTicker(300 * time.Millisecond)

	// 并发 worker 数量（可调）
	workerCount := 8
	jobs := make(chan runtime.Presence, 1024)
	// 启动若干 worker
	for i := 0; i < workerCount; i++ {
		go func(workerId int) {
			for p := range jobs {
				// 1) 安全获取 Trainer 副本
				trainer := GetActiveTrainerByUid(p.GetUserId())
				if trainer == nil {
					continue
				}
				if time.Now().UnixMilli()-trainer.SessionInfo.SessionEndTs < config.ReconnectTime {
					RemoveActiveTrainer(trainer.Id, true)
					continue
				}
				// 2) 获取附近玩家（此调用已经是线程安全的）
				list, m := GlobalAOIManager.GetNearbyPlayersForPlayerId(trainer.Id, true, 50)
				if m == MainServer.MainLandType_MainLand_None || len(list) == 0 {
					continue
				}
				// 3) 序列化 proto -> json/bytes （你用 ProtoToJson）
				resultProto := &MainServer.AOIBroadcast{
					MainLandType: m,
					Players:      list,
				}
				result, err := ProtoToBase64(resultProto) // 若用 protobuf，直接 Marshal
				if err != nil {
					logger.Error("ProtoToJson failed: %v", err)
					continue
				}
				// 4) 发送（注意：传入单个 presence 的 slice）
				if err := nk.StreamSend(GlobalAOIManager.streamInfo.mode, "", "", GlobalAOIManager.streamInfo.label, result, []runtime.Presence{p}, false); err != nil {
					logger.Error("StreamSend failed: %v", err)
				}
			}
		}(i)
	}

	go func() {
		defer func() {
			close(jobs)
		}()
		for {
			select {
			case <-ticker.C:
				// 快照 presences，避免在发送时阻塞主结构
				presences := GlobalAOIManager.GetPresencesList(nk)
				if presences == nil || len(presences) == 0 {
					continue
				}
				// push 到 job channel（批量）
				for _, p := range presences {
					select {
					case jobs <- p:
					default:
						// job 队列满了就丢一部分（防止阻塞）
						// 也可以记录丢弃统计，视需求决定是否要阻塞
					}
				}
			case <-ctx.Done():
				return
			}
		}
	}()
}

// 周期性广播
//
//	func StartBroadcastLoop(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule) {
//		ticker := time.NewTicker(200 * time.Millisecond)
//		go func() {
//			for {
//				select {
//				case <-ticker.C:
//					presences := GlobalAOIManager.presences
//					for _, p := range presences {
//						trainerInfo := GetActiveTrainerByUid(p.GetUserId())
//						list, m := GlobalAOIManager.GetNearbyPlayersForPlayerId(trainerInfo.Id, true, 50)
//						if len(list) == 0 || m == MainServer.MainLandType_MainLand_None {
//							continue
//						}
//						resultProto := &MainServer.AOIBroadcast{
//							MainLandType: m,
//							Players:      list,
//						}
//						result, err := ProtoToJson(resultProto)
//						if err != nil {
//							logger.Error("ProtoToJson failed: %v", err)
//							continue
//						}
//						err = nk.StreamSend(GlobalAOIManager.streamInfo.mode, "", "", GlobalAOIManager.streamInfo.label, result, []runtime.Presence{p}, false)
//						if err != nil {
//							logger.Error("StreamSend failed: %v", err)
//							continue
//						}
//						// logger.Info("presence: %v", p)
//					}
//				}
//			}
//		}()
//	}
func (am *AOIManager) GetPresencesList(nk runtime.NakamaModule) []runtime.Presence {
	presences, err := nk.StreamUserList(am.streamInfo.mode, "", "", am.streamInfo.label, true, true)
	if err != nil {
		return nil
	}
	return presences
}
func (am *AOIManager) JoinAOIStream(nk runtime.NakamaModule, userID string, sessionID string, logger runtime.Logger) error {
	if _, err := nk.StreamUserJoin(am.streamInfo.mode, "", "", am.streamInfo.label, userID, sessionID, am.streamInfo.hidden, am.streamInfo.persistence, ""); err != nil {
		return err
	}
	// am.UpdatePresences(nk)
	return nil
}

//	func (am *AOIManager) UpdatePresences(nk runtime.NakamaModule) {
//		presences, err := nk.StreamUserList(am.streamInfo.mode, "", "", am.streamInfo.label, true, true)
//		if err != nil {
//			return
//		}
//		am.presences = presences
//	}
func (am *AOIManager) getOrCreateMapAOI(mapId MainServer.MainLandType) *mapAOI {
	am.lock.Lock()
	defer am.lock.Unlock()
	if m, ok := am.maps[mapId]; ok {
		return m
	}
	m := newMapAOI(am.defaultChunkSize)
	am.maps[mapId] = m
	return m
}

// AddPlayer 将玩家加到指定地图（若玩家已在其它 map，会先移除旧地图）
// 返回 isNew 表示是否插入为新玩家
func (am *AOIManager) AddPlayer(mapId MainServer.MainLandType, id int64, pos *MainServer.TrainerLoc) (isNew bool) {
	// 处理旧地图（若存在）
	am.lock.Lock()
	oldMapId, existed := am.playerToMap[id]
	if existed && oldMapId != mapId {
		oldMapAOI := am.maps[oldMapId]
		// 先删除记录，再释放锁以避免死锁（删除实际发生在 oldMapAOI.removePlayer）
		delete(am.playerToMap, id)
		am.lock.Unlock()
		if oldMapAOI != nil {
			oldMapAOI.removePlayer(id)
		}
	} else {
		am.lock.Unlock()
	}

	m := am.getOrCreateMapAOI(mapId)
	newFlag, _ := m.addOrUpdatePlayer(id, pos)

	am.lock.Lock()
	am.playerToMap[id] = mapId
	am.lock.Unlock()
	return newFlag
}

// MovePlayerToMap 将玩家从旧地图移动到新的地图（并更新位置）
func (am *AOIManager) MovePlayerToMap(newMapId MainServer.MainLandType, id int64, pos *MainServer.TrainerLoc) {
	am.AddPlayer(newMapId, id, pos)
}

// RemovePlayer 从当前地图移除玩家（若 mapId 为空则根据记录移除）
func (am *AOIManager) RemovePlayer(mapId MainServer.MainLandType, id int64) {
	am.lock.Lock()
	if mapId == MainServer.MainLandType_MainLand_None {
		if old, ok := am.playerToMap[id]; ok {
			mapId = old
		}
	}
	if mapId != MainServer.MainLandType_MainLand_None {
		delete(am.playerToMap, id)
	}
	am.lock.Unlock()

	if mapId != MainServer.MainLandType_MainLand_None {
		if m, ok := am.maps[mapId]; ok {
			m.removePlayer(id)
		}
	}
}

// UpdatePosition 更新玩家位置（不会自动跨地图）
// 如果玩家当前不在 map 中，则会把其加入到 map 中（方便容错）
func (am *AOIManager) UpdatePosition(mapId MainServer.MainLandType, id int64, pos *MainServer.TrainerLoc) (movedChunk bool) {
	// 若 mapId 与记录不一致，先切换地图
	am.lock.RLock()
	currentMap, inRecord := am.playerToMap[id]
	am.lock.RUnlock()

	if !inRecord || currentMap != mapId {
		// 把 player 移到目标 map
		am.AddPlayer(mapId, id, pos)
		return true
	}

	// 更新同一 map 内位置
	am.lock.RLock()
	m, ok := am.maps[mapId]
	am.lock.RUnlock()
	if !ok {
		// 创建 map 并添加
		am.AddPlayer(mapId, id, pos)
		return true
	}
	_, movedChunk = m.addOrUpdatePlayer(id, pos)
	return movedChunk
}
func (am *AOIManager) GetNearbyPlayersForPlayerId(id int64, excludeSelf bool, maxResults int) ([]*MainServer.TrainerLoc, MainServer.MainLandType) {
	am.lock.RLock()
	m, ok := am.playerToMap[id]
	am.lock.RUnlock()
	if !ok {
		return nil, MainServer.MainLandType_MainLand_None
	}
	return am.GetNearbyPlayersForPlayer(m, id, excludeSelf, maxResults), m
}

// GetNearbyPlayersForPlayer 返回指定玩家周围的玩家（同一 map）
// excludeSelf 是否排除自己
func (am *AOIManager) GetNearbyPlayersForPlayer(mapId MainServer.MainLandType, id int64, excludeSelf bool, maxResults int) []*MainServer.TrainerLoc {
	am.lock.RLock()
	m, ok := am.maps[mapId]
	am.lock.RUnlock()
	if !ok {
		return nil
	}
	return m.getNearbyPlayers(id, excludeSelf, maxResults)
}

// GetNearbyForPosition 返回某位置周边玩家（同一 map）
func (am *AOIManager) GetNearbyForPosition(mapId MainServer.MainLandType, pos *MainServer.TrainerLoc, excludeID int64, maxResults int) []*MainServer.TrainerLoc {
	am.lock.RLock()
	m, ok := am.maps[mapId]
	am.lock.RUnlock()
	if !ok {
		return nil
	}
	return m.getNearbyForPosition(pos, excludeID, maxResults)
}

// BroadcastNearbyForPlayer 返回 map[playerID]PlayerInfo 用于打包给客户端
func (am *AOIManager) BroadcastNearbyForPlayer(mapId MainServer.MainLandType, id int64, excludeSelf bool) map[int64]*MainServer.TrainerLoc {
	list := am.GetNearbyPlayersForPlayer(mapId, id, excludeSelf, 0)
	out := make(map[int64]*MainServer.TrainerLoc, len(list))
	for _, p := range list {
		out[p.TrainerId] = p
	}
	return out
}

// CountPlayersInMapChunk（监控/调试用）
func (am *AOIManager) CountPlayersInMapChunk(mapId MainServer.MainLandType, cx, cz int) int {
	am.lock.RLock()
	m, ok := am.maps[mapId]
	am.lock.RUnlock()
	if !ok {
		return 0
	}
	return m.getChunkCount(cx, cz)
}

// mapAOI 辅助：获取 chunk 内玩家数量
func (m *mapAOI) getChunkCount(cx, cz int) int {
	k := chunkKey(cx, cz)
	m.lock.RLock()
	defer m.lock.RUnlock()
	if bucket, ok := m.chunks[k]; ok {
		return len(bucket)
	}
	return 0
}

// TotalPlayersInMap
func (am *AOIManager) TotalPlayersInMap(mapId MainServer.MainLandType) int {
	am.lock.RLock()
	defer am.lock.RUnlock()
	if m, ok := am.maps[mapId]; ok {
		return m.getTotalPlayers()
	}
	return 0
}

// RemoveAllPlayersFromMap (如地图下线)
func (am *AOIManager) RemoveAllPlayersFromMap(mapId MainServer.MainLandType) {
	am.lock.Lock()
	m, ok := am.maps[mapId]
	if ok {
		delete(am.maps, mapId)
	}
	am.lock.Unlock()
	if ok {
		// 清除 map 内数据
		m.lock.Lock()
		for pid := range m.players {
			delete(m.players, pid)
		}
		m.chunks = make(map[string]map[int64]*player)
		m.lock.Unlock()
	}
}
