package battle

import (
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
)

// 根据双方的组队状态确定战斗类型
func determineBattleType(proposer, target *MainServer.Trainer, inviteBattleType MainServer.BattleType) MainServer.BattleType {
	proposerPartyCount := 1
	targetPartyCount := 1

	// 检查发起者是否在组队
	if proposerParty, exists := tool.GetGlobalPartyMap().Get(proposer.Id); exists {
		proposerPartyCount = len(proposerParty.Trainers)
	}

	// 检查目标玩家是否在组队
	if targetParty, exists := tool.GetGlobalPartyMap().Get(target.Id); exists {
		targetPartyCount = len(targetParty.Trainers)
	}
	return MainServer.BattleType_MultiPlayer4v4
	// 根据双方组队人数确定战斗类型
	totalTrainers := proposerPartyCount + targetPartyCount
	if inviteBattleType == MainServer.BattleType_SinglePlayer && totalTrainers == 2 {
		return MainServer.BattleType_SinglePlayer
	} else if totalTrainers > 2 {
		return MainServer.BattleType_DoublePlayer
	} else {
		return MainServer.BattleType_SinglePlayerAndDoublePokemon
	}
	// if totalTrainers == 2 {
	// 	// 双方都是单人
	// 	return MainServer.BattleType_SinglePlayer
	// } else if totalTrainers <= 3 {
	// 	// 一方是单人，一方是组队
	// 	return MainServer.BattleType_SinglePlayerAndDoublePokemon
	// } else {
	// 	// 双方都是组队
	// 	return MainServer.BattleType_DoublePlayer
	// }
}
