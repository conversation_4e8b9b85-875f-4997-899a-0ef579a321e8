package battle

import (
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
)

func matchLabelBy(trainer *MainServer.Trainer, battleType MainServer.BattleType, maker *MainServer.BattleMatchMaker) *MainServer.BattleMatchLabel {
	label := &MainServer.BattleMatchLabel{}
	label.PartyUpTwo = false
	partyCount := 1
	partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
	if exists {
		partyCount = len(partyInfo.Trainers)
		label.PartyUpTwo = partyCount > 1
	}
	if maker == nil {
		label.Min = int32(partyCount)
		label.Max = int32(partyCount)
		return label
	}
	switch battleType {
	case MainServer.BattleType_SingleWild:
	case MainServer.BattleType_SingleNPC:
	case MainServer.BattleType_SingleNPCAndDoublePokemon:
	case MainServer.BattleType_SingleWildAndDoublePokemon:
		label.Min = 1
		label.Max = 1
	case MainServer.BattleType_SinglePlayer:
	case MainServer.BattleType_DoubleNPC:
	case MainServer.BattleType_DoubleWild:
		label.Min = 2
		label.Max = 2
	case MainServer.BattleType_SinglePlayerAndDoublePokemon:
		if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_none {
			label.Min = 2
			label.Max = 2
		} else if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
			label.Min = 2
			label.Max = 3
		} else if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
			label.Min = 3
			label.Max = 3
		}
	case MainServer.BattleType_DoublePlayer:
		if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_none {
			label.Min = 4
			label.Max = 4
		} else if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
			label.Min = 3
			label.Max = 4
		} else if maker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
			label.Min = 3
			label.Max = 3
		}
	}
	// if !exists {

	// }
	return label
}
func MatchMakerToQuery(matchMaker *MainServer.BattleMatchMaker) string {
	return "*"
	query := "+label." + KeyMatchOpen + ":T"
	query = query + " +label." + KeyMatchStarted + ":false"
	query = query + " +label." + KeyMatchStyle + ":" + matchMaker.MatchStyle.String()
	if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
		// message.MatchmakerAdd.StringProperties[battle.KeyMatchThreeTrainer] = "T"
	} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
		query = query + " +label." + KeyMatchThreeTrainer + ":true"
	} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_none {
		query = query + " +label." + KeyMatchThreeTrainer + ":false"
	}
	return query
}

//	func MatchMakerToLabelJson(trainer *MainServer.Trainer, battleType MainServer.BattleType, matchMaker *MainServer.BattleMatchMaker) string {
//		matchLabel := &MainServer.BattleMatchLabel{
//			op
//		}
//		query := "+label." + KeyMatchOpen + ":false"
//		query = query + " +label." + KeyMatchStarted + ":false"
//		query = query + " +label." + KeyMatchStyle + ":" + matchMaker.MatchStyle.String()
//		if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
//			// message.MatchmakerAdd.StringProperties[battle.KeyMatchThreeTrainer] = "T"
//		} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
//			query = query + " +label." + KeyMatchThreeTrainer + ":true"
//		} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_none {
//			query = query + " +label." + KeyMatchThreeTrainer + ":false"
//		}
//		return query
//	}
func matchMakerToProperties(matchMaker *MainServer.BattleMatchMaker) map[string]string {
	stringProperties := make(map[string]string)
	stringProperties[KeyMatchOpen] = "F"
	stringProperties[KeyMatchStarted] = "F"
	stringProperties[KeyMatchStyle] = matchMaker.MatchStyle.String()
	if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_accept {
		// message.MatchmakerAdd.StringProperties[battle.KeyMatchThreeTrainer] = "T"
	} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_only {
		stringProperties[KeyMatchThreeTrainer] = "T"
	} else if matchMaker.ThreeTrainerType == MainServer.BattleMatchThreeTrainerType_none {
		stringProperties[KeyMatchThreeTrainer] = "F"
	}
	return stringProperties
}
