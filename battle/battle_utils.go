package battle

import (
	"context"
	"errors"
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 辅助函数
func validateTrainer(ctx context.Context) (*MainServer.Trainer, error) {
	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
	if err != nil {
		return nil, err
	}
	if !LimitCallRate(trainer.Id) {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_RateLimitExceeded)
	}
	return trainer, nil
}

func parseBattlePrepare(payload string) (*MainServer.BattlePrepare, error) {
	prepareInfo := &MainServer.BattlePrepare{}
	if err := tool.Base64ToProto(payload, prepareInfo); err != nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	if prepareInfo.BattleMatchMaker == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_BattleInfoError)
	}
	return prepareInfo, nil
}

// 添加超时控制的channel操作
func receiveMessageWithTimeout(ch chan runtime.MatchData, timeout time.Duration) (*runtime.MatchData, error) {
	select {
	case msg := <-ch:
		return &msg, nil
	case <-time.After(timeout):
		return nil, errors.New("receive message timeout")
	}
}
