package battle

import (
	"context"
	"database/sql"
	"encoding/json"
	"go-nakama-poke/config"
	"go-nakama-poke/tool"
	"sync"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

type ClientInfo struct {
	CPUUsage       float64   // CPU 使用率
	MemoryUsage    float64   // 内存使用率
	LastActive     time.Time // 最后活跃时间
	IsBattleServer bool      // 是否为战斗服务器
}

var (
	clientCache        sync.Map                  // 全局缓存，用于存储客户端信息
	secretKey          = "your_secret_key"       // 预定义的密钥，用于验证战斗服务器
	matchServerMapping = make(map[string]string) // 全局字典，用于保存匹配ID和ServerID的对应关系
	mapLock            sync.Mutex                // 保护全局字典的锁
)

// BattlePlugin 实现
type BattlePlugin struct{}

// 创建新的插件实例
func NewBattlePlugin() *BattlePlugin {
	return &BattlePlugin{}
}

// RPC 方法：将客户端注册为战斗服务器
func (p *BattlePlugin) RegisterBattleServer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取调用者的客户端 ID
	userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		// If user ID is not found, RPC was called without a session token.
		return "", runtime.NewError("Invalid context", 401)
	}

	sessionID, ok := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)
	if !ok {
		// If session ID is not found, RPC was not called over a connected socket.
		return "", runtime.NewError("Invalid context", 401)
	}

	// 解析传入的密钥
	var input map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &input); err != nil {
		return "", runtime.NewError("Invalid payload format", 3)
	}

	// 检查密钥是否正确
	if input["secret_key"] != secretKey {
		logger.Warn("Client %s failed to provide a valid secret key", sessionID)
		return "", runtime.NewError("Invalid secret key", 7)
	}

	hidden := false
	persistence := false
	if _, err := nk.StreamUserJoin(config.BattleStreamMode, "", "", "battle server", userID, sessionID, hidden, persistence, ""); err != nil {
		return "", err
	}

	// 将该客户端注册为战斗服务器
	clientInfo := ClientInfo{
		IsBattleServer: true,
		LastActive:     time.Now(),
	}

	clientCache.Store(userID, clientInfo)
	logger.Info("Registered client %s as battle server", userID)

	return "{\"message\":\"Battle server registered successfully\"}", nil
}

// RPC 方法：接收客户端资源信息
func (p *BattlePlugin) UpdateClientResources(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取调用者的客户端 ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	// 从缓存中获取客户端信息
	value, ok := clientCache.Load(userID)
	if !ok {
		return "", runtime.NewError("Client not registered as battle server", 5)
	}

	clientInfo, ok := value.(ClientInfo)
	if !ok {
		return "", runtime.NewError("Failed to retrieve client info", 13)
	}

	// 检查是否是战斗服务器
	if !clientInfo.IsBattleServer {
		logger.Warn("Client %s is not a battle server, ignoring resource update", userID)
		return "{\"message\":\"Ignored: client is not a battle server\"}", nil
	}

	// 解析并更新客户端的 CPU 和内存信息
	var input map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &input); err != nil {
		return "", runtime.NewError("Invalid payload format", 3)
	}

	cpuUsage := input["cpu_usage"].(float64)
	memoryUsage := input["memory_usage"].(float64)

	clientInfo.CPUUsage = cpuUsage
	clientInfo.MemoryUsage = memoryUsage
	clientInfo.LastActive = time.Now() // 更新活跃时间

	// 将更新后的信息重新存储到缓存
	clientCache.Store(userID, clientInfo)

	logger.Info("Updated resources for battle server: %s (CPU: %f, Memory: %f)", userID, cpuUsage, memoryUsage)
	return "{\"message\":\"Battle server resources updated successfully\"}", nil
}

// 获取最空闲的战斗服务器（基于 CPU 和内存使用情况）
func (p *BattlePlugin) GetMostIdleServer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var idleClientID string = GetMostIdleServer()
	// var minUsage float64 = 100.0 // 初始设置为100%使用率，寻找更低的使用率

	// // 遍历所有客户端，找到最空闲的战斗服务器
	// clientCache.Range(func(key, value interface{}) bool {
	// 	clientInfo := value.(ClientInfo)

	// 	// 只检查战斗服务器
	// 	if clientInfo.IsBattleServer {
	// 		totalUsage := clientInfo.CPUUsage + clientInfo.MemoryUsage

	// 		if totalUsage < minUsage {
	// 			minUsage = totalUsage
	// 			idleClientID = key.(string)
	// 		}
	// 	}
	// 	return true
	// })

	if idleClientID == "" {
		return "", runtime.NewError("No idle server found", 5)
	}

	logger.Info("Most idle battle server is: %s", idleClientID)
	return "{\"message\":\"" + idleClientID + "\"}", nil
}
func GetMostIdleServer() string {
	var idleClientID string
	var minUsage float64 = 100.0 // 初始设置为100%使用率，寻找更低的使用率

	// 遍历所有客户端，找到最空闲的战斗服务器
	clientCache.Range(func(key, value interface{}) bool {
		clientInfo := value.(ClientInfo)

		// 只检查战斗服务器
		if clientInfo.IsBattleServer {
			totalUsage := clientInfo.CPUUsage + clientInfo.MemoryUsage

			if totalUsage < minUsage {
				minUsage = totalUsage
				idleClientID = key.(string)
			}
		}
		return true
	})
	return idleClientID
	// if idleClientID == "" {
	// 	return "", runtime.NewError("No idle server found", 5)
	// }

	// logger.Info("Most idle battle server is: %s", idleClientID)
	// return "{\"message\":\"" + idleClientID + "\"}", nil
}

// 客户端掉线处理，移除缓存中的信息
func (p *BattlePlugin) RemoveClientFromCache(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取调用者的客户端 ID
	sessionID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	clientCache.Delete(sessionID)
	logger.Info("Client %s removed from cache", sessionID)

	return "{\"message\":\"Client removed successfully\"}", nil
}

// 封装的函数：调用get_most_idle_server
func FetchMostIdleServer(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule) (string, error) {
	var idleServerID string = GetMostIdleServer()
	if idleServerID == "" {
		return "", runtime.NewError("No idle server found", 5)
	}
	// 调用 RPC 方法获取最空闲的服务器
	// result, err := nk.RPC(ctx, "get_most_idle_server", "{}")
	// if err != nil {
	// 	logger.Error("Failed to get most idle server: %v", err)
	// 	return "", err
	// }

	// 解析返回的 JSON 数据
	// var response map[string]interface{}
	// if err := json.Unmarshal([]byte(result), &response); err != nil {
	// 	logger.Error("Failed to parse idle server response: %v", err)
	// 	return "", err
	// }

	// idleServerID := response["message"].(string)
	// logger.Info("Most idle server found: %s", idleServerID)
	return idleServerID, nil
}
func (p *BattlePlugin) SendMessageToServer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取调用者的客户端 ID
	sessionID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	subject := "You've unlocked level 100!"
	content := map[string]interface{}{
		"reward_coins": 1000,
	}
	receiverID := sessionID
	senderID := "dcb891ea-a311-4681-9213-6741351c9994"
	code := 101
	persistent := true

	nk.NotificationSend(ctx, receiverID, subject, content, code, senderID, persistent)

	return "{}", nil
}
func ClearServerToMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, matchID string) {
	tool.Remove(matchID)
}

// 使用buntdb来进行数据管理
func AssignServerToMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, matchID string) (string, error) {
	// 检查是否已经存在映射
	serverID, err := tool.Get(matchID)

	if err == nil {
		logger.Info("Server %s already assigned to match %s", serverID, matchID)
		return serverID, nil
	}

	// 调用封装的函数获取最空闲的服务器
	idleServerID, err := FetchMostIdleServer(ctx, logger, nk)
	if err != nil {
		return "", err
	}

	// 存储到 BuntDB 中
	err = tool.Set(matchID, idleServerID)
	if err != nil {
		return "", err
	}

	logger.Info("Assigned server %s to match %s", idleServerID, matchID)
	return idleServerID, nil
}

// 在匹配逻辑中使用并存储到全局字典
// func AssignServerToMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, matchID string) (string, error) {
// 	// 先检查缓存中是否已经有对应的服务器
// 	mapLock.Lock()
// 	if serverID, ok := matchServerMapping[matchID]; ok {
// 		mapLock.Unlock()
// 		logger.Info("Server %s already assigned to match %s", serverID, matchID)
// 		return serverID, nil
// 	}
// 	mapLock.Unlock()

// 	// 如果没有，则调用封装的函数获取最空闲的服务器
// 	idleServerID, err := FetchMostIdleServer(ctx, logger, nk)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 存储匹配ID和服务器ID的对应关系
// 	mapLock.Lock()
// 	matchServerMapping[matchID] = idleServerID
// 	mapLock.Unlock()

// 	logger.Info("Assigned server %s to match %s", idleServerID, matchID)
// 	return idleServerID, nil
// }

// 初始化模块，注册所有的 RPC 方法
// func InitModule(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, initializer runtime.Initializer) error {
//     plugin := NewPlugin()

//     // 注册 RPC 方法
//     if err := initializer.RegisterRpc("register_battle_server", plugin.RegisterBattleServer); err != nil {
//         return err
//     }
//     if err := initializer.RegisterRpc("update_client_resources", plugin.UpdateClientResources); err != nil {
//         return err
//     }
//     if err := initializer.RegisterRpc("get_most_idle_server", plugin.GetMostIdleServer); err != nil {
//         return err
//     }
//     if err := initializer.RegisterRpc("remove_client_from_cache", plugin.RemoveClientFromCache); err != nil {
//         return err
//     }

//     logger.Info("Custom plugin loaded successfully.")
//     return nil
// }
