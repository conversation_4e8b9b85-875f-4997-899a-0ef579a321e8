package nconst

import (
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 错误码枚举定义
// 这些错误码应该在proto文件中定义为MainServer.ErrorCode枚举
// const (
// 	// 成功
// 	ErrorCodeSuccess = 0

// 	// 1000-1999: 致命错误 - 客户端应该退出到登录界面
// 	ErrorCodeSessionExpired     = 1001 // 会话过期
// 	ErrorCodeTrainerNotFound    = 1002 // 训练师未找到（内存中不存在）
// 	ErrorCodeAccountBanned      = 1003 // 账号被封禁
// 	ErrorCodeServerMaintenance  = 1004 // 服务器维护
// 	ErrorCodeVersionMismatch    = 1005 // 版本不匹配
// 	ErrorCodeDuplicateLogin     = 1006 // 重复登录
// 	ErrorCodeAuthenticationFail = 1007 // 认证失败

// 	// 2000-2999: 权限错误 - 客户端显示错误信息
// 	ErrorCodePermissionDenied     = 2001 // 权限不足
// 	ErrorCodeAdminRequired        = 2002 // 需要管理员权限
// 	ErrorCodeSpecialRightRequired = 2003 // 需要特殊权限
// 	ErrorCodeTeamLeaderRequired   = 2004 // 需要队长权限
// 	ErrorCodeBoxAccessDenied      = 2005 // 盒子访问被拒绝

// 	// 3000-3999: 参数错误 - 客户端显示错误信息
// 	ErrorCodeInvalidPayload      = 3001 // 无效的请求数据
// 	ErrorCodeInvalidParameter    = 3002 // 无效的参数
// 	ErrorCodeMissingParameter    = 3003 // 缺少必要参数
// 	ErrorCodeParameterOutOfRange = 3004 // 参数超出范围

// 	// 4000-4999: 业务逻辑错误 - 客户端显示错误信息
// 	ErrorCodeResourceNotFound    = 4001 // 资源未找到
// 	ErrorCodeResourceExhausted   = 4002 // 资源耗尽
// 	ErrorCodeOperationNotAllowed = 4003 // 操作不被允许
// 	ErrorCodeInsufficientFunds   = 4004 // 资金不足
// 	ErrorCodeInventoryFull       = 4005 // 背包已满
// 	ErrorCodePokemonNotFound     = 4006 // 精灵未找到
// 	ErrorCodeItemNotFound        = 4007 // 道具未找到
// 	ErrorCodeBattleNotFound      = 4008 // 战斗未找到
// 	ErrorCodeMatchNotFound       = 4009 // 匹配未找到
// 	ErrorCodeQuestNotFound       = 4010 // 任务未找到
// 	ErrorCodeEmailNotFound       = 4011 // 邮件未找到
// 	ErrorCodeTradeNotFound       = 4012 // 交易未找到
// 	ErrorCodeCooldownActive      = 4013 // 冷却时间中
// 	ErrorCodeRateLimitExceeded   = 4014 // 频率限制超出
// 	ErrorCodeOperationLocked     = 4015 // 操作被锁定
// 	ErrorCodeInvalidState        = 4016 // 无效状态
// 	ErrorCodeNotAnEgg            = 4017 // 不是蛋
// 	ErrorCodeItemCannotUseHere   = 4018 // 道具无法在此场景使用
// 	ErrorCodeConfigNotFound      = 4019 // 配置未找到
// 	ErrorCodePokemonNotInBox     = 4020 // 宝可梦不在盒子中
// 	ErrorCodeCannotBreed         = 4021 // 这对宝可梦不能繁殖

// 	// 5000-5999: 系统错误 - 客户端显示通用错误信息
// 	ErrorCodeDatabaseError      = 5001 // 数据库错误
// 	ErrorCodeTransactionFailed  = 5002 // 事务失败
// 	ErrorCodeSerializationError = 5003 // 序列化错误
// 	ErrorCodeNetworkError       = 5004 // 网络错误
// 	ErrorCodeInternalError      = 5005 // 内部错误
// 	ErrorCodeServiceUnavailable = 5006 // 服务不可用
// 	ErrorCodeTimeout            = 5007 // 超时
// 	ErrorCodeConfigError        = 5008 // 配置错误

// 	// 6000-6999: 战斗相关错误
// 	ErrorCodeBattleServerNotFound = 6001 // 战斗服务器未找到
// 	ErrorCodeBattleInfoError      = 6002 // 战斗信息错误
// 	ErrorCodeBattleChoiceLocked   = 6003 // 战斗选择被锁定
// 	ErrorCodeCreateBattleError    = 6004 // 创建战斗错误
// )

// 错误信息映射
var errorMessages = map[MainServer.ErrorCode]string{
	MainServer.ErrorCode_ErrorCode_Success: "成功",

	// 致命错误
	MainServer.ErrorCode_ErrorCode_SessionExpired:     "会话已过期，请重新登录",
	MainServer.ErrorCode_ErrorCode_TrainerNotFound:    "训练师信息未找到，请重新登录",
	MainServer.ErrorCode_ErrorCode_AccountBanned:      "账号已被封禁",
	MainServer.ErrorCode_ErrorCode_ServerMaintenance:  "服务器维护中",
	MainServer.ErrorCode_ErrorCode_VersionMismatch:    "客户端版本不匹配，请更新",
	MainServer.ErrorCode_ErrorCode_DuplicateLogin:     "账号在其他地方登录",
	MainServer.ErrorCode_ErrorCode_AuthenticationFail: "身份验证失败",

	// 权限错误
	MainServer.ErrorCode_ErrorCode_PermissionDenied:     "权限不足",
	MainServer.ErrorCode_ErrorCode_AdminRequired:        "需要管理员权限",
	MainServer.ErrorCode_ErrorCode_SpecialRightRequired: "需要特殊权限",
	MainServer.ErrorCode_ErrorCode_TeamLeaderRequired:   "需要队长权限",
	MainServer.ErrorCode_ErrorCode_BoxAccessDenied:      "盒子访问被拒绝",

	// 参数错误
	MainServer.ErrorCode_ErrorCode_InvalidPayload:      "无效的请求数据",
	MainServer.ErrorCode_ErrorCode_InvalidParameter:    "无效的参数",
	MainServer.ErrorCode_ErrorCode_MissingParameter:    "缺少必要参数",
	MainServer.ErrorCode_ErrorCode_ParameterOutOfRange: "参数超出范围",

	// 业务逻辑错误
	MainServer.ErrorCode_ErrorCode_ResourceNotFound:    "资源未找到",
	MainServer.ErrorCode_ErrorCode_ResourceExhausted:   "资源耗尽",
	MainServer.ErrorCode_ErrorCode_OperationNotAllowed: "操作不被允许",
	MainServer.ErrorCode_ErrorCode_InsufficientFunds:   "资金不足",
	MainServer.ErrorCode_ErrorCode_InventoryFull:       "背包已满",
	MainServer.ErrorCode_ErrorCode_PokemonNotFound:     "精灵未找到",
	MainServer.ErrorCode_ErrorCode_ItemNotFound:        "道具未找到",
	MainServer.ErrorCode_ErrorCode_BattleNotFound:      "战斗未找到",
	MainServer.ErrorCode_ErrorCode_MatchNotFound:       "匹配未找到",
	MainServer.ErrorCode_ErrorCode_QuestNotFound:       "任务未找到",
	MainServer.ErrorCode_ErrorCode_EmailNotFound:       "邮件未找到",
	MainServer.ErrorCode_ErrorCode_TradeNotFound:       "交易未找到",
	MainServer.ErrorCode_ErrorCode_CooldownActive:      "冷却时间中",
	MainServer.ErrorCode_ErrorCode_RateLimitExceeded:   "操作过于频繁",
	MainServer.ErrorCode_ErrorCode_OperationLocked:     "操作被锁定",
	MainServer.ErrorCode_ErrorCode_InvalidState:        "无效状态",
	MainServer.ErrorCode_ErrorCode_NotAnEgg:            "不是蛋",
	MainServer.ErrorCode_ErrorCode_ItemCannotUseHere:   "道具无法在此场景使用",
	MainServer.ErrorCode_ErrorCode_ConfigNotFound:      "配置未找到",
	MainServer.ErrorCode_ErrorCode_PokemonNotInBox:     "宝可梦不在盒子中",
	MainServer.ErrorCode_ErrorCode_CannotBreed:         "这对宝可梦不能繁殖",

	// 系统错误
	MainServer.ErrorCode_ErrorCode_DatabaseError:      "数据库错误",
	MainServer.ErrorCode_ErrorCode_TransactionFailed:  "事务失败",
	MainServer.ErrorCode_ErrorCode_SerializationError: "序列化错误",
	MainServer.ErrorCode_ErrorCode_NetworkError:       "网络错误",
	MainServer.ErrorCode_ErrorCode_InternalError:      "内部错误",
	MainServer.ErrorCode_ErrorCode_ServiceUnavailable: "服务不可用",
	MainServer.ErrorCode_ErrorCode_Timeout:            "操作超时",
	MainServer.ErrorCode_ErrorCode_ConfigError:        "配置错误",

	// 战斗相关错误
	MainServer.ErrorCode_ErrorCode_BattleServerNotFound: "战斗服务器未找到",
	MainServer.ErrorCode_ErrorCode_BattleInfoError:      "战斗信息错误",
	MainServer.ErrorCode_ErrorCode_BattleChoiceLocked:   "战斗选择被锁定",
	MainServer.ErrorCode_ErrorCode_CreateBattleError:    "创建战斗失败",
}

// 判断是否为致命错误（需要客户端退出到登录界面）
func IsFatalError(errorCode MainServer.ErrorCode) bool {
	return int32(errorCode) >= 1000 && int32(errorCode) < 2000
}

// 获取错误信息
func GetErrorMessage(errorCode MainServer.ErrorCode) string {
	if msg, exists := errorMessages[errorCode]; exists {
		return msg
	}
	return "未知错误"
}

// 创建统一的错误响应
func NewGameError(errorCode MainServer.ErrorCode, details ...string) error {
	message := GetErrorMessage(errorCode)
	if len(details) > 0 && details[0] != "" {
		message = details[0]
	}

	// 对于致命错误，使用特殊的HTTP状态码
	httpCode := 400
	if IsFatalError(errorCode) {
		httpCode = 401 // 使用401表示需要重新认证
	} else if int32(errorCode) >= 2000 && int32(errorCode) < 3000 {
		httpCode = 403 // 权限错误
	} else if int32(errorCode) >= 4000 && int32(errorCode) < 5000 {
		httpCode = 400 // 业务逻辑错误
	} else if int32(errorCode) >= 5000 && int32(errorCode) < 6000 {
		httpCode = 500 // 系统错误
	}

	return runtime.NewError(message, httpCode)
}

// 创建带有错误码的响应结构
// 注意：需要在proto文件中定义MainServer.ErrorResponse后才能使用
// func CreateErrorResponse(errorCode int, details ...string) *MainServer.ErrorResponse {
// 	return &MainServer.ErrorResponse{
// 		ErrorCode: int32(errorCode),
// 		Message:   GetErrorMessage(errorCode),
// 		Details:   details,
// 		IsFatal:   IsFatalError(errorCode),
// 	}
// }

// 便捷函数：训练师未找到错误
func NewTrainerNotFoundError() error {
	return NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
}

// 便捷函数：权限不足错误
func NewPermissionDeniedError(details ...string) error {
	return NewGameError(MainServer.ErrorCode_ErrorCode_PermissionDenied, details...)
}

// 便捷函数：无效参数错误
func NewInvalidParameterError(details ...string) error {
	return NewGameError(MainServer.ErrorCode_ErrorCode_InvalidParameter, details...)
}

// 便捷函数：资源未找到错误
func NewResourceNotFoundError(resourceType string) error {
	return NewGameError(MainServer.ErrorCode_ErrorCode_ResourceNotFound, resourceType+"未找到")
}

// 便捷函数：数据库错误
func NewDatabaseError(details ...string) error {
	return NewGameError(MainServer.ErrorCode_ErrorCode_DatabaseError, details...)
}
