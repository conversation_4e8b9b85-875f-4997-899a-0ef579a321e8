package transaction

// import (
// 	"context"
// 	"database/sql"
// 	"encoding/json"
// 	"strconv"

// 	"go-nakama-poke/nconst"
// 	"go-nakama-poke/proto/MainServer"
// 	"go-nakama-poke/tool"

// 	"github.com/heroiclabs/nakama-common/runtime"
// )

// // RpcGetTransactionHistory RPC函数：获取交易历史
// func RpcGetTransactionHistory(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 解析请求参数
// 	param := &MainServer.RpcGetTransactionHistoryRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
// 	}

// 	// 获取训练师信息
// 	trainer, err := tool.GetActiveTrainerByCtxWithError(ctx)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 设置默认值
// 	if param.Limit <= 0 {
// 		param.Limit = 20
// 	}
// 	if param.Limit > 100 {
// 		param.Limit = 100
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TransactionFailed)
// 	}
// 	defer tx.Rollback()

// 	// 转换参数类型
// 	var transactionType *MainServer.TransactionType
// 	var operationType *MainServer.TransactionOperationType
// 	var itemType *MainServer.TransactionItemType

// 	if param.TransactionType != MainServer.TransactionType_TransactionType_None {
// 		transactionType = &param.TransactionType
// 	}
// 	if param.OperationType != MainServer.TransactionOperationType_TransactionOperationType_Unknown {
// 		operationType = &param.OperationType
// 	}
// 	if param.ItemType != MainServer.TransactionItemType_TransactionItemType_Unknown {
// 		itemType = &param.ItemType
// 	}

// 	// 获取交易记录
// 	records, err := GetTransactionRecords(ctx, logger, tx, trainer.Id, transactionType, operationType, itemType, param.Limit, param.Offset)
// 	if err != nil {
// 		return "", runtime.NewError("获取交易记录失败: "+err.Error(), 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 构建响应结果（records已经是proto格式）
// 	result := &MainServer.RpcGetTransactionHistoryResponse{
// 		Records: records,
// 		Count:   int32(len(records)),
// 	}

// 	return tool.ProtoToBase64(result)
// }

// // RpcGetTransactionStatistics RPC函数：获取交易统计
// func RpcGetTransactionStatistics(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	param := &MainServer.RpcGetTransactionStatisticsRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 设置默认值
// 	if param.Days <= 0 {
// 		param.Days = 7
// 	}
// 	if param.Days > 365 {
// 		param.Days = 365
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 获取统计数据
// 	statistics, err := GetTransactionStatistics(ctx, logger, tx, trainer.Id, param.Days)
// 	if err != nil {
// 		return "", runtime.NewError("获取交易统计失败: "+err.Error(), 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 构建响应结果（statistics已经是proto格式）
// 	result := &MainServer.RpcGetTransactionStatisticsResponse{
// 		Statistics: statistics,
// 	}

// 	return tool.ProtoToBase64(result)
// }

// // RpcRecordItemUsage RPC函数：记录道具使用
// func RpcRecordItemUsage(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	param := &MainServer.RpcRecordItemUsageRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 验证参数
// 	if param.ItemId == "" {
// 		return "", runtime.NewError("道具ID不能为空", 400)
// 	}
// 	if param.Quantity <= 0 {
// 		return "", runtime.NewError("使用数量必须大于0", 400)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 解析额外信息
// 	var extraInfo map[string]interface{}
// 	if param.ExtraInfo != "" {
// 		if err := json.Unmarshal([]byte(param.ExtraInfo), &extraInfo); err != nil {
// 			logger.Warn("Failed to parse extra_info: %v", err)
// 		}
// 	}

// 	// 记录道具使用
// 	err = RecordItemUsage(ctx, logger, tx, trainer.Id, param.ItemId, param.ItemName, param.Quantity, extraInfo)
// 	if err != nil {
// 		return "", runtime.NewError("记录道具使用失败: "+err.Error(), 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 构建响应结果
// 	result := &MainServer.RpcRecordItemUsageResponse{
// 		Success: true,
// 		Message: "道具使用记录成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }

// // RpcGetPurchaseHistory RPC函数：获取购买历史
// func RpcGetPurchaseHistory(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	param := &MainServer.RpcGetPurchaseHistoryRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 设置默认值
// 	if param.Limit <= 0 {
// 		param.Limit = 20
// 	}
// 	if param.Limit > 100 {
// 		param.Limit = 100
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 转换参数类型
// 	var itemType *MainServer.TransactionItemType
// 	if param.ItemType != MainServer.TransactionItemType_TransactionItemType_Unknown {
// 		itemType = &param.ItemType
// 	}

// 	// 获取购买历史
// 	records, err := GetPurchaseHistory(ctx, logger, tx, trainer.Id, itemType, param.Limit, param.Offset)
// 	if err != nil {
// 		return "", runtime.NewError("获取购买历史失败: "+err.Error(), 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 构建响应结果（records已经是proto格式）
// 	result := &MainServer.RpcGetPurchaseHistoryResponse{
// 		Records: records,
// 		Count:   int32(len(records)),
// 	}

// 	return tool.ProtoToBase64(result)
// }

// // RpcDeleteTransactionRecord RPC函数：删除交易记录（管理员功能）
// func RpcDeleteTransactionRecord(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	param := &MainServer.RpcDeleteTransactionRecordRequest{}
// 	err := tool.Base64ToProto(payload, param)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 验证参数
// 	if param.RecordId == "" {
// 		return "", runtime.NewError("记录ID不能为空", 400)
// 	}

// 	recordId, err := strconv.ParseInt(param.RecordId, 10, 64)
// 	if err != nil {
// 		return "", runtime.NewError("无效的记录ID", 400)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 删除交易记录
// 	err = DeleteTransactionRecord(ctx, logger, tx, recordId)
// 	if err != nil {
// 		return "", runtime.NewError("删除交易记录失败: "+err.Error(), 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 构建响应结果
// 	result := &MainServer.RpcDeleteTransactionRecordResponse{
// 		Success: true,
// 		Message: "交易记录删除成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }
