package transaction

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strconv"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RecordPokemonPurchase 记录购买宝可梦
func RecordPokemonPurchase(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	buyerTid int64, sellerTid int64, pokemonId int64, pokemonName string,
	price int64, specialCoin int64, marketId string) error {

	record := &MainServer.TransactionRecordInfo{
		Tid:             buyerTid,
		TransactionType: MainServer.TransactionType_TransactionType_Purchase,
		OperationType:   MainServer.TransactionOperationType_TransactionOperationType_Buy,
		ItemType:        MainServer.TransactionItemType_TransactionItemType_Pokemon,
		ItemId:          strconv.FormatInt(pokemonId, 10),
		ItemName:        pokemonName,
		Quantity:        1,
		Price:           price,
		SpecialCoin:     specialCoin,
		SellerTid:       sellerTid,
		BuyerTid:        buyerTid,
		MarketId:        marketId,
		ExtraInfo:       "{}",
	}

	return AddTransactionRecord(ctx, logger, tx, record)
}

// RecordItemPurchase 记录购买道具
func RecordItemPurchase(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	buyerTid int64, sellerTid int64, itemId int64, itemName string,
	quantity int32, price int64, specialCoin int64, marketId string) error {

	record := &MainServer.TransactionRecordInfo{
		Tid:             buyerTid,
		TransactionType: MainServer.TransactionType_TransactionType_Purchase,
		OperationType:   MainServer.TransactionOperationType_TransactionOperationType_Buy,
		ItemType:        MainServer.TransactionItemType_TransactionItemType_Item,
		ItemId:          strconv.FormatInt(itemId, 10),
		ItemName:        itemName,
		Quantity:        quantity,
		Price:           price,
		SpecialCoin:     specialCoin,
		SellerTid:       sellerTid,
		BuyerTid:        buyerTid,
		MarketId:        marketId,
		ExtraInfo:       "{}",
	}

	return AddTransactionRecord(ctx, logger, tx, record)
}

// RecordItemUsage 记录使用道具
func RecordItemUsage(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	userTid int64, itemId int64, itemName string, quantity int32, extraInfo map[string]interface{}) error {

	extraInfoJSON := "{}"
	if extraInfo != nil {
		if jsonBytes, err := json.Marshal(extraInfo); err == nil {
			extraInfoJSON = string(jsonBytes)
		}
	}

	record := &MainServer.TransactionRecordInfo{
		Tid:             userTid,
		TransactionType: MainServer.TransactionType_TransactionType_Use,
		OperationType:   MainServer.TransactionOperationType_TransactionOperationType_Use,
		ItemType:        MainServer.TransactionItemType_TransactionItemType_Item,
		ItemId:          strconv.FormatInt(itemId, 10),
		ItemName:        itemName,
		Quantity:        quantity,
		Price:           0,
		SpecialCoin:     0,
		SellerTid:       0,
		BuyerTid:        0,
		MarketId:        "",
		ExtraInfo:       extraInfoJSON,
	}

	return AddTransactionRecord(ctx, logger, tx, record)
}

// RecordMarketListing 记录市场上架
func RecordMarketListing(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	sellerTid int64, itemType MainServer.TransactionItemType, itemId int64, itemName string,
	quantity int32, price int64, specialCoin int64, marketId string) error {

	record := &MainServer.TransactionRecordInfo{
		Tid:             sellerTid,
		TransactionType: MainServer.TransactionType_TransactionType_Market_Sale,
		OperationType:   MainServer.TransactionOperationType_TransactionOperationType_List,
		ItemType:        itemType,
		ItemId:          strconv.FormatInt(itemId, 10),
		ItemName:        itemName,
		Quantity:        quantity,
		Price:           price,
		SpecialCoin:     specialCoin,
		SellerTid:       sellerTid,
		BuyerTid:        0,
		MarketId:        marketId,
		ExtraInfo:       "{}",
	}

	return AddTransactionRecord(ctx, logger, tx, record)
}

// RecordMarketDelisting 记录市场下架
func RecordMarketDelisting(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	sellerTid int64, itemType MainServer.TransactionItemType, itemId int64, itemName string,
	quantity int32, marketId string) error {

	record := &MainServer.TransactionRecordInfo{
		Tid:             sellerTid,
		TransactionType: MainServer.TransactionType_TransactionType_Market_Sale,
		OperationType:   MainServer.TransactionOperationType_TransactionOperationType_Delist,
		ItemType:        itemType,
		ItemId:          strconv.FormatInt(itemId, 10),
		ItemName:        itemName,
		Quantity:        quantity,
		Price:           0,
		SpecialCoin:     0,
		SellerTid:       sellerTid,
		BuyerTid:        0,
		MarketId:        marketId,
		ExtraInfo:       "{}",
	}

	return AddTransactionRecord(ctx, logger, tx, record)
}

// GetPurchaseHistory 获取购买历史
func GetPurchaseHistory(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	tid int64, itemType *MainServer.TransactionItemType, limit int32, offset int32) ([]*MainServer.TransactionRecordInfo, error) {

	transactionType := MainServer.TransactionType_TransactionType_Purchase
	operationType := MainServer.TransactionOperationType_TransactionOperationType_Buy

	return GetTransactionRecords(ctx, logger, tx, tid, &transactionType, &operationType, itemType, limit, offset)
}

// GetUsageHistory 获取使用历史
func GetUsageHistory(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	tid int64, limit int32, offset int32) ([]*MainServer.TransactionRecordInfo, error) {

	transactionType := MainServer.TransactionType_TransactionType_Use
	operationType := MainServer.TransactionOperationType_TransactionOperationType_Use
	itemType := MainServer.TransactionItemType_TransactionItemType_Item

	return GetTransactionRecords(ctx, logger, tx, tid, &transactionType, &operationType, &itemType, limit, offset)
}

// GetMarketHistory 获取市场操作历史
func GetMarketHistory(ctx context.Context, logger runtime.Logger, tx *sql.Tx,
	tid int64, operationType *MainServer.TransactionOperationType, limit int32, offset int32) ([]*MainServer.TransactionRecordInfo, error) {

	transactionType := MainServer.TransactionType_TransactionType_Market_Sale

	return GetTransactionRecords(ctx, logger, tx, tid, &transactionType, operationType, nil, limit, offset)
}

// GetTransactionStatistics 获取交易统计
func GetTransactionStatistics(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, days int32) (*MainServer.TransactionStatistics, error) {
	startTime := time.Now().AddDate(0, 0, -int(days)).UnixMilli()

	query := fmt.Sprintf(`
		SELECT 
			transaction_type,
			operation_type,
			item_type,
			COUNT(*) as count,
			COALESCE(SUM(price), 0) as total_price,
			COALESCE(SUM(special_coin), 0) as total_special_coin,
			COALESCE(SUM(quantity), 0) as total_quantity
		FROM %s
		WHERE tid = $1 AND transaction_ts >= $2
		GROUP BY transaction_type, operation_type, item_type
		ORDER BY transaction_type, operation_type, item_type
	`, TableTransactionRecords)

	rows, err := tx.QueryContext(ctx, query, tid, startTime)
	if err != nil {
		logger.Error("Failed to get transaction statistics: %v", err)
		return nil, err
	}
	defer rows.Close()

	typeStats := make(map[string]*MainServer.TransactionTypeStatistics)
	var totalTransactions int32
	var totalSpent int64
	var totalEarned int64

	for rows.Next() {
		var transactionType, operationType, itemType int
		var count int32
		var totalPrice, totalSpecialCoin, totalQuantity int64

		err := rows.Scan(&transactionType, &operationType, &itemType, &count, &totalPrice, &totalSpecialCoin, &totalQuantity)
		if err != nil {
			logger.Error("Failed to scan statistics: %v", err)
			continue
		}

		key := fmt.Sprintf("type_%d_op_%d_item_%d", transactionType, operationType, itemType)
		typeStats[key] = &MainServer.TransactionTypeStatistics{
			Count:            count,
			TotalPrice:       totalPrice,
			TotalSpecialCoin: totalSpecialCoin,
			TotalQuantity:    totalQuantity,
		}

		totalTransactions += count

		// 计算支出和收入
		if operationType == int(MainServer.TransactionOperationType_TransactionOperationType_Buy) {
			totalSpent += totalPrice
		} else if operationType == int(MainServer.TransactionOperationType_TransactionOperationType_Sell) {
			totalEarned += totalPrice
		}
	}

	// 构建proto统计结果
	statistics := &MainServer.TransactionStatistics{
		Tid:               tid,
		TotalTransactions: totalTransactions,
		TotalSpent:        totalSpent,
		TotalEarned:       totalEarned,
		NetAmount:         totalEarned - totalSpent,
		Days:              days,
		TypeStats:         typeStats,
	}

	logger.Info("Generated transaction statistics for tid %d over %d days: %d transactions", tid, days, totalTransactions)
	return statistics, nil
}

// GetRecentTransactions 获取最近的交易记录
func GetRecentTransactions(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, limit int32) ([]*MainServer.TransactionRecordInfo, error) {
	return GetTransactionRecords(ctx, logger, tx, tid, nil, nil, nil, limit, 0)
}

// 不再需要转换函数，直接使用proto定义的数据结构
