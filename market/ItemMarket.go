package market

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 交易所
func RpcBuyTradeStoreItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	var buyItemRequest = &MainServer.RpcBuyItemRequest{}
	if err := tool.Base64ToProto(payload, buyItemRequest); err != nil {
		logger.Error("解析 buyItemRequest 失败: %v", err) // 记录解析失败
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}
	if buyItemRequest.BuySiteType != MainServer.BuySiteType_BuySiteTypeTradeStore || buyItemRequest.ItemId <= 0 {
		return "", runtime.NewError("类型错误", 400)
	}
	activeTrainer := tool.GetActiveTrainerByCtx(ctx)
	if activeTrainer == nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	saleItem, err := inventory.GetItemById(ctx, tx, buyItemRequest.ItemId, buyItemRequest.ItemName)
	if err != nil {
		return "", fmt.Errorf("failed to GetItemById: %v", err)
	}
	count := buyItemRequest.Count
	if saleItem.Quantity < count {
		count = saleItem.Quantity
		// return "", runtime.NewError("数量不足", 400)
	}
	saleItem.Quantity -= count
	err = inventory.UpdateItemQuantity(ctx, tx, saleItem.Id, saleItem.Quantity, saleItem.UpdateTs)
	if err != nil {
		return "", fmt.Errorf("failed to UpsertItem: %v", err)
	}
	err = inventory.AddItemOrUpdate(ctx, tx, activeTrainer.Id, saleItem.ItemName, saleItem.InventoryType, count, saleItem.ItemSaleType, saleItem.TeamType, saleItem.SummonPokeNameId)
	if err != nil {
		return "", fmt.Errorf("failed to AddItemOrUpdate: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return "", nil
}

// func RpcBuyItemsByKeyAndName(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	var buyInventoryItemInfo = &MainServer.BuyInventoryItemInfo{}
// 	if err := tool.Base64ToProto(payload, buyInventoryItemInfo); err != nil {
// 		logger.Error("解析 buyInventoryItemInfo 失败: %v", err) // 记录解析失败
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
// 	}
// 	activeTrainer := tool.GetActiveTrainerByCtx(ctx)
// 	if activeTrainer == nil {
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}
// 	localItem, exists := item.GetItemByKeyAndName(buyInventoryItemInfo.Category, buyInventoryItemInfo.Name)
// 	if !exists {
// 		logger.Error("Failed to query item info")
// 		return "", runtime.NewError("Failed to query item info", 404)
// 	}
// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to begin transaction: %v", err)
// 	}
// 	if activeTrainer.Coin < int64(localItem.Cost) {
// 		return "", runtime.NewError("coin err", 400)
// 	}
// 	err = trainer.Payment(ctx, logger, tx, activeTrainer, 0, int64(localItem.Cost))
// 	if err != nil {
// 		return "", err
// 	}

// 	err = inventory.AddNormalItem(ctx, tx, activeTrainer.Id, localItem.Name, localItem.InventoryType, buyInventoryItemInfo.Count)

// 	if err != nil {
// 		tx.Rollback()
// 		return "", fmt.Errorf("failed to AddItemToInventory: %v", err)
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		return "", fmt.Errorf("failed to commit transaction: %v", err)
// 	}
// 	return "", nil
// }
