package market

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"math/rand"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableMarketPokes = "market_pokes"

func InitMarket(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createMarketPokesTableIfNotExists(ctx, logger, db, TableMarketPokes)
}

// 创建表格并建立索引，如果表不存在
func createMarketPokesTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB, tableName string) error {
	// 定义 SQL 语句，创建表格和必要的索引
	createTableSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id VARCHAR(255) PRIMARY KEY,
			name VARCHAR(255),
			item_id VARCHAR(255),
			evs NOT NULL DEFAULT '{}'::jsonb,
			ivs NOT NULL DEFAULT '{}'::jsonb,
			ability VARCHAR(50)
		);`, tableName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create table %s: %w", tableName, err)
	}

	// 创建 JSON 字段上的索引（可选）
	createIndexSQL := fmt.Sprintf(`
		CREATE INDEX IF NOT EXISTS idx_%s_evs_ivs ON %s USING GIN (evs, ivs);
	`, tableName, tableName)

	_, err = db.ExecContext(ctx, createIndexSQL)
	if err != nil {
		return fmt.Errorf("failed to create index on table %s: %w", tableName, err)
	}
	logger.Info("createMarketPokesTableIfNotExists 成功")
	return nil
}
func TestSalesRandomPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 初始化随机数种子
	rand.Seed(time.Now().UnixMilli())

	// 生成测试数据
	// for i := 0; i < 10; i++ { // 创建 10 个宝可梦
	// 	poke := createRandomPoke(i)
	// 	err := savePokeToTable(ctx, db, TableMarketPokes, &poke)
	// 	if err != nil {
	// 		return "", fmt.Errorf("failed to save poke %s: %w", poke.Id, err)
	// 	}
	// }

	return "Pokes saved successfully", nil
}

// 辅助函数，用于创建随机宝可梦
// func createRandomPoke(index int) MainServer.Poke {
// 	names := []string{"Pikachu", "Charmander", "Bulbasaur", "Squirtle", "Jigglypuff", "Gengar", "Eevee", "Snorlax", "Mewtwo", "Charizard"}

// 	return MainServer.Poke{
// 		Id:     index,                        // 使用 index 作为 ID
// 		Name:   names[rand.Intn(len(names))], // 随机选择宝可梦名字
// 		ItemId: "",
// 		Evs: &MainServer.PokeStat{
// 			Hp:  rand.Int31n(256), // EVS 最大值 255
// 			Atk: rand.Int31n(256),
// 			Def: rand.Int31n(256),
// 			Spa: rand.Int31n(256),
// 			Spd: rand.Int31n(256),
// 			Spe: rand.Int31n(256),
// 		},
// 		Ivs: &MainServer.PokeStat{
// 			Hp:  rand.Int31n(32), // IVS 最大值 31
// 			Atk: rand.Int31n(32),
// 			Def: rand.Int31n(32),
// 			Spa: rand.Int31n(32),
// 			Spd: rand.Int31n(32),
// 			Spe: rand.Int31n(32),
// 		},
// 		Ability: rand.Int31n(3), // 随机选择特性数量
// 	}
// }

// 将宝可梦数据存储到数据库表中
func savePokeToTable(ctx context.Context, db *sql.DB, tableName string, poke *MainServer.Poke) error {
	// 将 evs 和 ivs 转换为 JSON 格式
	evsJSON, err := json.Marshal(poke.Evs)
	if err != nil {
		return fmt.Errorf("failed to serialize evs: %w", err)
	}

	ivsJSON, err := json.Marshal(poke.Ivs)
	if err != nil {
		return fmt.Errorf("failed to serialize ivs: %w", err)
	}

	// 插入数据到表格中，使用 ON CONFLICT 来避免重复主键错误
	insertSQL := fmt.Sprintf(`
		INSERT INTO %s (id, name, item_id, evs, ivs, ability)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (id) DO UPDATE
		SET name = EXCLUDED.name, item_id = EXCLUDED.item_id, evs = EXCLUDED.evs, ivs = EXCLUDED.ivs, ability = EXCLUDED.ability;
	`, tableName)

	itemName := ""
	if poke.ItemInfo != nil {
		itemName = poke.ItemInfo.ItemName
	}
	_, err = db.ExecContext(ctx, insertSQL, poke.Id, poke.Name, itemName, evsJSON, ivsJSON, poke.Ability)
	if err != nil {
		return fmt.Errorf("failed to save poke to table %s: %w", tableName, err)
	}

	return nil
}

// // 存储宝可梦到集合
// func savePokeToCollection(ctx context.Context, nk runtime.NakamaModule, db *sql.DB, tableName string, poke *MainServer.Poke) error {
// 	pokeJSON, err := json.Marshal(poke)
// 	if err != nil {
// 		return runtime.NewError("区域数据序列化失败", 500)
// 	}
// 	object := &api.StorageObject{
// 		Collection: collection,
// 		Key:        poke.Id,
// 		Value:      string(pokeJSON), // 将 poke 转换为 JSON
// 	}

// 	// 创建一个包含单个 StorageWrite 的切片
// 	storageWrites := []*runtime.StorageWrite{
// 		{
// 			Collection: object.Collection,
// 			Key:        object.Key,
// 			Value:      object.Value,
// 		},
// 	}

// 	// 存储到 Nakama
// 	_, err = nk.StorageWrite(ctx, storageWrites)
// 	if err != nil {
// 		return fmt.Errorf("failed to write poke to storage: %w", err)
// 	}

// 	return nil
// }

// func QueryMarketPokesSql(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	filter := MainServer.MarketPokeFilter{
// 		Names:         []string{"Pikachu", "Bulbasaur", "Charizard"},
// 		MinEvs:        &MainServer.PokeStat{Hp: 0, Atk: 0, Def: 0, Spa: 0, Spd: 0, Spe: 0},
// 		MinIvs:        &MainServer.PokeStat{Hp: 0, Atk: 0},
// 		Sort:          MainServer.MarketPokeSort_ivs,
// 		Page:          1,
// 		PageSize:      10,
// 		MinPrice:      0,
// 		MaxPrice:      0,
// 		Ability:    2,
// 		AbilityLength: 3,
// 	}
// 	result, err := QueryMarketSqlPokes(db, &filter)
// 	if err != nil {
// 		return "", runtime.NewError("查询失败: "+err.Error(), 500)
// 	}
// 	// 序列化结果
// 	resultStr, err := json.Marshal(result)
// 	if err != nil {
// 		return "", runtime.NewError("结果序列化失败: "+err.Error(), 500)
// 	}
// 	return string(resultStr), err
// }

// func QueryMarketPokesCollection(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	return "", nil
// }

// // 函数：通过 MarketPokeFilter 进行筛选和排序
// func QueryMarketPokes(filter *MainServer.MarketPokeFilter, ctx context.Context, nk runtime.NakamaModule) ([]*api.StorageObject, error) {
// 	collection := TableMarketPokes
// 	query := ""

// 	// 名称匹配（Name matching）
// 	if len(filter.Names) > 0 {
// 		namesQuery := fmt.Sprintf("(value->'poke'->>'name') IN ('%s')", strings.Join(filter.Names, "', '"))
// 		query += namesQuery
// 	}

// 	// EVS 筛选条件
// 	if filter.MinEvs.Hp > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'hp')::int >= %d", filter.MinEvs.Hp)
// 	}
// 	if filter.MinEvs.Atk > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'atk')::int >= %d", filter.MinEvs.Atk)
// 	}
// 	if filter.MinEvs.Def > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'def')::int >= %d", filter.MinEvs.Def)
// 	}
// 	if filter.MinEvs.Spa > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'spa')::int >= %d", filter.MinEvs.Spa)
// 	}
// 	if filter.MinEvs.Spd > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'spd')::int >= %d", filter.MinEvs.Spd)
// 	}
// 	if filter.MinEvs.Spe > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'evs'->>'spe')::int >= %d", filter.MinEvs.Spe)
// 	}

// 	// IVS 筛选条件
// 	if filter.MinIvs.Hp > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'hp')::int >= %d", filter.MinIvs.Hp)
// 	}
// 	if filter.MinIvs.Atk > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'atk')::int >= %d", filter.MinIvs.Atk)
// 	}
// 	if filter.MinIvs.Def > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'def')::int >= %d", filter.MinIvs.Def)
// 	}
// 	if filter.MinIvs.Spa > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'spa')::int >= %d", filter.MinIvs.Spa)
// 	}
// 	if filter.MinIvs.Spd > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'spd')::int >= %d", filter.MinIvs.Spd)
// 	}
// 	if filter.MinIvs.Spe > 0 {
// 		query += fmt.Sprintf(" AND (value->'poke'->'ivs'->>'spe')::int >= %d", filter.MinIvs.Spe)
// 	}
// 	// Price 筛选条件
// 	if filter.MinPrice > 0 {
// 		query += fmt.Sprintf(" AND (value->>'price')::numeric >= %f", filter.MinPrice)
// 	}
// 	if filter.MaxPrice > 0 {
// 		query += fmt.Sprintf(" AND (value->>'price')::numeric <= %f", filter.MaxPrice)
// 	}

// 	// ability 筛选条件
// 	if filter.AbilityLength > 0 && filter.Ability >= 0 {
// 		abilityCondition := fmt.Sprintf(
// 			"((value->'poke'->>'ability')::int %% %d) <= %d",
// 			filter.AbilityLength, filter.AbilityLength-filter.Ability-1,
// 		)
// 		query += " AND " + abilityCondition
// 	}

// 	// 排序条件 (Sorting)
// 	var order []string // 使用切片来保存排序字段
// 	switch filter.Sort {
// 	case MainServer.MarketPokeSort_ivs:
// 		// IVS 排序（按 IVS 各项数值相加总和排序）
// 		order = append(order, "-((value->'poke'->'ivs'->>'hp')::int + (value->'poke'->'ivs'->>'atk')::int + (value->'poke'->'ivs'->>'def')::int + (value->'poke'->'ivs'->>'spa')::int + (value->'poke'->'ivs'->>'spd')::int + (value->'poke'->'ivs'->>'spe')::int)")
// 	case MainServer.MarketPokeSort_evs:
// 		// EVS 排序（按 EVS 各项数值相加总和排序）
// 		order = append(order, "-((value->'poke'->'evs'->>'hp')::int + (value->'poke'->'evs'->>'atk')::int + (value->'poke'->'evs'->>'def')::int + (value->'poke'->'evs'->>'spa')::int + (value->'poke'->'evs'->>'spd')::int + (value->'poke'->'evs'->>'spe')::int)")
// 	case MainServer.MarketPokeSort_price:
// 		// Price 排序（价格从低到高）
// 		order = append(order, "(value->>'price')::numeric")
// 	}

// 	// 设置分页和查询
// 	limit := filter.PageSize

// 	// 执行查询
// 	storageObjects, err := nk.StorageIndexList(ctx, "", collection, query, int(limit), order)
// 	if err != nil {
// 		return nil, fmt.Errorf("error listing storage index entries: %w", err)
// 	}
// 	return storageObjects.Objects, nil
// }
