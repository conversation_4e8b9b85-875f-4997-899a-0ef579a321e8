# 添加构建参数
ARG NAKAMA_VERSION=3.27.1
# exec /nakama/nakama --database.address postgres:localdb@postgres:5432/nakama
# 修改基础镜像引用方式
FROM registry.heroiclabs.com/heroiclabs/nakama-pluginbuilder:${NAKAMA_VERSION} AS builder

# 设置环境变量
ENV GO111MODULE=on CGO_ENABLED=1
WORKDIR /backend

# 合并RUN指令，减少层数
RUN apt-get update && \
    apt-get -y upgrade && \
    apt-get install -y --no-install-recommends gcc libc6-dev && \
    go install github.com/go-delve/delve/cmd/dlv@latest && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
# # 将安装依赖等不常变动的步骤放在 COPY 之前
# RUN apt-get update && \
#     apt-get -y upgrade && \
#     apt-get install -y --no-install-recommends gcc libc6-dev

# # 安装 delve 调试工具，这步也可以缓存
# RUN go install github.com/go-delve/delve/cmd/dlv@latest
# 复制依赖文件并安装依赖
# 先复制go.mod和go.sum（如果有）以利用缓存
COPY go.mod go.sum* ./
RUN go mod download

COPY vendor/ vendor/

# # 复制所有源文件
COPY . .

# 编译生成插件
RUN go build --trimpath --gcflags "all=-N -l" --mod=vendor --buildmode=plugin -o ./backend.so

# 使用 Nakama dsym 镜像
FROM registry.heroiclabs.com/heroiclabs/nakama-dsym:${NAKAMA_VERSION}
# FROM registry.heroiclabs.com/heroiclabs/nakama:${NAKAMA_VERSION}

# 将构建的二进制文件和配置文件复制到目标镜像
COPY --from=builder /go/bin/dlv /nakama
COPY --from=builder /backend/backend.so /nakama/data/modules/
COPY local.yml /nakama/data/
COPY poke/data/*.json /nakama/data/
COPY poke/data/*.bytes /nakama/data/
COPY quest/data/*.json /nakama/data/
COPY quest/data/*.bytes /nakama/data/

# 设置工作目录
WORKDIR /nakama

# 可选：设置启动命令
# CMD ["./nakama", "server"]
# ENTRYPOINT [ "/bin/bash" ]