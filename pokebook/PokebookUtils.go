package pokebook

import (
	"fmt"
	"go-nakama-poke/proto/MainServer"
)

// 图鉴工具函数

// 将图鉴数据转换为可读的状态映射
func ConvertPokebookToStatusMap(pokebook *MainServer.TrainerPokebook) (map[int32]MainServer.PokedexStatus, error) {
	statusMap := make(map[int32]MainServer.PokedexStatus)

	for i := int32(1); i <= pokebook.TotalPokemonCount; i++ {
		status, err := GetPokemonStatus(pokebook.PokedexData, i)
		if err != nil {
			return nil, fmt.Errorf("获取精灵%d状态失败: %w", i, err)
		}
		statusMap[i] = status
	}

	return statusMap, nil
}

// 从状态映射创建图鉴数据
func CreatePokebookFromStatusMap(tid int64, statusMap map[int32]MainServer.PokedexStatus) (*MainServer.TrainerPokebook, error) {
	// 找到最大的精灵ID
	maxPokemonId := int32(1025) // 默认值
	for pokemonId := range statusMap {
		if pokemonId > maxPokemonId {
			maxPokemonId = pokemonId
		}
	}

	// 计算需要的字节数
	bytesNeeded := (maxPokemonId + 3) / 4
	pokedexData := make([]byte, bytesNeeded)

	// 设置每只精灵的状态
	for pokemonId, status := range statusMap {
		if pokemonId < 1 {
			continue
		}
		err := setPokemonStatusInData(pokedexData, pokemonId, status)
		if err != nil {
			return nil, fmt.Errorf("设置精灵%d状态失败: %w", pokemonId, err)
		}
	}

	pokebook := &MainServer.TrainerPokebook{
		Tid:               tid,
		PokedexData:       pokedexData,
		TotalPokemonCount: maxPokemonId,
	}

	return pokebook, nil
}

// 验证图鉴数据完整性
func ValidatePokebookData(pokebook *MainServer.TrainerPokebook) error {
	if pokebook == nil {
		return fmt.Errorf("图鉴数据为空")
	}

	if pokebook.Tid <= 0 {
		return fmt.Errorf("训练师ID无效: %d", pokebook.Tid)
	}

	if pokebook.TotalPokemonCount <= 0 {
		return fmt.Errorf("总精灵数无效: %d", pokebook.TotalPokemonCount)
	}

	// 检查数据长度是否足够
	expectedBytes := (pokebook.TotalPokemonCount + 3) / 4
	if int32(len(pokebook.PokedexData)) < expectedBytes {
		return fmt.Errorf("图鉴数据长度不足，期望%d字节，实际%d字节", expectedBytes, len(pokebook.PokedexData))
	}

	return nil
}

// 比较两个图鉴数据的差异
func ComparePokebooks(oldPokebook, newPokebook *MainServer.TrainerPokebook) (map[int32]PokedexStatusChange, error) {
	changes := make(map[int32]PokedexStatusChange)

	// 确定比较范围
	maxCount := oldPokebook.TotalPokemonCount
	if newPokebook.TotalPokemonCount > maxCount {
		maxCount = newPokebook.TotalPokemonCount
	}

	for i := int32(1); i <= maxCount; i++ {
		oldStatus, _ := GetPokemonStatus(oldPokebook.PokedexData, i)
		newStatus, _ := GetPokemonStatus(newPokebook.PokedexData, i)

		if oldStatus != newStatus {
			changes[i] = PokedexStatusChange{
				PokemonId: i,
				OldStatus: oldStatus,
				NewStatus: newStatus,
			}
		}
	}

	return changes, nil
}

// 状态变化结构
type PokedexStatusChange struct {
	PokemonId int32
	OldStatus MainServer.PokedexStatus
	NewStatus MainServer.PokedexStatus
}

// 获取状态变化的描述
func (c PokedexStatusChange) GetDescription() string {
	oldDesc := getStatusDescription(c.OldStatus)
	newDesc := getStatusDescription(c.NewStatus)
	return fmt.Sprintf("精灵%d: %s -> %s", c.PokemonId, oldDesc, newDesc)
}

// 获取状态描述
func getStatusDescription(status MainServer.PokedexStatus) string {
	switch status {
	case MainServer.PokedexStatus_PokedexStatus_NotSeen:
		return "未见过"
	case MainServer.PokedexStatus_PokedexStatus_Seen:
		return "见过"
	case MainServer.PokedexStatus_PokedexStatus_Caught:
		return "捕捉过"
	case MainServer.PokedexStatus_PokedexStatus_CaughtShiny:
		return "捕捉过闪光"
	default:
		return "未知状态"
	}
}

// 计算图鉴完成度详细信息
func CalculateDetailedStats(pokebook *MainServer.TrainerPokebook) (*DetailedPokebookStats, error) {
	stats := &DetailedPokebookStats{
		TotalPokemon: pokebook.TotalPokemonCount,
		StatusCounts: make(map[MainServer.PokedexStatus]int32),
		Regions:      make(map[string]*RegionStats),
	}

	// 统计各状态数量
	for i := int32(1); i <= pokebook.TotalPokemonCount; i++ {
		status, err := GetPokemonStatus(pokebook.PokedexData, i)
		if err != nil {
			continue
		}
		stats.StatusCounts[status]++
	}

	// 计算完成度
	seenTotal := stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_Seen] +
		stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_Caught] +
		stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_CaughtShiny]

	if stats.TotalPokemon > 0 {
		stats.CompletionRate = float32(seenTotal) / float32(stats.TotalPokemon) * 100
	}

	// 计算捕获率
	caughtTotal := stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_Caught] +
		stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_CaughtShiny]

	if seenTotal > 0 {
		stats.CatchRate = float32(caughtTotal) / float32(seenTotal) * 100
	}

	// 计算闪光率
	if caughtTotal > 0 {
		stats.ShinyRate = float32(stats.StatusCounts[MainServer.PokedexStatus_PokedexStatus_CaughtShiny]) / float32(caughtTotal) * 100
	}

	return stats, nil
}

// 详细统计信息
type DetailedPokebookStats struct {
	TotalPokemon   int32                              `json:"total_pokemon"`
	StatusCounts   map[MainServer.PokedexStatus]int32 `json:"status_counts"`
	CompletionRate float32                            `json:"completion_rate"`
	CatchRate      float32                            `json:"catch_rate"`
	ShinyRate      float32                            `json:"shiny_rate"`
	Regions        map[string]*RegionStats            `json:"regions"`
}

// 地区统计信息
type RegionStats struct {
	Name           string                             `json:"name"`
	TotalPokemon   int32                              `json:"total_pokemon"`
	StatusCounts   map[MainServer.PokedexStatus]int32 `json:"status_counts"`
	CompletionRate float32                            `json:"completion_rate"`
}

// 导出图鉴数据为JSON格式（用于备份或调试）
func ExportPokebookToJSON(pokebook *MainServer.TrainerPokebook) (map[string]interface{}, error) {
	statusMap, err := ConvertPokebookToStatusMap(pokebook)
	if err != nil {
		return nil, err
	}

	// 转换状态为字符串
	statusStrMap := make(map[string]string)
	for pokemonId, status := range statusMap {
		if status != MainServer.PokedexStatus_PokedexStatus_NotSeen {
			statusStrMap[fmt.Sprintf("%d", pokemonId)] = getStatusDescription(status)
		}
	}

	result := map[string]interface{}{
		"tid":                 pokebook.Tid,
		"total_pokemon_count": pokebook.TotalPokemonCount,
		"create_ts":           pokebook.CreateTs,
		"update_ts":           pokebook.UpdateTs,
		"pokemon_status":      statusStrMap,
	}

	return result, nil
}

// 从JSON导入图鉴数据
func ImportPokebookFromJSON(data map[string]interface{}) (*MainServer.TrainerPokebook, error) {
	tid, ok := data["tid"].(float64)
	if !ok {
		return nil, fmt.Errorf("无效的训练师ID")
	}

	_, ok = data["total_pokemon_count"].(float64)
	if !ok {
		return nil, fmt.Errorf("无效的总精灵数")
	}

	statusData, ok := data["pokemon_status"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的精灵状态数据")
	}

	// 转换状态数据
	statusMap := make(map[int32]MainServer.PokedexStatus)
	for pokemonIdStr, statusStr := range statusData {
		pokemonId := int32(0)
		fmt.Sscanf(pokemonIdStr, "%d", &pokemonId)

		status := parseStatusFromString(statusStr.(string))
		statusMap[pokemonId] = status
	}

	return CreatePokebookFromStatusMap(int64(tid), statusMap)
}

// 从字符串解析状态
func parseStatusFromString(statusStr string) MainServer.PokedexStatus {
	switch statusStr {
	case "见过":
		return MainServer.PokedexStatus_PokedexStatus_Seen
	case "捕捉过":
		return MainServer.PokedexStatus_PokedexStatus_Caught
	case "捕捉过闪光":
		return MainServer.PokedexStatus_PokedexStatus_CaughtShiny
	default:
		return MainServer.PokedexStatus_PokedexStatus_NotSeen
	}
}

// 检查是否可以升级状态（防止状态倒退）
func CanUpgradeStatus(currentStatus, newStatus MainServer.PokedexStatus) bool {
	// 状态优先级：NotSeen < Seen < Caught < CaughtShiny
	return int(newStatus) > int(currentStatus)
}

// 安全地升级精灵状态（只允许向更高级状态升级）
func SafeUpgradePokemonStatus(pokedexData []byte, pokemonId int32, newStatus MainServer.PokedexStatus) error {
	currentStatus, err := GetPokemonStatus(pokedexData, pokemonId)
	if err != nil {
		return err
	}

	if !CanUpgradeStatus(currentStatus, newStatus) {
		return fmt.Errorf("不能将精灵%d的状态从%s降级到%s",
			pokemonId, getStatusDescription(currentStatus), getStatusDescription(newStatus))
	}

	return setPokemonStatusInData(pokedexData, pokemonId, newStatus)
}
