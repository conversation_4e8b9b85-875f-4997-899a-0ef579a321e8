package pokebook

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 注册所有图鉴相关的RPC函数
// func RegisterPokebookRPCs(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
// 	// 注册用户RPC函数
// 	if err := initializer.RegisterRpc("RpcGetTrainerPokebook", RpcGetTrainerPokebook); err != nil {
// 		logger.Error("注册RpcGetTrainerPokebook失败: %v", err)
// 		return err
// 	}

// 	if err := initializer.RegisterRpc("RpcSetPokemonStatus", RpcSetPokemonStatus); err != nil {
// 		logger.Error("注册RpcSetPokemonStatus失败: %v", err)
// 		return err
// 	}

// 	if err := initializer.RegisterRpc("RpcBatchSetPokemonStatus", RpcBatchSetPokemonStatus); err != nil {
// 		logger.Error("注册RpcBatchSetPokemonStatus失败: %v", err)
// 		return err
// 	}

// 	if err := initializer.RegisterRpc("RpcGetPokebookStats", RpcGetPokebookStats); err != nil {
// 		logger.Error("注册RpcGetPokebookStats失败: %v", err)
// 		return err
// 	}

// 	if err := initializer.RegisterRpc("RpcExtendPokebookCapacity", RpcExtendPokebookCapacity); err != nil {
// 		logger.Error("注册RpcExtendPokebookCapacity失败: %v", err)
// 		return err
// 	}

// 	if err := initializer.RegisterRpc("RpcGetPokemonByStatus", RpcGetPokemonByStatus); err != nil {
// 		logger.Error("注册RpcGetPokemonByStatus失败: %v", err)
// 		return err
// 	}

// 	// 注册管理员RPC函数
// 	if err := initializer.RegisterRpc("RpcAdminResetTrainerPokebook", RpcAdminResetTrainerPokebook); err != nil {
// 		logger.Error("注册RpcAdminResetTrainerPokebook失败: %v", err)
// 		return err
// 	}

// 	logger.Info("精灵图鉴RPC函数注册完成")
// 	return nil
// }

// // 完整的图鉴系统初始化
// func InitializePokebookSystem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
// 	logger.Info("开始初始化精灵图鉴系统...")

// 	// 1. 初始化数据库表
// 	InitPokebook(ctx, logger, db)

// 	// 2. 注册RPC函数
// 	err := RegisterPokebookRPCs(ctx, logger, db, nk, initializer)
// 	if err != nil {
// 		logger.Error("注册图鉴RPC函数失败: %v", err)
// 		return err
// 	}

// 	// 3. 运行测试（可选，仅在开发环境）
// 	// RunAllTests()

// 	logger.Info("精灵图鉴系统初始化完成")
// 	return nil
// }

// 获取系统信息
func GetPokebookSystemInfo() map[string]interface{} {
	return map[string]interface{}{
		"system_name":       "Pokemon Pokedex System",
		"version":           "1.0.0",
		"max_pokemon_count": 1025,
		"bits_per_pokemon":  2,
		"storage_format":    "BYTEA",
		"supported_status": []string{
			"NotSeen (00)",
			"Seen (01)",
			"Caught (10)",
			"CaughtShiny (11)",
		},
		"features": []string{
			"高效存储 (2bit per Pokemon)",
			"动态扩展精灵数量",
			"批量操作支持",
			"统计功能",
			"数据导入导出",
			"状态验证",
		},
		"rpc_functions": []string{
			"RpcGetTrainerPokebook",
			"RpcSetPokemonStatus",
			"RpcBatchSetPokemonStatus",
			"RpcGetPokebookStats",
			"RpcExtendPokebookCapacity",
			"RpcGetPokemonByStatus",
			"RpcAdminResetTrainerPokebook",
		},
	}
}

// 系统健康检查
func HealthCheck(ctx context.Context, db *sql.DB) error {
	// 检查数据库表是否存在
	query := `SELECT EXISTS (
		SELECT FROM information_schema.tables 
		WHERE table_name = $1
	)`

	var exists bool
	err := db.QueryRowContext(ctx, query, TablePokebookName).Scan(&exists)
	if err != nil {
		return err
	}

	if !exists {
		return fmt.Errorf("图鉴表 %s 不存在", TablePokebookName)
	}

	return nil
}

// 系统统计信息
func GetSystemStats(ctx context.Context, db *sql.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计总训练师数量
	var totalTrainers int64
	err := db.QueryRowContext(ctx, fmt.Sprintf("SELECT COUNT(*) FROM %s", TablePokebookName)).Scan(&totalTrainers)
	if err != nil {
		return nil, err
	}
	stats["total_trainers"] = totalTrainers

	// 统计平均完成度（这需要更复杂的查询，这里简化处理）
	stats["average_completion"] = "需要实现复杂查询"

	// 统计数据库大小
	var tableSize string
	err = db.QueryRowContext(ctx,
		`SELECT pg_size_pretty(pg_total_relation_size($1))`,
		TablePokebookName).Scan(&tableSize)
	if err == nil {
		stats["table_size"] = tableSize
	}

	return stats, nil
}

// 数据迁移和升级
func MigratePokebookData(ctx context.Context, logger runtime.Logger, db *sql.DB, fromVersion, toVersion string) error {
	logger.Info("开始图鉴数据迁移，从版本 %s 到 %s", fromVersion, toVersion)

	// 这里可以添加版本升级逻辑
	switch {
	case fromVersion == "1.0.0" && toVersion == "1.1.0":
		// 示例：添加新字段或修改表结构
		logger.Info("执行 1.0.0 到 1.1.0 的迁移")
		// 具体迁移逻辑

	default:
		logger.Warn("未找到从 %s 到 %s 的迁移路径", fromVersion, toVersion)
	}

	logger.Info("图鉴数据迁移完成")
	return nil
}

// 批量初始化训练师图鉴（用于新系统部署）
func BatchInitializeTrainerPokebooks(ctx context.Context, logger runtime.Logger, db *sql.DB, trainerIds []int64) error {
	logger.Info("开始批量初始化 %d 个训练师的图鉴", len(trainerIds))

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	successCount := 0
	for _, tid := range trainerIds {
		// 检查是否已存在
		var exists bool
		err := tx.QueryRowContext(ctx,
			fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE tid = $1)", TablePokebookName),
			tid).Scan(&exists)
		if err != nil {
			logger.Error("检查训练师 %d 图鉴失败: %v", tid, err)
			continue
		}

		if exists {
			logger.Debug("训练师 %d 图鉴已存在，跳过", tid)
			continue
		}

		// 创建新图鉴
		_, err = createNewPokebook(ctx, tx, tid)
		if err != nil {
			logger.Error("创建训练师 %d 图鉴失败: %v", tid, err)
			continue
		}

		successCount++
		if successCount%100 == 0 {
			logger.Info("已初始化 %d 个训练师图鉴", successCount)
		}
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	logger.Info("批量初始化完成，成功创建 %d 个图鉴", successCount)
	return nil
}

// 清理过期或无效数据
func CleanupPokebookData(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	logger.Info("开始清理图鉴数据")

	// 这里可以添加清理逻辑，比如：
	// 1. 删除无效的训练师图鉴
	// 2. 压缩数据
	// 3. 重建索引

	logger.Info("图鉴数据清理完成")
	return nil
}
