# 精灵图鉴系统 (Pokebook System)

这是一个高效的精灵图鉴系统，使用2bit存储每只精灵的状态，支持1025+只精灵的图鉴记录。

## 功能特性

### 核心功能
- **高效存储**: 每只精灵仅使用2bit存储状态，1025只精灵约需257字节
- **四种状态**: 未见过(00)、见过(01)、捕捉过(10)、捕捉过闪光(11)
- **动态扩展**: 支持动态添加新精灵，自动扩展存储容量
- **批量操作**: 支持批量设置精灵状态，减少数据库交互
- **统计功能**: 提供详细的图鉴完成度统计

### 数据库设计
- 使用BYTEA类型存储二进制数据
- 每个训练师一条记录，支持唯一约束
- 包含创建和更新时间戳
- 优化的索引设计

## 文件结构

```
pokebook/
├── Pokebook.go           # 核心数据库操作函数
├── PokebookRpc.go        # RPC接口实现
├── PokebookUtils.go      # 工具函数和数据转换
├── proto_definitions.md  # Protocol Buffer定义
└── README.md            # 使用说明
```

## 使用方法

### 1. 初始化系统

在main.go中添加初始化代码：

```go
import "go-nakama-poke/pokebook"

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
    // 初始化图鉴系统
    pokebook.InitPokebook(ctx, logger, db)
    
    // 注册RPC函数
    if err := initializer.RegisterRpc("RpcGetTrainerPokebook", pokebook.RpcGetTrainerPokebook); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcSetPokemonStatus", pokebook.RpcSetPokemonStatus); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcBatchSetPokemonStatus", pokebook.RpcBatchSetPokemonStatus); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcGetPokebookStats", pokebook.RpcGetPokebookStats); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcExtendPokebookCapacity", pokebook.RpcExtendPokebookCapacity); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcGetPokemonByStatus", pokebook.RpcGetPokemonByStatus); err != nil {
        return err
    }
    if err := initializer.RegisterRpc("RpcAdminResetTrainerPokebook", pokebook.RpcAdminResetTrainerPokebook); err != nil {
        return err
    }
    
    return nil
}
```

### 2. 基本操作

#### 获取训练师图鉴
```go
pokebook, err := pokebook.GetTrainerPokebook(ctx, tx, trainerID)
```

#### 设置精灵状态
```go
err := pokebook.SetPokemonStatus(ctx, tx, trainerID, pokemonID, pokebook.MainServer.PokedexStatus_PokedexStatus_Caught)
```

#### 批量设置状态
```go
updates := map[int32]pokebook.PokedexStatus{
    25: pokebook.MainServer.PokedexStatus_PokedexStatus_Caught,      // 皮卡丘
    26: pokebook.MainServer.PokedexStatus_PokedexStatus_CaughtShiny, // 雷丘闪光
}
err := pokebook.BatchSetPokemonStatus(ctx, tx, trainerID, updates)
```

#### 获取统计信息
```go
stats, err := pokebook.GetPokebookStats(ctx, tx, trainerID)
```

### 3. RPC调用示例

#### 客户端调用（C#）
```csharp
// 获取图鉴数据
var pokebook = await CallRpc<TrainerPokebook>("RpcGetTrainerPokebook", "");

// 设置精灵状态
var request = new RpcSetPokemonStatusRequest
{
    PokemonId = 25,
    Status = PokedexStatus.Caught
};
var response = await CallRpc<RpcSetPokemonStatusResponse>("RpcSetPokemonStatus", request);

// 获取统计信息
var stats = await CallRpc<PokebookStats>("RpcGetPokebookStats", "");
```

### 4. 数据解析（C#客户端）

```csharp
public static PokedexStatus GetPokemonStatus(byte[] pokedexData, int pokemonId)
{
    if (pokemonId < 1) return PokedexStatus.NotSeen;
    
    int index = pokemonId - 1;
    int byteIndex = index / 4;
    int bitOffset = (index % 4) * 2;
    
    if (byteIndex >= pokedexData.Length) return PokedexStatus.NotSeen;
    
    byte mask = (byte)(0x03 << bitOffset);
    byte statusBits = (byte)((pokedexData[byteIndex] & mask) >> bitOffset);
    
    return (PokedexStatus)statusBits;
}
```

## 状态说明

| 状态值 | 二进制 | 枚举名称 | 中文描述 |
|--------|--------|----------|----------|
| 0 | 00 | MainServer.PokedexStatus_PokedexStatus_NotSeen | 未见过 |
| 1 | 01 | MainServer.PokedexStatus_PokedexStatus_Seen | 见过 |
| 2 | 10 | MainServer.PokedexStatus_PokedexStatus_Caught | 捕捉过 |
| 3 | 11 | MainServer.PokedexStatus_PokedexStatus_CaughtShiny | 捕捉过闪光 |

## 性能优化

### 存储优化
- 每只精灵仅占用2bit，极大节省存储空间
- 1025只精灵仅需257字节存储空间
- 使用BYTEA类型，数据库原生支持

### 查询优化
- 提供批量操作接口，减少数据库交互
- 索引优化，支持快速查询
- 内存中操作，减少序列化开销

### 扩展性
- 支持动态添加新精灵
- 自动扩展存储容量
- 向后兼容的数据格式

## 注意事项

1. **精灵ID从1开始**: 精灵ID必须从1开始，0为无效ID
2. **状态升级**: 建议只允许状态向更高级升级（见过→捕捉→闪光）
3. **数据完整性**: 使用事务确保数据一致性
4. **容量扩展**: 新增精灵时需要扩展所有训练师的图鉴容量

## 错误处理

系统提供详细的错误信息：
- 无效的精灵ID
- 数据长度不足
- 状态降级错误
- 数据库操作失败

## 测试建议

1. 测试基本的CRUD操作
2. 测试边界条件（精灵ID为0、负数、超大值）
3. 测试批量操作的性能
4. 测试数据完整性和一致性
5. 测试容量扩展功能

## 未来扩展

可以考虑添加以下功能：
- 精灵遇见时间记录
- 地区图鉴支持
- 图鉴成就系统
- 数据导入导出功能
- 图鉴分享功能
