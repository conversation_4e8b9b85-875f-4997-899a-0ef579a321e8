package pokebook

import (
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"testing"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 测试用的模拟logger
type MockLogger struct{}

func (m *MockLogger) Debug(format string, v ...interface{}) {}
func (m *MockLogger) Info(format string, v ...interface{})  {}
func (m *MockLogger) Warn(format string, v ...interface{})  {}
func (m *MockLogger) Error(format string, v ...interface{}) {}
func (m *MockLogger) WithField(key string, v interface{}) runtime.Logger {
	return m
}
func (m *MockLogger) WithFields(fields map[string]interface{}) runtime.Logger {
	return m
}
func (m *MockLogger) Fields() map[string]interface{} {
	return make(map[string]interface{})
}

// 测试基本的图鉴操作
func TestBasicPokebookOperations(t *testing.T) {
	// 这里需要实际的数据库连接来运行测试
	// 以下是测试逻辑的示例

	fmt.Println("=== 精灵图鉴系统测试 ===")

	// 测试数据
	testTid := int64(12345)

	// 测试创建图鉴
	fmt.Println("1. 测试创建图鉴...")
	pokebook := &MainServer.TrainerPokebook{
		Tid:               testTid,
		TotalPokemonCount: 1025,
		CreateTs:          time.Now().Unix(),
		UpdateTs:          time.Now().Unix(),
	}

	// 初始化图鉴数据
	bytesNeeded := (pokebook.TotalPokemonCount + 3) / 4
	pokebook.PokedexData = make([]byte, bytesNeeded)

	fmt.Printf("图鉴创建成功，训练师ID: %d, 总精灵数: %d, 数据大小: %d字节\n",
		pokebook.Tid, pokebook.TotalPokemonCount, len(pokebook.PokedexData))

	// 测试设置精灵状态
	fmt.Println("\n2. 测试设置精灵状态...")
	testCases := []struct {
		pokemonId int32
		status    MainServer.PokedexStatus
		desc      string
	}{
		{1, MainServer.PokedexStatus_PokedexStatus_Seen, "妙蛙种子 - 见过"},
		{4, MainServer.PokedexStatus_PokedexStatus_Caught, "小火龙 - 捕捉"},
		{7, MainServer.PokedexStatus_PokedexStatus_Caught, "杰尼龟 - 捕捉"},
		{25, MainServer.PokedexStatus_PokedexStatus_CaughtShiny, "皮卡丘 - 闪光"},
		{150, MainServer.PokedexStatus_PokedexStatus_Seen, "超梦 - 见过"},
	}

	for _, tc := range testCases {
		err := setPokemonStatusInData(pokebook.PokedexData, tc.pokemonId, tc.status)
		if err != nil {
			fmt.Printf("设置精灵%d状态失败: %v\n", tc.pokemonId, err)
			continue
		}

		// 验证状态
		status, err := GetPokemonStatus(pokebook.PokedexData, tc.pokemonId)
		if err != nil {
			fmt.Printf("获取精灵%d状态失败: %v\n", tc.pokemonId, err)
			continue
		}

		if status == tc.status {
			fmt.Printf("✓ %s - 设置成功\n", tc.desc)
		} else {
			fmt.Printf("✗ %s - 设置失败，期望%d，实际%d\n", tc.desc, tc.status, status)
		}
	}

	// 测试统计功能
	fmt.Println("\n3. 测试统计功能...")
	statusCounts := make(map[MainServer.PokedexStatus]int32)
	for i := int32(1); i <= pokebook.TotalPokemonCount; i++ {
		status, err := GetPokemonStatus(pokebook.PokedexData, i)
		if err != nil {
			continue
		}
		statusCounts[status]++
	}

	fmt.Printf("统计结果:\n")
	fmt.Printf("- 未见过: %d\n", statusCounts[MainServer.PokedexStatus_PokedexStatus_NotSeen])
	fmt.Printf("- 见过: %d\n", statusCounts[MainServer.PokedexStatus_PokedexStatus_Seen])
	fmt.Printf("- 捕捉过: %d\n", statusCounts[MainServer.PokedexStatus_PokedexStatus_Caught])
	fmt.Printf("- 捕捉过闪光: %d\n", statusCounts[MainServer.PokedexStatus_PokedexStatus_CaughtShiny])

	// 计算完成度
	seenTotal := statusCounts[MainServer.PokedexStatus_PokedexStatus_Seen] +
		statusCounts[MainServer.PokedexStatus_PokedexStatus_Caught] +
		statusCounts[MainServer.PokedexStatus_PokedexStatus_CaughtShiny]
	completionRate := float32(seenTotal) / float32(pokebook.TotalPokemonCount) * 100
	fmt.Printf("- 完成度: %.2f%%\n", completionRate)

	// 测试数据验证
	fmt.Println("\n4. 测试数据验证...")
	err := ValidatePokebookData(pokebook)
	if err != nil {
		fmt.Printf("✗ 数据验证失败: %v\n", err)
	} else {
		fmt.Printf("✓ 数据验证通过\n")
	}

	// 测试边界条件
	fmt.Println("\n5. 测试边界条件...")

	// 测试无效精灵ID
	_, err = GetPokemonStatus(pokebook.PokedexData, 0)
	if err != nil {
		fmt.Printf("✓ 正确处理无效精灵ID(0): %v\n", err)
	}

	_, err = GetPokemonStatus(pokebook.PokedexData, -1)
	if err != nil {
		fmt.Printf("✓ 正确处理无效精灵ID(-1): %v\n", err)
	}

	// 测试超出范围的精灵ID
	status, err := GetPokemonStatus(pokebook.PokedexData, 2000)
	if err == nil && status == MainServer.PokedexStatus_PokedexStatus_NotSeen {
		fmt.Printf("✓ 正确处理超出范围的精灵ID(2000): 返回未见过状态\n")
	}

	fmt.Println("\n=== 测试完成 ===")
}

// 测试批量操作
func TestBatchOperations() {
	fmt.Println("\n=== 批量操作测试 ===")

	testTid := int64(67890)
	statusMap := map[int32]MainServer.PokedexStatus{
		1:   MainServer.PokedexStatus_PokedexStatus_Caught,      // 妙蛙种子
		2:   MainServer.PokedexStatus_PokedexStatus_Seen,        // 妙蛙草
		3:   MainServer.PokedexStatus_PokedexStatus_CaughtShiny, // 妙蛙花闪光
		4:   MainServer.PokedexStatus_PokedexStatus_Caught,      // 小火龙
		5:   MainServer.PokedexStatus_PokedexStatus_Seen,        // 火恐龙
		6:   MainServer.PokedexStatus_PokedexStatus_Caught,      // 喷火龙
		25:  MainServer.PokedexStatus_PokedexStatus_CaughtShiny, // 皮卡丘闪光
		26:  MainServer.PokedexStatus_PokedexStatus_Caught,      // 雷丘
		150: MainServer.PokedexStatus_PokedexStatus_Seen,        // 超梦
	}

	// 从状态映射创建图鉴
	pokebook, err := CreatePokebookFromStatusMap(testTid, statusMap)
	if err != nil {
		fmt.Printf("✗ 创建图鉴失败: %v\n", err)
		return
	}

	fmt.Printf("✓ 批量创建图鉴成功，训练师ID: %d\n", pokebook.Tid)

	// 验证状态
	fmt.Println("验证批量设置的状态:")
	for pokemonId, expectedStatus := range statusMap {
		actualStatus, err := GetPokemonStatus(pokebook.PokedexData, pokemonId)
		if err != nil {
			fmt.Printf("✗ 获取精灵%d状态失败: %v\n", pokemonId, err)
			continue
		}

		if actualStatus == expectedStatus {
			fmt.Printf("✓ 精灵%d: %s\n", pokemonId, getStatusDescription(actualStatus))
		} else {
			fmt.Printf("✗ 精灵%d: 期望%s，实际%s\n", pokemonId,
				getStatusDescription(expectedStatus), getStatusDescription(actualStatus))
		}
	}

	// 转换为状态映射
	resultMap, err := ConvertPokebookToStatusMap(pokebook)
	if err != nil {
		fmt.Printf("✗ 转换为状态映射失败: %v\n", err)
		return
	}

	// 统计非默认状态的精灵
	nonDefaultCount := 0
	for _, status := range resultMap {
		if status != MainServer.PokedexStatus_PokedexStatus_NotSeen {
			nonDefaultCount++
		}
	}

	fmt.Printf("✓ 转换完成，共有%d只精灵有记录\n", nonDefaultCount)
	fmt.Println("=== 批量操作测试完成 ===")
}

// 测试数据导出导入
func TestDataExportImport() {
	fmt.Println("\n=== 数据导出导入测试 ===")

	// 创建测试数据
	testTid := int64(11111)
	statusMap := map[int32]MainServer.PokedexStatus{
		1:   MainServer.PokedexStatus_PokedexStatus_Caught,
		25:  MainServer.PokedexStatus_PokedexStatus_CaughtShiny,
		150: MainServer.PokedexStatus_PokedexStatus_Seen,
	}

	pokebook, err := CreatePokebookFromStatusMap(testTid, statusMap)
	if err != nil {
		fmt.Printf("✗ 创建测试图鉴失败: %v\n", err)
		return
	}

	// 导出为JSON
	jsonData, err := ExportPokebookToJSON(pokebook)
	if err != nil {
		fmt.Printf("✗ 导出JSON失败: %v\n", err)
		return
	}

	fmt.Printf("✓ 导出JSON成功，包含%d个字段\n", len(jsonData))

	// 导入JSON
	importedPokebook, err := ImportPokebookFromJSON(jsonData)
	if err != nil {
		fmt.Printf("✗ 导入JSON失败: %v\n", err)
		return
	}

	// 验证导入的数据
	if importedPokebook.Tid == pokebook.Tid {
		fmt.Printf("✓ 训练师ID匹配: %d\n", importedPokebook.Tid)
	} else {
		fmt.Printf("✗ 训练师ID不匹配: 期望%d，实际%d\n", pokebook.Tid, importedPokebook.Tid)
	}

	// 验证精灵状态
	for pokemonId, expectedStatus := range statusMap {
		actualStatus, err := GetPokemonStatus(importedPokebook.PokedexData, pokemonId)
		if err != nil {
			fmt.Printf("✗ 获取导入精灵%d状态失败: %v\n", pokemonId, err)
			continue
		}

		if actualStatus == expectedStatus {
			fmt.Printf("✓ 精灵%d状态匹配: %s\n", pokemonId, getStatusDescription(actualStatus))
		} else {
			fmt.Printf("✗ 精灵%d状态不匹配: 期望%s，实际%s\n", pokemonId,
				getStatusDescription(expectedStatus), getStatusDescription(actualStatus))
		}
	}

	fmt.Println("=== 数据导出导入测试完成 ===")
}

// 运行所有测试
func RunAllTests() {
	fmt.Println("开始运行精灵图鉴系统测试...")

	TestBasicPokebookOperations(nil)
	TestBatchOperations()
	TestDataExportImport()

	fmt.Println("\n所有测试完成！")
}
