package pokebook

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 临时RPC结构体定义，等待proto更新后替换
// type RpcSetPokemonStatusRequest struct {
// 	PokemonId int32 `json:"pokemon_id"`
// 	Status    int32 `json:"status"`
// }

// type RpcSetPokemonStatusResponse struct {
// 	Success bool   `json:"success"`
// 	Message string `json:"message"`
// }

// type PokemonStatusUpdate struct {
// 	PokemonId int32 `json:"pokemon_id"`
// 	Status    int32 `json:"status"`
// }

// type RpcBatchSetPokemonStatusRequest struct {
// 	Updates []PokemonStatusUpdate `json:"updates"`
// }

// type RpcBatchSetPokemonStatusResponse struct {
// 	Success     bool   `json:"success"`
// 	Message     string `json:"message"`
// 	UpdateCount int32  `json:"update_count"`
// }

// type RpcExtendPokebookCapacityRequest struct {
// 	NewTotalCount int32 `json:"new_total_count"`
// }

// type RpcExtendPokebookCapacityResponse struct {
// 	Success       bool   `json:"success"`
// 	Message       string `json:"message"`
// 	NewTotalCount int32  `json:"new_total_count"`
// }

// type RpcGetPokemonByStatusRequest struct {
// 	Status int32 `json:"status"`
// }

// type RpcGetPokemonByStatusResponse struct {
// 	PokemonIds []int32 `json:"pokemon_ids"`
// 	Count      int32   `json:"count"`
// }

// RPC: 获取训练师图鉴数据
func RpcGetTrainerPokebook(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取图鉴数据
	pokebook, err := GetTrainerPokebook(ctx, tx, trainer.Id)
	if err != nil {
		return "", fmt.Errorf("获取图鉴数据失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回JSON数据
	// resultBytes, err := json.Marshal(pokebook)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化图鉴数据失败: %w", err)
	// }
	return tool.ProtoToBase64(pokebook)
	// return string(resultBytes), nil
}

// RPC: 设置精灵状态
func RpcSetPokemonStatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.RpcSetPokemonStatusRequest
	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		return "", fmt.Errorf("解析请求参数失败: %w", err)
	}

	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 设置精灵状态
	status := MainServer.PokedexStatus(request.Status)
	err = SetPokemonStatus(ctx, tx, trainer.Id, request.PokemonId, status)
	if err != nil {
		return "", fmt.Errorf("设置精灵状态失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回成功结果
	result := &MainServer.RpcSetPokemonStatusResponse{
		Success: true,
		Message: "精灵状态设置成功",
	}
	return tool.ProtoToBase64(result)
	// resultBytes, err := json.Marshal(result)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化响应失败: %w", err)
	// }
	// return string(resultBytes), nil
}

// RPC: 批量设置精灵状态
func RpcBatchSetPokemonStatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.RpcBatchSetPokemonStatusRequest
	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		return "", fmt.Errorf("解析请求参数失败: %w", err)
	}

	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 转换状态更新
	updates := make(map[int32]MainServer.PokedexStatus)
	for _, update := range request.Updates {
		updates[update.PokemonId] = MainServer.PokedexStatus(update.Status)
	}

	// 批量设置精灵状态
	err = BatchSetPokemonStatus(ctx, tx, trainer.Id, updates)
	if err != nil {
		return "", fmt.Errorf("批量设置精灵状态失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回成功结果
	result := &MainServer.RpcBatchSetPokemonStatusResponse{
		Success:     true,
		Message:     "批量设置精灵状态成功",
		UpdateCount: int32(len(updates)),
	}
	return tool.ProtoToBase64(result)
	// resultBytes, err := json.Marshal(result)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化响应失败: %w", err)
	// }
	// return string(resultBytes), nil
}

// RPC: 获取图鉴统计信息
func RpcGetPokebookStats(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取统计信息
	stats, err := GetPokebookStats(ctx, tx, trainer.Id)
	if err != nil {
		return "", fmt.Errorf("获取图鉴统计失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}
	return tool.ProtoToBase64(stats)
	// 返回统计数据
	// resultBytes, err := json.Marshal(stats)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化统计数据失败: %w", err)
	// }
	// return string(resultBytes), nil
}

// RPC: 扩展图鉴容量
func RpcExtendPokebookCapacity(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.RpcExtendPokebookCapacityRequest
	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		return "", fmt.Errorf("解析请求参数失败: %w", err)
	}

	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 扩展容量
	err = ExtendPokebookCapacity(ctx, tx, trainer.Id, request.NewTotalCount)
	if err != nil {
		return "", fmt.Errorf("扩展图鉴容量失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回成功结果
	result := &MainServer.RpcExtendPokebookCapacityResponse{
		Success:       true,
		Message:       "图鉴容量扩展成功",
		NewTotalCount: request.NewTotalCount,
	}
	return tool.ProtoToBase64(result)
	// resultBytes, err := json.Marshal(result)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化响应失败: %w", err)
	// }
	// return string(resultBytes), nil
}

// RPC: 查询特定状态的精灵列表
func RpcGetPokemonByStatus(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	var request MainServer.RpcGetPokemonByStatusRequest
	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		return "", fmt.Errorf("解析请求参数失败: %w", err)
	}

	// 获取用户ID
	userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		return "", fmt.Errorf("无法获取用户ID")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userId)
	if trainer == nil {
		return "", fmt.Errorf("获取训练师信息失败")
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 查询精灵列表
	status := MainServer.PokedexStatus(request.Status)
	pokemonList, err := GetPokemonByStatus(ctx, tx, trainer.Id, status)
	if err != nil {
		return "", fmt.Errorf("查询精灵列表失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	// 返回结果
	result := &MainServer.RpcGetPokemonByStatusResponse{
		PokemonIds: pokemonList,
		Count:      int32(len(pokemonList)),
	}
	return tool.ProtoToBase64(result)
	// resultBytes, err := json.Marshal(result)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化响应失败: %w", err)
	// }
	// return string(resultBytes), nil
}

// 管理员RPC: 重置训练师图鉴
// func RpcAdminResetTrainerPokebook(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 解析请求参数
// 	var params map[string]interface{}
// 	err := json.Unmarshal([]byte(payload), &params)
// 	if err != nil {
// 		return "", fmt.Errorf("解析参数失败: %w", err)
// 	}

// 	// 获取目标训练师ID
// 	tidStr, ok := params["tid"].(string)
// 	if !ok {
// 		return "", fmt.Errorf("缺少训练师ID参数")
// 	}

// 	tid, err := strconv.ParseInt(tidStr, 10, 64)
// 	if err != nil {
// 		return "", fmt.Errorf("训练师ID格式错误: %w", err)
// 	}

// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", fmt.Errorf("开始事务失败: %w", err)
// 	}
// 	defer tx.Rollback()

// 	// 重置图鉴
// 	err = ResetTrainerPokebook(ctx, tx, tid)
// 	if err != nil {
// 		return "", fmt.Errorf("重置图鉴失败: %w", err)
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		return "", fmt.Errorf("提交事务失败: %w", err)
// 	}

// 	// 返回成功结果
// 	result := map[string]interface{}{
// 		"success": true,
// 		"message": fmt.Sprintf("训练师%d的图鉴重置成功", tid),
// 	}

// 	resultBytes, _ := json.Marshal(result)
// 	return string(resultBytes), nil
// }
