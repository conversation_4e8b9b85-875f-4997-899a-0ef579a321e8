# Pokebook Proto Definitions

以下是精灵图鉴系统需要的Protocol Buffer定义，请在另一个项目中手动添加到MainServer.proto文件中：

## 枚举定义

```protobuf
// 精灵图鉴状态枚举
enum PokedexStatus {
    MainServer.PokedexStatus_PokedexStatus_NotSeen = 0;      // 00 - 未遇见过
    MainServer.PokedexStatus_PokedexStatus_Seen = 1;         // 01 - 遇见过  
    MainServer.PokedexStatus_PokedexStatus_Caught = 2;       // 10 - 捕捉过
    MainServer.PokedexStatus_PokedexStatus_CaughtShiny = 3;  // 11 - 捕捉过闪光
}
```

## 消息定义

```protobuf
// 训练师图鉴数据
message TrainerPokebook {
    int64 id = 1;                    // 数据库ID
    int64 tid = 2;                   // 训练师ID
    bytes pokedex_data = 3;          // 图鉴数据（每只精灵2bit）
    int32 total_pokemon_count = 4;   // 总精灵数量
    int64 create_ts = 5;             // 创建时间戳
    int64 update_ts = 6;             // 更新时间戳
}

// 图鉴统计信息
message PokebookStats {
    int32 total_pokemon = 1;         // 总精灵数
    int32 seen_count = 2;            // 见过的精灵数
    int32 caught_count = 3;          // 捕捉的精灵数
    int32 shiny_count = 4;           // 闪光精灵数
    float completion_rate = 5;       // 完成度百分比
}

// 精灵状态更新
message PokemonStatusUpdate {
    int32 pokemon_id = 1;            // 精灵ID
    PokedexStatus status = 2;        // 新状态
}
```

## RPC请求和响应定义

```protobuf
// 设置精灵状态请求
message RpcSetPokemonStatusRequest {
    int32 pokemon_id = 1;            // 精灵ID
    PokedexStatus status = 2;        // 状态
}

// 设置精灵状态响应
message RpcSetPokemonStatusResponse {
    bool success = 1;                // 是否成功
    string message = 2;              // 消息
}

// 批量设置精灵状态请求
message RpcBatchSetPokemonStatusRequest {
    repeated PokemonStatusUpdate updates = 1;  // 状态更新列表
}

// 批量设置精灵状态响应
message RpcBatchSetPokemonStatusResponse {
    bool success = 1;                // 是否成功
    string message = 2;              // 消息
    int32 update_count = 3;          // 更新数量
}

// 扩展图鉴容量请求
message RpcExtendPokebookCapacityRequest {
    int32 new_total_count = 1;       // 新的总精灵数
}

// 扩展图鉴容量响应
message RpcExtendPokebookCapacityResponse {
    bool success = 1;                // 是否成功
    string message = 2;              // 消息
    int32 new_total_count = 3;       // 新的总精灵数
}

// 查询特定状态精灵请求
message RpcGetPokemonByStatusRequest {
    PokedexStatus status = 1;        // 要查询的状态
}

// 查询特定状态精灵响应
message RpcGetPokemonByStatusResponse {
    repeated int32 pokemon_ids = 1;  // 精灵ID列表
    int32 count = 2;                 // 数量
}
```

## 使用说明

### 数据存储格式
- 每只精灵使用2bit存储状态（00=未见过，01=见过，10=捕捉过，11=捕捉过闪光）
- 每个字节可以存储4只精灵的状态
- 精灵ID从1开始，数据按顺序存储

### 客户端解析（C#示例）
```csharp
public enum PokedexStatus
{
    NotSeen = 0,      // 00
    Seen = 1,         // 01  
    Caught = 2,       // 10
    CaughtShiny = 3   // 11
}

public static PokedexStatus GetPokemonStatus(byte[] pokedexData, int pokemonId)
{
    if (pokemonId < 1) return PokedexStatus.NotSeen;
    
    int index = pokemonId - 1;
    int byteIndex = index / 4;
    int bitOffset = (index % 4) * 2;
    
    if (byteIndex >= pokedexData.Length) return PokedexStatus.NotSeen;
    
    byte mask = (byte)(0x03 << bitOffset); // 0x03 = 00000011
    byte statusBits = (byte)((pokedexData[byteIndex] & mask) >> bitOffset);
    
    return (PokedexStatus)statusBits;
}

public static void SetPokemonStatus(byte[] pokedexData, int pokemonId, PokedexStatus status)
{
    if (pokemonId < 1) return;
    
    int index = pokemonId - 1;
    int byteIndex = index / 4;
    int bitOffset = (index % 4) * 2;
    
    if (byteIndex >= pokedexData.Length) return;
    
    byte mask = (byte)(0x03 << bitOffset);
    pokedexData[byteIndex] = (byte)((pokedexData[byteIndex] & ~mask) | ((byte)status << bitOffset));
}
```

### RPC调用示例
```csharp
// 获取图鉴数据
var pokebook = await CallRpc<TrainerPokebook>("RpcGetTrainerPokebook", "");

// 设置精灵状态
var request = new RpcSetPokemonStatusRequest
{
    PokemonId = 25, // 皮卡丘
    Status = PokedexStatus.Caught
};
await CallRpc<RpcSetPokemonStatusResponse>("RpcSetPokemonStatus", request);

// 获取统计信息
var stats = await CallRpc<PokebookStats>("RpcGetPokebookStats", "");
```

## 数据库表结构
```sql
CREATE TABLE pokebook (
    id BIGSERIAL PRIMARY KEY,
    tid BIGINT NOT NULL,
    pokedex_data BYTEA NOT NULL DEFAULT '\x',
    total_pokemon_count INT NOT NULL DEFAULT 1025,
    create_ts BIGINT NOT NULL,
    update_ts BIGINT NOT NULL,
    UNIQUE(tid)
);

CREATE INDEX idx_pokebook_tid ON pokebook (tid);
CREATE INDEX idx_pokebook_update_ts ON pokebook (update_ts);
```

## 性能优化建议
1. 图鉴数据使用BYTEA类型存储，节省空间
2. 每只精灵仅占用2bit，1025只精灵约需要257字节
3. 支持动态扩展精灵数量
4. 提供批量操作接口减少数据库交互
5. 客户端可以缓存图鉴数据，仅在有更新时同步
