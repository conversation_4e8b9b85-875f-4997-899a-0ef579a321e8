package pokebook

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 临时结构体定义，等待proto更新后替换为MainServer中的定义
// type TrainerPokebook struct {
// 	Id                int64  `json:"id"`
// 	Tid               int64  `json:"tid"`
// 	PokedexData       []byte `json:"pokedex_data"`
// 	TotalPokemonCount int32  `json:"total_pokemon_count"`
// 	CreateTs          int64  `json:"create_ts"`
// 	UpdateTs          int64  `json:"update_ts"`
// }

// type PokebookStats struct {
// 	TotalPokemon   int32   `json:"total_pokemon"`
// 	SeenCount      int32   `json:"seen_count"`
// 	CaughtCount    int32   `json:"caught_count"`
// 	ShinyCount     int32   `json:"shiny_count"`
// 	CompletionRate float32 `json:"completion_rate"`
// }

const (
	TablePokebookName = "pokebook"
)

// 精灵图鉴状态枚举
// type PokedexStatus int

// const (
// 	MainServer.PokedexStatus_PokedexStatus_NotSeen     PokedexStatus = 0 // 00 - 未遇见过
// 	MainServer.PokedexStatus_PokedexStatus_Seen        PokedexStatus = 1 // 01 - 遇见过
// 	MainServer.PokedexStatus_PokedexStatus_Caught      PokedexStatus = 2 // 10 - 捕捉过
// 	MainServer.PokedexStatus_PokedexStatus_CaughtShiny PokedexStatus = 3 // 11 - 捕捉过闪光
// )

// 初始化图鉴表
func InitPokebook(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createPokebookTableIfNotExists(ctx, logger, db)
}

// 创建图鉴表（如果不存在）
func createPokebookTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            pokedex_data BYTEA NOT NULL DEFAULT '\x',
            total_pokemon_count INT NOT NULL DEFAULT 1025,
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL,
            UNIQUE(tid)
        );
        CREATE INDEX IF NOT EXISTS idx_pokebook_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_pokebook_update_ts ON %s (update_ts);
    `, TablePokebookName, TablePokebookName, TablePokebookName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建图鉴表失败: %v", err)
		return
	}

	logger.Info("图鉴表初始化完成")
}

// 获取训练师的图鉴数据
func GetTrainerPokebook(ctx context.Context, tx *sql.Tx, tid int64) (*MainServer.TrainerPokebook, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, pokedex_data, total_pokemon_count, create_ts, update_ts
		FROM %s
		WHERE tid = $1
	`, TablePokebookName)

	pokebook := &MainServer.TrainerPokebook{}
	var pokedexData []byte

	err := tx.QueryRowContext(ctx, query, tid).Scan(
		&pokebook.Id,
		&pokebook.Tid,
		&pokedexData,
		&pokebook.TotalPokemonCount,
		&pokebook.CreateTs,
		&pokebook.UpdateTs,
	)

	if err == sql.ErrNoRows {
		// 如果不存在，创建新的图鉴记录
		return createNewPokebook(ctx, tx, tid)
	} else if err != nil {
		return nil, fmt.Errorf("查询图鉴失败: %w", err)
	}

	pokebook.PokedexData = pokedexData
	return pokebook, nil
}

// 创建新的图鉴记录
func createNewPokebook(ctx context.Context, tx *sql.Tx, tid int64) (*MainServer.TrainerPokebook, error) {
	now := time.Now().Unix()
	totalPokemonCount := int32(1025) // 默认1025只精灵

	// 计算需要的字节数（每只精灵2bit，所以每个字节可以存储4只精灵）
	bytesNeeded := (totalPokemonCount + 3) / 4 // 向上取整
	pokedexData := make([]byte, bytesNeeded)

	query := fmt.Sprintf(`
		INSERT INTO %s (tid, pokedex_data, total_pokemon_count, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id
	`, TablePokebookName)

	pokebook := &MainServer.TrainerPokebook{
		Tid:               tid,
		PokedexData:       pokedexData,
		TotalPokemonCount: totalPokemonCount,
		CreateTs:          now,
		UpdateTs:          now,
	}

	err := tx.QueryRowContext(ctx, query,
		pokebook.Tid,
		pokebook.PokedexData,
		pokebook.TotalPokemonCount,
		pokebook.CreateTs,
		pokebook.UpdateTs,
	).Scan(&pokebook.Id)

	if err != nil {
		return nil, fmt.Errorf("创建图鉴记录失败: %w", err)
	}

	return pokebook, nil
}

// 更新图鉴数据
func UpdatePokebook(ctx context.Context, tx *sql.Tx, pokebook *MainServer.TrainerPokebook) error {
	pokebook.UpdateTs = time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET
			pokedex_data = $2,
			total_pokemon_count = $3,
			update_ts = $4
		WHERE id = $1
	`, TablePokebookName)

	result, err := tx.ExecContext(ctx, query,
		pokebook.Id,
		pokebook.PokedexData,
		pokebook.TotalPokemonCount,
		pokebook.UpdateTs,
	)

	if err != nil {
		return fmt.Errorf("更新图鉴失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("图鉴记录不存在")
	}

	return nil
}

// 设置精灵状态
func SetPokemonStatus(ctx context.Context, tx *sql.Tx, tid int64, pokemonId int32, status MainServer.PokedexStatus) error {
	// 获取当前图鉴数据
	pokebook, err := GetTrainerPokebook(ctx, tx, tid)
	if err != nil {
		return err
	}

	// 检查精灵ID是否有效
	if pokemonId < 1 || pokemonId > pokebook.TotalPokemonCount {
		return fmt.Errorf("精灵ID无效: %d", pokemonId)
	}

	// 扩展数据如果需要
	err = ensureCapacity(pokebook, pokemonId)
	if err != nil {
		return err
	}

	// 设置状态
	err = setPokemonStatusInData(pokebook.PokedexData, pokemonId, status)
	if err != nil {
		return err
	}

	// 更新数据库
	return UpdatePokebook(ctx, tx, pokebook)
}

// 获取精灵状态
func GetPokemonStatus(pokedexData []byte, pokemonId int32) (MainServer.PokedexStatus, error) {
	if pokemonId < 1 {
		return MainServer.PokedexStatus_PokedexStatus_NotSeen, fmt.Errorf("精灵ID无效: %d", pokemonId)
	}

	// 计算字节位置和位偏移
	// pokemonId从1开始，所以需要减1
	index := pokemonId - 1
	byteIndex := index / 4
	bitOffset := (index % 4) * 2

	// 检查数据长度
	if int(byteIndex) >= len(pokedexData) {
		return MainServer.PokedexStatus_PokedexStatus_NotSeen, nil // 超出范围默认为未见过
	}

	// 提取2位状态
	mask := byte(0x03) << bitOffset // 0x03 = 00000011
	statusBits := (pokedexData[byteIndex] & mask) >> bitOffset

	return MainServer.PokedexStatus(statusBits), nil
}

// 在数据中设置精灵状态
func setPokemonStatusInData(pokedexData []byte, pokemonId int32, status MainServer.PokedexStatus) error {
	if pokemonId < 1 {
		return fmt.Errorf("精灵ID无效: %d", pokemonId)
	}

	// 计算字节位置和位偏移
	index := pokemonId - 1
	byteIndex := index / 4
	bitOffset := (index % 4) * 2

	// 检查数据长度
	if int(byteIndex) >= len(pokedexData) {
		return fmt.Errorf("数据长度不足")
	}

	// 清除原有状态并设置新状态
	mask := byte(0x03) << bitOffset // 0x03 = 00000011
	pokedexData[byteIndex] = (pokedexData[byteIndex] & ^mask) | (byte(status) << bitOffset)

	return nil
}

// 确保数据容量足够
func ensureCapacity(pokebook *MainServer.TrainerPokebook, pokemonId int32) error {
	// 如果精灵ID超出当前总数，扩展总数
	if pokemonId > pokebook.TotalPokemonCount {
		pokebook.TotalPokemonCount = pokemonId
	}

	// 计算需要的字节数
	bytesNeeded := (pokebook.TotalPokemonCount + 3) / 4

	// 如果当前数据长度不够，扩展数据
	if int32(len(pokebook.PokedexData)) < bytesNeeded {
		newData := make([]byte, bytesNeeded)
		copy(newData, pokebook.PokedexData)
		pokebook.PokedexData = newData
	}

	return nil
}

// 批量设置精灵状态
func BatchSetPokemonStatus(ctx context.Context, tx *sql.Tx, tid int64, updates map[int32]MainServer.PokedexStatus) error {
	// 获取当前图鉴数据
	pokebook, err := GetTrainerPokebook(ctx, tx, tid)
	if err != nil {
		return err
	}

	// 找到最大的精灵ID
	maxPokemonId := pokebook.TotalPokemonCount
	for pokemonId := range updates {
		if pokemonId > maxPokemonId {
			maxPokemonId = pokemonId
		}
	}

	// 确保容量足够
	err = ensureCapacity(pokebook, maxPokemonId)
	if err != nil {
		return err
	}

	// 批量设置状态
	for pokemonId, status := range updates {
		if pokemonId < 1 {
			continue // 跳过无效ID
		}
		err = setPokemonStatusInData(pokebook.PokedexData, pokemonId, status)
		if err != nil {
			return fmt.Errorf("设置精灵%d状态失败: %w", pokemonId, err)
		}
	}

	// 更新数据库
	return UpdatePokebook(ctx, tx, pokebook)
}

// 扩展图鉴容量（当有新精灵添加时）
func ExtendPokebookCapacity(ctx context.Context, tx *sql.Tx, tid int64, newTotalCount int32) error {
	pokebook, err := GetTrainerPokebook(ctx, tx, tid)
	if err != nil {
		return err
	}

	if newTotalCount <= pokebook.TotalPokemonCount {
		return nil // 不需要扩展
	}

	// 扩展容量
	err = ensureCapacity(pokebook, newTotalCount)
	if err != nil {
		return err
	}

	return UpdatePokebook(ctx, tx, pokebook)
}

// 获取图鉴统计信息
func GetPokebookStats(ctx context.Context, tx *sql.Tx, tid int64) (*MainServer.PokebookStats, error) {
	pokebook, err := GetTrainerPokebook(ctx, tx, tid)
	if err != nil {
		return nil, err
	}

	stats := &MainServer.PokebookStats{
		TotalPokemon:   pokebook.TotalPokemonCount,
		SeenCount:      0,
		CaughtCount:    0,
		ShinyCount:     0,
		CompletionRate: 0.0,
	}

	// 统计各种状态的精灵数量
	for i := int32(1); i <= pokebook.TotalPokemonCount; i++ {
		status, err := GetPokemonStatus(pokebook.PokedexData, i)
		if err != nil {
			continue
		}
		switch status {
		case MainServer.PokedexStatus_PokedexStatus_Seen:
			stats.SeenCount++
		case MainServer.PokedexStatus_PokedexStatus_Caught:
			stats.CaughtCount++
		case MainServer.PokedexStatus_PokedexStatus_CaughtShiny:
			stats.ShinyCount++
			stats.CaughtCount++ // 闪光也算捕捉
		}
	}

	// 计算完成度（见过的精灵数 / 总精灵数）
	if stats.TotalPokemon > 0 {
		seenTotal := stats.SeenCount + stats.CaughtCount
		stats.CompletionRate = float32(seenTotal) / float32(stats.TotalPokemon) * 100
	}

	return stats, nil
}

// 查询特定状态的精灵列表
func GetPokemonByStatus(ctx context.Context, tx *sql.Tx, tid int64, status MainServer.PokedexStatus) ([]int32, error) {
	pokebook, err := GetTrainerPokebook(ctx, tx, tid)
	if err != nil {
		return nil, err
	}

	var pokemonList []int32

	for i := int32(1); i <= pokebook.TotalPokemonCount; i++ {
		pokemonStatus, err := GetPokemonStatus(pokebook.PokedexData, i)
		if err != nil {
			continue
		}

		if pokemonStatus == status {
			pokemonList = append(pokemonList, i)
		}
	}

	return pokemonList, nil
}

// 检查精灵是否已见过（任何状态除了NotSeen）
func IsPokemonSeen(pokedexData []byte, pokemonId int32) bool {
	status, err := GetPokemonStatus(pokedexData, pokemonId)
	if err != nil {
		return false
	}
	return status != MainServer.PokedexStatus_PokedexStatus_NotSeen
}

// 检查精灵是否已捕捉（包括普通和闪光）
func IsPokemonCaught(pokedexData []byte, pokemonId int32) bool {
	status, err := GetPokemonStatus(pokedexData, pokemonId)
	if err != nil {
		return false
	}
	return status == MainServer.PokedexStatus_PokedexStatus_Caught || status == MainServer.PokedexStatus_PokedexStatus_CaughtShiny
}

// 检查精灵是否为闪光
func IsPokemonShiny(pokedexData []byte, pokemonId int32) bool {
	status, err := GetPokemonStatus(pokedexData, pokemonId)
	if err != nil {
		return false
	}
	return status == MainServer.PokedexStatus_PokedexStatus_CaughtShiny
}

// 获取所有训练师的图鉴数据（管理员用）
func GetAllPokebooks(ctx context.Context, tx *sql.Tx, updateTs int64) ([]*MainServer.TrainerPokebook, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, pokedex_data, total_pokemon_count, create_ts, update_ts
		FROM %s
		WHERE update_ts > $1
		ORDER BY update_ts DESC
	`, TablePokebookName)

	rows, err := tx.QueryContext(ctx, query, updateTs)
	if err != nil {
		return nil, fmt.Errorf("查询图鉴失败: %w", err)
	}
	defer rows.Close()

	var pokebooks []*MainServer.TrainerPokebook
	for rows.Next() {
		pokebook := &MainServer.TrainerPokebook{}
		var pokedexData []byte

		err := rows.Scan(
			&pokebook.Id,
			&pokebook.Tid,
			&pokedexData,
			&pokebook.TotalPokemonCount,
			&pokebook.CreateTs,
			&pokebook.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描图鉴数据失败: %w", err)
		}

		pokebook.PokedexData = pokedexData
		pokebooks = append(pokebooks, pokebook)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历图鉴数据失败: %w", err)
	}

	return pokebooks, nil
}

// 重置训练师图鉴（管理员用）
func ResetTrainerPokebook(ctx context.Context, tx *sql.Tx, tid int64) error {
	query := fmt.Sprintf(`DELETE FROM %s WHERE tid = $1`, TablePokebookName)

	result, err := tx.ExecContext(ctx, query, tid)
	if err != nil {
		return fmt.Errorf("重置图鉴失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("图鉴记录不存在")
	}

	return nil
}
