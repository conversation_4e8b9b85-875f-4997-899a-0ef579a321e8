package hook

import (
	"context"
	"go-nakama-poke/tool"
	"time"

	"github.com/heroiclabs/nakama-common/api"
	"github.com/heroiclabs/nakama-common/runtime"
)

func HookEvent(initializer runtime.Initializer) {
	hookPartyMethod(initializer)
	hookMatchMethod(initializer)
	initializer.RegisterEventSessionStart(eventSessionStart)
	initializer.RegisterEventSessionEnd(eventSessionEnd)
}

func eventSessionEnd(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	// CleanupOldEntries()
	if trainer != nil {
		trainer.SessionInfo.SessionEndTs = time.Now().UnixMilli()
		if trainer.SessionInfo.LocInfo != nil && trainer.SessionInfo.LocInfo.Loc != nil {
			tool.GlobalAOIManager.RemovePlayer(trainer.SessionInfo.LocInfo.Loc.MainLandType, trainer.Id)
		}
	}
	logger.Debug("process event session end: %+v", evt)
}

func eventSessionStart(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	// trainer := tool.GetActiveTrainerByCtx(ctx)
	// if trainer != nil && trainer.SessionInfo.SessionEndTs > 0 {
	// 	if time.Now().UnixMilli()-trainer.SessionInfo.SessionEndTs > config.ReconnectTime {
	// 		tool.RemoveUserActiveTrainer(ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string))
	// 	}
	// 	trainer.SessionInfo.SessionEndTs = 0
	// }
	logger.Debug("process event session start: %+v", evt)
}
