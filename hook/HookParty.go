package hook

import (
	"go-nakama-poke/nconst"
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/rtapi"
	"github.com/heroiclabs/nakama-common/runtime"
)

func hookPartyMethod(initializer runtime.Initializer) {
	// initializer.RegisterBeforeRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	// 	envelope, ok := in.Message.(*rtapi.Envelope_PartyCreate)
	// 	if !ok {
	// 		return nil, runtime.NewError("error getting envelope as PartyCreate envelope", 13)
	// 	}
	// 	// userId := envelope.Party.Self.UserId
	// 	// partyId := envelope.Party.PartyId
	// 	// updatePatyInfo(userId, partyId, envelope.Party.Leader, envelope.Party.Presences)
	// 	return in, nil
	// })
	hookNames := []string{"PartyCreate", "PartyJoin", "PartyLeave", "PartyPromote", "PartyAccept", "PartyRemove", "PartyClose"}
	for i := range hookNames {
		initializer.RegisterAfterRt(hookNames[i], func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *rtapi.Envelope, in *rtapi.Envelope) error {
			// if hookNames[i] == "PartyCreate" {

			// }
			trainer := tool.GetActiveTrainerByCtx(ctx)
			if trainer == nil {
				return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
			}
			// ok := false
			if hookNames[i] == "PartyClose" {
				tool.GetGlobalPartyMap().RemovePartyInfo(trainer.Id)
				return nil
			}
			// envelope := &rtapi.Envelope_Party{}
			if hookNames[i] == "PartyCreate" {
				// _, ok := in.Message.(*rtapi.Envelope_PartyCreate)
				// if !ok {
				// 	return runtime.NewError("error getting envelope as PartyCreate envelope", 13)
				// }
				outEnvelope, ok := out.Message.(*rtapi.Envelope_Party)
				if !ok {
					return runtime.NewError("error getting envelope as PartyCreate envelope", 13)
				}
				CreatePartyInfo(trainer.Id, outEnvelope.Party.PartyId, outEnvelope.Party.Leader, outEnvelope.Party.Presences)
			} else if hookNames[i] == "PartyLeave" {
				// envelope, ok := in.Message.(*rtapi.Envelope_PartyLeave)
				// if !ok {
				// 	return runtime.NewError("error getting envelope as PartyLeave envelope", 13)
				// }
				leavePartyInfo(trainer.Id)
			} else if hookNames[i] == "PartyAccept" {
				envelope, ok := in.Message.(*rtapi.Envelope_PartyAccept)
				if !ok {
					return runtime.NewError("error getting envelope as PartyAccept envelope", 13)
				}
				acceptPartyInfo(trainer.Id, envelope.PartyAccept.Presence)
			} else if hookNames[i] == "PartyPromote" {
				envelope, ok := in.Message.(*rtapi.Envelope_PartyPromote)
				if !ok {
					return runtime.NewError("error getting envelope as PartyPromote envelope", 13)
				}
				promotePartyInfo(trainer.Id, envelope.PartyPromote.Presence)
			}
			// else if hookNames[i] == "PartyJoin" {
			// 	envelope, ok := in.Message.(*rtapi.Envelope_PartyJoin)
			// 	if !ok {
			// 		return runtime.NewError("error getting envelope as PartyJoin envelope", 13)
			// 	}
			// 	addPartyInfo(trainer.Id, envelope.PartyJoin.)
			// }
			// else if hookNames[i] == "PartyLeave" {
			// 	envelope, ok := in.Message.(*rtapi.Envelope_PartyLeave)
			// } else if hookNames[i] == "PartyRemove" {
			// 	envelope, ok := in.Message.(*rtapi.Envelope_PartyRemove)

			// } else if hookNames[i] == "PartyPromote" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyPromote)
			// } else if hookNames[i] == "PartyAccept" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyAccept)
			// }
			// if !ok {
			// 	return runtime.NewError("error getting envelope as "+hookNames[i]+" envelope", 13)
			// }
			// envelope, ok := out.Message.(*rtapi.Envelope_Party)
			// if !ok {
			// 	// if hookNames[i] == "PartyClose" || hookNames[i] == "PartyLeave" {
			// 	// 	tool.GetGlobalPartyMap().RemovePartyInfo(trainer.Id)
			// 	// 	return nil
			// 	// }
			// 	return runtime.NewError("error getting envelope as "+hookNames[i]+" envelope", 13)
			// }
			// partyId := envelope.Party.PartyId
			// updatePatyInfo(trainer.Id, partyId, envelope.Party.Leader, envelope.Party.Presences)
			//
			return nil
		})
	}
	// initializer.RegisterAfterRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {

	// initializer.RegisterAfterRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out, in *rtapi.Envelope) error {
	// 	envelope, ok := in.Message.(*rtapi.Envelope_Party)
	// 	if !ok {
	// 		return runtime.NewError("error getting envelope as PartyCreate envelope", 13)
	// 	}
	// 	userId := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 	partyId := envelope.Party.PartyId
	// 	updatePatyInfo(userId, partyId, envelope.Party.Leader, envelope.Party.Presences)
	// 	return nil
	// })

	// initializer.RegisterBeforeAddFriends(func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.CreateGroupRequest) (*api.CreateGroupRequest, error) {
	// 	//if containsProfanity(in.Name) {
	// 	//   return nil, runtime.NewError("profanity detected", 3)
	// 	// }

	// 	r eturn in, nil
	// })
}
func promotePartyInfo(tid int64, presence *rtapi.UserPresence) {
	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
	if !exists {
		return
	}

	leader := tool.GetActiveTrainerByUid(presence.UserId)
	if leader == nil {
		return
	}
	partyInfo.Leader = leader
	UpdateTrainerPartyInfo(partyInfo)
}
func acceptPartyInfo(tid int64, presence *rtapi.UserPresence) {
	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
	if !exists {
		return
	}
	trainer := tool.GetActiveTrainerByUid(presence.UserId)
	if trainer == nil {
		return
	}
	partyInfo.Trainers[trainer.Id] = trainer
	partyInfo.TidMax_Index += 1
	partyInfo.TrainersIndexMap[trainer.Id] = partyInfo.TidMax_Index
	// for i := range presences {
	// 	// Uids = append(Uids, presences[i].UserId)
	// 	trainer := tool.GetActiveTrainerByUid(presences[i].UserId)
	// 	if trainer != nil {
	// 		partyInfo.Trainers[trainer.Id] = trainer
	// 	}
	// }
	UpdateTrainerPartyInfo(partyInfo)
	// tool.GetGlobalPartyMap().Set(tid, partyInfo)
}
func leavePartyInfo(tid int64) {
	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
	if !exists {
		return
	}
	delete(partyInfo.Trainers, tid)
	tool.GetGlobalPartyMap().Remove(tid)
	// for i := range presences {
	// 	for _, v := range partyInfo.Trainers {
	// 		if v.Uid == presences[i].UserId {
	// 			delete(partyInfo.Trainers, v.Id)
	// 		}
	// 	}
	// 	// Uids = append(Uids, presences[i].UserId)
	// 	// trainer := tool.GetActiveTrainerByUid(presences[i].UserId)
	// 	// if trainer != nil {
	// 	// 	delete(partyInfo.Trainers, trainer.Id)
	// 	// 	// partyInfo.Trainers[trainer.Id] = trainer
	// 	// }
	// }
	UpdateTrainerPartyInfo(partyInfo)
	// tool.GetGlobalPartyMap().Set(tid, partyInfo)
}
func UpdateTrainerPartyInfo(partyInfo *MainServer.PartyInfo) {
	if len(partyInfo.Trainers) == 0 {
		tool.GetGlobalPartyMap().RemovePartyInfo(partyInfo.Leader.Id)
		return
	}
	for _, v := range partyInfo.Trainers {
		tool.GetGlobalPartyMap().Set(v.Id, partyInfo)
	}
}
func CreatePartyInfo(tid int64, partyId string, leader *rtapi.UserPresence, presences []*rtapi.UserPresence) {
	leaderTrainer := tool.GetActiveTrainerByUid(leader.UserId)
	if leaderTrainer == nil {
		return
	}
	partyInfo := &MainServer.PartyInfo{
		Trainers:         make(map[int64]*MainServer.Trainer),
		PartyId:          partyId,
		Leader:           tool.GetActiveTrainerByUid(leader.UserId),
		TrainersIndexMap: make(map[int64]int32),
	}
	for i := range presences {
		trainer := tool.GetActiveTrainerByUid(presences[i].UserId)
		if trainer != nil {
			partyInfo.Trainers[trainer.Id] = trainer
			partyInfo.TidMax_Index += 1
			partyInfo.TrainersIndexMap[trainer.Id] = partyInfo.TidMax_Index
		}
	}
	partyInfo.TidMax_Index = int32(len(partyInfo.Trainers))
	UpdateTrainerPartyInfo(partyInfo)
}
func ClosePartyInfo(tid int64, partyId string) {
	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
	if !exists {
		return
	}
	for _, v := range partyInfo.Trainers {
		tool.GetGlobalPartyMap().Remove(v.Id)
	}
}

// func updatePatyInfo(tid int64, partyId string, leader *rtapi.UserPresence, presences []*rtapi.UserPresence) {
// 	if len(presences) <= 0 { //party没人就直接删除
// 		tool.GetGlobalPartyMap().Remove(tid)
// 		return
// 	}
// 	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
// 	if !exists {
// 		partyInfo = &MainServer.PartyInfo{
// 			Trainers: make(map[int64]*MainServer.Trainer),
// 		}
// 	}
// 	//  else if partyInfo.Leader.Uid == userId &&  {

// 	// }
// 	// Uids := []string{}
// 	// trainers := []*MainServer.Trainer{}
// 	for i := range presences {
// 		// Uids = append(Uids, presences[i].UserId)
// 		trainer := tool.GetActiveTrainerByUid(presences[i].UserId)
// 		if trainer != nil {
// 			partyInfo.Trainers[trainer.Id] = trainer
// 			// trainers = append(trainers, trainer)
// 		}
// 	}
// 	// partyInfo.Trainers = trainers
// 	partyInfo.PartyId = partyId
// 	partyInfo.Leader = tool.GetActiveTrainerByUid(leader.UserId)
// 	tool.GetGlobalPartyMap().Set(tid, partyInfo)
// }
