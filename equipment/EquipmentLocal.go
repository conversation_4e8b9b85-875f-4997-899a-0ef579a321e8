package equipment

import (
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"log"
	"math/rand"
	"os"

	"google.golang.org/protobuf/proto"
)

var equipmentConfigMap map[string]*MainServer.EquipmentConfigInfo

// LoadPokemonInfos 加载 Pokedex 数据到全局变量 pokemonInfos
func LoadEquipmentConfigInfos() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/EquipmentConfig.bytes")
	if err != nil {
		log.Fatalf("Failed to read EquipmentConfig.bytes file: %v", err)
	}
	var equipmenList MainServer.EquipmentList
	if err := proto.Unmarshal(data, &equipmenList); err != nil {
		log.Fatalf("Failed to parse proto: %v", err)
	}
	equipmentConfigMap = equipmenList.EquipmentMap
	// 解析 JSON 数据直接到全局变量
	// if err := json.Unmarshal(data, &pokemonInfos); err != nil {
	// 	log.Fatalf("Failed to parse pokedex.json: %v", err)
	// }

	log.Printf("Successfully loaded %d Equipment entries into equipmentConfigMap.", len(equipmentConfigMap))
}
func getLocalEquipmentConfig(equipmentName string) (*MainServer.EquipmentConfigInfo, bool) {
	config, exists := equipmentConfigMap[equipmentName]
	return config, exists
}
func GenerateEquipmentInfo(equipmentName string) (*MainServer.Equipment, error) {
	config, exists := getLocalEquipmentConfig(equipmentName)
	if !exists {
		return nil, fmt.Errorf("equipment config not found: %s", equipmentName)
	}
	// 创建 Equipment 结构体
	equipment := &MainServer.Equipment{
		EquipmentName: config.EquipmentName,
		ExpiredTs:     config.ExpireTs,
		ItemSaleType:  config.ItemSaleType,
		EquipmentType: config.EquipmentType,
		TeamType:      config.TeamType,
		Status:        MainServer.EquipmentStatus_EquipmentStatus_Normal,
		FortifyCount:  0,
		EffectInfo:    &MainServer.TrainerEquipmentEffect{},
	}
	//
	results := generateLimitedRewardGroup(config.OneEquipments, 1)
	results = append(results, generateLimitedRewardGroup(config.TwoEquipments, 2)...)
	results = append(results, generateLimitedRewardGroup(config.ThreeEquipments, 3)...)
	results = append(results, config.AllEquipments...)
	for _, v := range results {
		equipment = GenerateEquipmentConfigValue(v, equipment)
	}

	return equipment, nil
}
func checkDropRate(dropRate float32) bool {
	if dropRate <= 0 {
		return false
	}
	if dropRate >= 100 {
		return true
	}

	// 生成0-99的随机数
	randomValue := rand.Intn(100)
	return randomValue < int(dropRate)
}
func generateLimitedRewardGroup(equipmentValues []*MainServer.EquipmentConfigValue, maxCount int) []*MainServer.EquipmentConfigValue {
	if len(equipmentValues) <= maxCount {
		return equipmentValues
	}
	// 随机选择指定数量的奖励
	var results []*MainServer.EquipmentConfigValue = make([]*MainServer.EquipmentConfigValue, 0)
	selectedIndices := make(map[int]bool)

	for len(results) < maxCount {
		index := rand.Intn(len(equipmentValues))
		if !selectedIndices[index] {
			selectedIndices[index] = true
			results = append(results, equipmentValues[index])
		}
	}

	return results
}
func GenerateEquipmentConfigValue(v *MainServer.EquipmentConfigValue, equipment *MainServer.Equipment) *MainServer.Equipment {
	if !checkDropRate(v.DropRate) {
		return equipment
	}
	if equipment.EffectInfo == nil {
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
	}
	if equipment.EffectInfo.EquipmentEffect == nil {
		equipment.EffectInfo.EquipmentEffect = make(map[int32]float32)
	}
	if equipment.EffectInfo.EquipmentPokeIvsEffect == nil {
		equipment.EffectInfo.EquipmentPokeIvsEffect = make(map[int32]float32)
	}
	if equipment.EffectInfo.EquipmentPokeShineEffect == nil {
		equipment.EffectInfo.EquipmentPokeShineEffect = make(map[int32]float32)
	}
	if v.EffectType != MainServer.TrainerEquipmentEffectType_EquipmentEffect_None {
		equipment.EffectInfo.EquipmentEffect[int32(v.EffectType)] = v.EffectTypeValue
	}
	if v.EffectPokeIvsType != MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Unknown {
		equipment.EffectInfo.EquipmentPokeIvsEffect[int32(v.EffectPokeIvsType)] = v.EffectPokeIvsTypeValue
	}
	if v.EffectPokeShineType != MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Unknown {
		equipment.EffectInfo.EquipmentPokeShineEffect[int32(v.EffectPokeShineType)] = v.EffectPokeShineTypeValue
	}
	// switch v.EffectType {
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterEnemy:
	// 	// equipment.EffectInfo.EncounterEnemy = v.EffectTypeValue
	// 	equipment.EffectInfo.EquipmentEffect[int32(v.EffectType)] = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterShine:
	// 	equipment.EffectInfo.EncounterShine = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedGoodPokebaby:
	// 	equipment.EffectInfo.BreedGoodPokebaby = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedDifficultBaby:
	// 	equipment.EffectInfo.BreedDifficultBaby = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BorrowGoodPoke:
	// 	equipment.EffectInfo.BorrowGoodPoke = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingGoodEquipment:
	// 	equipment.EffectInfo.FishingGoodEquipment = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingGoodPoke:
	// 	equipment.EffectInfo.FishingGoodPoke = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy:
	// 	equipment.EffectInfo.EncounterGoodEnemy = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattlePoke:
	// 	equipment.EffectInfo.GetBattlePoke = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleEquipment:
	// 	equipment.EffectInfo.GetBattleEquipment = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_TreeFruitGrowup:
	// 	equipment.EffectInfo.TreeFruitGrowup = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_TreeFruitWithered:
	// 	equipment.EffectInfo.TreeFruitWithered = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_HatchEggTime:
	// 	equipment.EffectInfo.HatchEggTime = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingBattleEquipment:
	// 	equipment.EffectInfo.FishingBattleEquipment = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleExp:
	// 	equipment.EffectInfo.GetBattleExp = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleMoney:
	// 	equipment.EffectInfo.GetBattleMoney = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleEvs:
	// 	equipment.EffectInfo.GetBattleEvs = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedShine:
	// 	equipment.EffectInfo.BreedShine = v.EffectTypeValue
	// 	break
	// }

	// switch v.EffectPokeType {
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Normal:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeNormal = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fire:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeFire = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Water:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeWater = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Electric:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeElectric = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Grass:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeGrass = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ice:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeIce = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fighting:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeFighting = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Poison:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokePoison = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ground:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeGround = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Flying:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeFlying = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Psychic:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokePsychic = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Bug:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeBug = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Rock:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeRock = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ghost:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeGhost = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dragon:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeDragon = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dark:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeDark = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Steel:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeSteel = v.EffectTypeValue
	// 	break
	// case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fairy:
	// 	equipment.EffectInfo.EffectPokeTypeInfo.EffectPokeFairy = v.EffectTypeValue
	// 	break
	// }
	return equipment
}
